export interface IUserRole {
  roleId: string; // Este é o ID da relação (owner_role_relation.id) que deve ser usado para vinculações
  roleName: string;
  partPercent: string;
}

export interface IUserInfo {
  id: string;
  name: string;
  email: string;
  document: string;
  type: 'owner' | 'business';
  roles: IUserRole[];
}

export interface IAuthResponse {
  accessToken: string;
  refreshToken?: string;
  user: IUserInfo;
}
