import { ArrowRightIcon } from "lucide-react";

export interface IProps {
  title: string;
  value: string;
  onClick?: () => void;
}

export default function CardUpgradeOpt({ title, value, onClick }: IProps) {
  return (
    <div
      className="flex md:flex-row flex-col gap-2 justify-between cursor-pointer"
      onClick={onClick}
    >
      <div className="bg-[#1C1C1C] md:w-[100%] p-5 rounded-lg border-[#FF9900] border my-5">
        <div className="flex">
          <div className="">
            <p className="ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4">
              {title}
            </p>
            <p className="ml-3 font-regular weight-400 text-[15px] lh-[18px]">
              {value}
            </p>
          </div>
          <div className="flex-1" />
          <div className="flex items-center">
            <div className="">
              <p className="text-white text-lg font-bold">
                <ArrowRightIcon color="#fff" width={20}></ArrowRightIcon>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
