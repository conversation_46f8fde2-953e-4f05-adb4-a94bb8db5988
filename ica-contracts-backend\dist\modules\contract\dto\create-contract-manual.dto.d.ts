export declare enum PersonType {
    PF = "PF",
    PJ = "PJ"
}
export declare enum Role {
    broker = "broker",
    advisor = "advisor"
}
export declare enum ContractType {
    SCP = "SCP",
    MUTUO = "MUTUO"
}
export declare enum PaymentMethod {
    PIX = "pix",
    BANK_TRANSFER = "bank_transfer",
    BOLETO = "boleto"
}
export declare enum InvestorProfile {
    CONSERVATIVE = "conservative",
    MODERATE = "moderate",
    AGGRESSIVE = "aggressive"
}
export declare enum CompanyLegalType {
    MEI = "MEI",
    EI = "EI",
    EIRELI = "EIRELI",
    LTDA = "LTDA",
    SLU = "SLU",
    SA = "SA",
    SS = "SS",
    CONSORCIO = "CONSORCIO"
}
export declare class AddressDto {
    street: string;
    city: string;
    state: string;
    neighborhood: string;
    postalCode: string;
    number: string;
    complement?: string;
}
export declare class IndividualDto {
    fullName: string;
    cpf: string;
    rg: string;
    issuingAgency: string;
    nationality: string;
    occupation: string;
    birthDate: string;
    email: string;
    phone: string;
    motherName: string;
    address: AddressDto;
}
export declare class CompanyDto {
    corporateName: string;
    cnpj: string;
    type: CompanyLegalType;
    address: AddressDto;
    representative: IndividualDto;
}
export declare class InvestmentDetailsDto {
    amount: number;
    monthlyRate: number;
    durationInMonths: number;
    paymentMethod: PaymentMethod;
    endDate: string;
    profile: InvestorProfile;
    quotaQuantity?: number;
    isDebenture?: boolean;
}
export declare class AdvisorAssignmentDto {
    advisorId: string;
    rate: number;
}
export declare class BankAccountDto {
    bank: string;
    agency: string;
    account: string;
    pix?: string;
}
export declare class CreateNewContractDto {
    role: Role;
    personType: PersonType;
    contractType: ContractType;
    advisors: AdvisorAssignmentDto[];
    investment: InvestmentDetailsDto;
    bankAccount: BankAccountDto;
    individual?: IndividualDto;
    company?: CompanyDto;
    isUpgrade?: boolean;
    originalContractId?: string;
    upgradeType?: string;
}
