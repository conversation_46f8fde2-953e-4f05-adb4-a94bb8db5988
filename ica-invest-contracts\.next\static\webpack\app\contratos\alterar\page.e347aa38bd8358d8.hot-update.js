"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-multiple\", \"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            // ✅ NOVA LÓGICA: Se há mensagem de complemento (depósito adicional),\n            // não validar múltiplo de 5000 pois o valor pode ser \"parcial\"\n            // A variável showComplementMessage não está disponível aqui no contexto do yup,\n            // então vamos pular essa validação específica e deixar o backend validar\n            // ou implementar a validação no onSubmit onde temos acesso ao estado\n            return numericValue > 0; // Apenas validar se é maior que 0\n        }\n        return true;\n    }).test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [irOpcaoSelecionada, setIrOpcaoSelecionada] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se uma opção de IR foi selecionada\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento) {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const cotas = Math.floor(valorNumerico / 5000);\n            setValue(\"quotaQuantity\", cotas.toString());\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 346,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando contrato anterior era MUTUO\n        if (data.modalidade === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            console.log(\"=== C\\xc1LCULO SCP COM DESCONTO IR ===\");\n            console.log(\"Valor original:\", valorNumerico);\n            console.log(\"IR a descontar:\", contractDetails.totalIR);\n            console.log(\"Valor ap\\xf3s desconto:\", valorAposDesconto);\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                // Arredondar para BAIXO (menos cotas) - pegar apenas cotas completas\n                const cotasCompletas = Math.floor(valorAposDesconto / 5000);\n                const valorCotasCompletas = cotasCompletas * 5000;\n                const valorComplementarNecessario = valorAposDesconto - valorCotasCompletas;\n                console.log(\"Resto:\", resto);\n                console.log(\"Cotas completas:\", cotasCompletas);\n                console.log(\"Valor das cotas completas:\", valorCotasCompletas);\n                console.log(\"Valor complementar necess\\xe1rio:\", valorComplementarNecessario);\n                // Atualizar o valor do investimento para o valor das cotas completas + IR\n                const novoValorInvestimento = valorCotasCompletas + contractDetails.totalIR;\n                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)((novoValorInvestimento * 100).toString());\n                setValue(\"valorInvestimento\", valorFormatado);\n                // Atualizar quantidade de cotas\n                setValue(\"quotaQuantity\", cotasCompletas.toString());\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"⚠️ Valor ajustado para \".concat(cotasCompletas, \" cotas completas. Valor complementar de \").concat(new Intl.NumberFormat(\"pt-BR\", {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }).format(valorComplementarNecessario), \" ser\\xe1 necess\\xe1rio.\"));\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR,\n        setValue\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, não precisa de IR\n            setIrCalculado(true);\n            setIrOpcaoSelecionada(true);\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setIrOpcaoSelecionada(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 911,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 914,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 935,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 946,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 962,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 961,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 975,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 988,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 987,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 998,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 997,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1008,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1022,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1007,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1032,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1046,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1045,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1056,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 913,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 912,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1080,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1084,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1083,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1082,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1106,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1105,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1116,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1104,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1081,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1128,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1129,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1142,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1140,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 910,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1171,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1172,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1159,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1174,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1179,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1178,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1158,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message,\n                                        label: \"Valor do Investimento\",\n                                        placeholder: \"ex: R$ 50.000,00\",\n                                        setValue: (e)=>setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1188,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1187,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1203,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1156,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1211,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1213,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1209,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1225,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1226,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1227,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1229,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1230,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1231,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1223,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1236,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1245,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1248,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1254,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1255,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1256,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1257,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1263,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1244,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1268,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1267,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1274,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1275,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1273,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1285,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1284,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1234,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1222,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1221,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1219,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1298,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) !== \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1311,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1310,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1309,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-900 border border-orange-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Altera\\xe7\\xe3o M\\xdaTUO → SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1321,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Como o contrato anterior era M\\xdaTUO, \\xe9 necess\\xe1rio tratar o IR acumulado. A tabela de IR ser\\xe1 exibida para que voc\\xea possa escolher como proceder com o desconto.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1320,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1319,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"A\\xe7\\xe3o Obrigat\\xf3ria:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1331,\n                                        columnNumber: 18\n                                    }, this),\n                                    ' Clique no bot\\xe3o \"\"Calcular IR\"\" acima antes de prosseguir.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1329,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-900 border border-green-500 rounded-lg p-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-200 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IR Calculado:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1341,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Agora selecione uma das op\\xe7\\xf5es abaixo (obrigat\\xf3rio):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1340,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDeposito\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDeposito\", true);\n                                                            setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Restaurar valor original se existir (caso estava com desconto)\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                            }\n                                                        } else {\n                                                            setValue(\"irDeposito\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDesconto\")); // Verifica se a outra está marcada\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1346,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDesconto\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDesconto\", true);\n                                                            setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                            const valorAtual = watch(\"valorInvestimento\");\n                                                            if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                                // Salvar valor original se ainda não foi salvo\n                                                                if (!valorOriginalInvestimento) {\n                                                                    setValorOriginalInvestimento(valorAtual);\n                                                                }\n                                                                const valorNumerico = parseValor(valorAtual);\n                                                                const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                                // Debug logs\n                                                                console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                                console.log(\"Valor atual (string):\", valorAtual);\n                                                                console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                                console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                                const valorComDesconto = valorNumerico - valorIRTabela;\n                                                                console.log(\"Valor com desconto:\", valorComDesconto);\n                                                                // Verificar se o valor com desconto é positivo\n                                                                if (valorComDesconto <= 0) {\n                                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                    setValue(\"irDesconto\", false);\n                                                                    return;\n                                                                }\n                                                                // Aplicar o valor com desconto\n                                                                // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                                const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                                console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                                console.log(\"Valor formatado:\", valorFormatado);\n                                                                setValue(\"valorInvestimento\", valorFormatado);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                    style: \"currency\",\n                                                                    currency: \"BRL\"\n                                                                }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                            } else {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                                setValue(\"irDesconto\", false);\n                                                            }\n                                                            // Mostrar aviso de adendo\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                        } else {\n                                                            setValue(\"irDesconto\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDeposito\")); // Verifica se a outra está marcada\n                                                            // Restaurar valor original se existir\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                            }\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1370,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1344,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        watch(\"irDesconto\") && valorOriginalInvestimento && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-blue-200 font-bold mb-2\",\n                                    children: \"\\uD83D\\uDCB0 Desconto de IR Aplicado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1444,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-200 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor original:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1446,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                valorOriginalInvestimento\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1446,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor total do IR (da tabela):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1447,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                new Intl.NumberFormat(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                }).format(contractDetails.totalIR)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1447,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor final (com desconto):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1448,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                watch(\"valorInvestimento\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1448,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-200 text-xs mt-2\",\n                                            children: \"ℹ️ O valor descontado \\xe9 baseado no c\\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1445,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1443,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Conforme informado no formul\\xe1rio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1461,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1460,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold mb-3\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1466,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1464,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-800 border border-yellow-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-100 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Exemplo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1472,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Contrato de R$ 10.000 com IR de R$ 2.000 = R$ 8.000 restantes.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1472,\n                                                columnNumber: 105\n                                            }, this),\n                                            \"• Sistema cadastra: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: [\n                                                    Math.floor(((0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(watch(\"valorInvestimento\")) - ((contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) || 0)) / 5000),\n                                                    \" cota(s)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 164\n                                            }, this),\n                                            \"• Valor complementar: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1474,\n                                                columnNumber: 39\n                                            }, this),\n                                            \" dever\\xe1 ser pago separadamente.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1471,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1470,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1458,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1155,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1480,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>setValue(\"valorInvestimento\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e || \"\"), {\n                                                shouldValidate: true\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1487,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1500,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1512,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1522,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1534,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1539,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1540,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1541,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1535,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1533,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1484,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1547,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1558,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1563,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1564,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1565,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1559,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1557,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1568,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1578,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1583,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1584,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1579,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1577,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1546,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1482,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1481,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1591,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1600,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1590,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1151,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1637,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1642,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1643,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1641,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1640,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1639,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1638,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1654,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1662,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1659,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1658,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1674,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1675,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1673,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1657,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1656,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1655,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"LaY9YY2dj55/8Qmmi1xAsqziseQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});