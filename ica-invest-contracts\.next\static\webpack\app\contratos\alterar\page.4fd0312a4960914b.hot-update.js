"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [irOpcaoSelecionada, setIrOpcaoSelecionada] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se uma opção de IR foi selecionada\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    const [scpValidationError, setScpValidationError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Erro de validação SCP customizado\n    // Função para validar valor SCP\n    const validateScpValue = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)((valor, modalidade)=>{\n        if (modalidade !== \"SCP\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Se o valor estiver vazio, limpar erro e retornar true (será validado pelo Yup)\n        if (!valor || valor.trim() === \"\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        const valorNumerico = Number(valor.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n        // Se há mensagem de complemento, valor parcial é permitido\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - showComplementMessage:\", showComplementMessage);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valor:\", valor);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valorNumerico:\", valorNumerico);\n        if (showComplementMessage) {\n            console.log(\"✅ SCP: Valor parcial permitido devido ao dep\\xf3sito adicional\");\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Validação normal: múltiplo de 5.000 e mínimo de 30.000\n        if (valorNumerico < 30000) {\n            console.log(\"❌ SCP: Valor menor que R$ 30.000\");\n            setScpValidationError(\"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\");\n            return false;\n        }\n        console.log(\"✅ SCP: Valor v\\xe1lido\");\n        setScpValidationError(\"\");\n        return true;\n    }, [\n        showComplementMessage\n    ]);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\") {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            if (valorNumerico > 0) {\n                const cotas = Math.floor(valorNumerico / 5000);\n                setValue(\"quotaQuantity\", cotas.toString());\n            } else {\n                setValue(\"quotaQuantity\", \"0\");\n            }\n        } else if (modalidade === \"SCP\") {\n            // Se não há valor ou valor está vazio, limpar cotas\n            setValue(\"quotaQuantity\", \"0\");\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 377,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando contrato anterior era MUTUO\n        if (data.modalidade === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validação específica para SCP usando função customizada\n        if (!validateScpValue(data.valorInvestimento, data.modalidade)) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(scpValidationError);\n            return;\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            var _contractData_contracts_, _contractData_contracts;\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                // ✅ CAMPOS DE UPGRADE - Identificar que é um upgrade e qual contrato deve ser expirado\n                isUpgrade: true,\n                originalContractId: contractData === null || contractData === void 0 ? void 0 : (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.idContrato,\n                upgradeType: tipo,\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D UseEffect SCP - Condi\\xe7\\xf5es:\");\n        console.log(\"  modalidade === 'SCP':\", modalidade === \"SCP\");\n        console.log(\"  valorInvestimento:\", valorInvestimento);\n        console.log(\"  valorInvestimento.trim() !== '':\", valorInvestimento && valorInvestimento.trim() !== \"\");\n        console.log(\"  irDesconto:\", irDesconto);\n        console.log(\"  contractDetails?.totalIR:\", contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR);\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\" && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            console.log(\"=== C\\xc1LCULO SCP COM DESCONTO IR ===\");\n            console.log(\"Valor original:\", valorNumerico);\n            console.log(\"IR a descontar:\", contractDetails.totalIR);\n            console.log(\"Valor ap\\xf3s desconto:\", valorAposDesconto);\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                // Arredondar para BAIXO (menos cotas) - pegar apenas cotas completas\n                const cotasCompletas = Math.floor(valorAposDesconto / 5000);\n                const valorCotasCompletas = cotasCompletas * 5000;\n                const valorComplementarNecessario = valorAposDesconto - valorCotasCompletas;\n                console.log(\"Resto:\", resto);\n                console.log(\"Cotas completas:\", cotasCompletas);\n                console.log(\"Valor das cotas completas:\", valorCotasCompletas);\n                console.log(\"Valor complementar necess\\xe1rio:\", valorComplementarNecessario);\n                // Atualizar o valor do investimento para o valor das cotas completas + IR\n                const novoValorInvestimento = valorCotasCompletas + contractDetails.totalIR;\n                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)((novoValorInvestimento * 100).toString());\n                setValue(\"valorInvestimento\", valorFormatado);\n                // Atualizar quantidade de cotas\n                setValue(\"quotaQuantity\", cotasCompletas.toString());\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n                console.log(\"✅ showComplementMessage ativado - valor parcial permitido\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"⚠️ Valor ajustado para \".concat(cotasCompletas, \" cotas completas. Valor complementar de \").concat(new Intl.NumberFormat(\"pt-BR\", {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }).format(valorComplementarNecessario), \" ser\\xe1 necess\\xe1rio.\"));\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR,\n        setValue\n    ]);\n    // Validação em tempo real para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (valorInvestimento && modalidade) {\n            validateScpValue(valorInvestimento, modalidade);\n        }\n    }, [\n        valorInvestimento,\n        modalidade,\n        validateScpValue,\n        showComplementMessage\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, IR é opcional mas pode ser aplicado\n            setIrCalculado(true);\n            setIrOpcaoSelecionada(false); // Permitir seleção de IR para SCP\n            // NÃO resetar irDeposito e irDesconto para SCP - deixar o usuário escolher\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setIrOpcaoSelecionada(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 967,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 976,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 977,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 972,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 980,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1002,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 990,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1017,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1016,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1044,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1043,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1053,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1065,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1064,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1078,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1077,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1090,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1089,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1088,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1102,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1113,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1123,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 969,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 968,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1136,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1140,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1139,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1138,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1162,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1161,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1171,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1160,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1137,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1184,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1186,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1185,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1198,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1196,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 966,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1213,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1227,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1228,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1215,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1230,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1235,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1234,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1233,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1214,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"valorInvestimento\",\n                                            width: \"100%\",\n                                            error: !!errors.valorInvestimento || !!scpValidationError,\n                                            errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message) || scpValidationError,\n                                            label: \"Valor do Investimento\",\n                                            placeholder: \"ex: R$ 50.000,00\",\n                                            setValue: (e)=>{\n                                                const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                                setValue(\"valorInvestimento\", maskedValue, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1244,\n                                            columnNumber: 15\n                                        }, this),\n                                        showComplementMessage && scpValidationError === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-xs mt-1\",\n                                            children: \"✅ Valor parcial permitido (dep\\xf3sito adicional ser\\xe1 necess\\xe1rio)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1243,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1212,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1275,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1276,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1274,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1290,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1291,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1292,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1293,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1294,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1296,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1289,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1288,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1302,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1301,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1310,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1313,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1319,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1321,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1322,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1328,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1309,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1333,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1332,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1339,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1340,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1338,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1349,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1299,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1287,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1286,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1284,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1364,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1363,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) !== \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1376,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1375,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1374,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-900 border border-orange-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Altera\\xe7\\xe3o M\\xdaTUO → SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1386,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Como o contrato anterior era M\\xdaTUO, \\xe9 necess\\xe1rio tratar o IR acumulado. A tabela de IR ser\\xe1 exibida para que voc\\xea possa escolher como proceder com o desconto.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1385,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1384,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"A\\xe7\\xe3o Obrigat\\xf3ria:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1396,\n                                        columnNumber: 18\n                                    }, this),\n                                    ' Clique no bot\\xe3o \"\"Calcular IR\"\" acima antes de prosseguir.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1395,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1394,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-900 border border-green-500 rounded-lg p-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-200 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IR Calculado:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1406,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Agora selecione uma das op\\xe7\\xf5es abaixo \",\n                                            watch(\"modalidade\") === \"MUTUO\" ? \"(obrigat\\xf3rio)\" : \"(opcional)\",\n                                            \":\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1405,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1404,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDeposito\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDeposito\", true);\n                                                            setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Restaurar valor original se existir (caso estava com desconto)\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                            }\n                                                        } else {\n                                                            setValue(\"irDeposito\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDesconto\")); // Verifica se a outra está marcada\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1411,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1410,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDesconto\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDesconto\", true);\n                                                            setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                            const valorAtual = watch(\"valorInvestimento\");\n                                                            if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                                // Salvar valor original se ainda não foi salvo\n                                                                if (!valorOriginalInvestimento) {\n                                                                    setValorOriginalInvestimento(valorAtual);\n                                                                }\n                                                                const valorNumerico = parseValor(valorAtual);\n                                                                const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                                // Debug logs\n                                                                console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                                console.log(\"Valor atual (string):\", valorAtual);\n                                                                console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                                console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                                const valorComDesconto = valorNumerico - valorIRTabela;\n                                                                console.log(\"Valor com desconto:\", valorComDesconto);\n                                                                // Verificar se o valor com desconto é positivo\n                                                                if (valorComDesconto <= 0) {\n                                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                    setValue(\"irDesconto\", false);\n                                                                    return;\n                                                                }\n                                                                // Aplicar o valor com desconto\n                                                                // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                                const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                                console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                                console.log(\"Valor formatado:\", valorFormatado);\n                                                                setValue(\"valorInvestimento\", valorFormatado);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                    style: \"currency\",\n                                                                    currency: \"BRL\"\n                                                                }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                            } else {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                                setValue(\"irDesconto\", false);\n                                                            }\n                                                            // Mostrar aviso de adendo\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                        } else {\n                                                            setValue(\"irDesconto\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDeposito\")); // Verifica se a outra está marcada\n                                                            // Restaurar valor original se existir\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                            }\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1435,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1434,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        watch(\"irDesconto\") && valorOriginalInvestimento && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-blue-200 font-bold mb-2\",\n                                    children: \"\\uD83D\\uDCB0 Desconto de IR Aplicado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1509,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-200 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor original:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1511,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                valorOriginalInvestimento\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1511,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor total do IR (da tabela):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1512,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                new Intl.NumberFormat(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                }).format(contractDetails.totalIR)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor final (com desconto):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1513,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                watch(\"valorInvestimento\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-200 text-xs mt-2\",\n                                            children: \"ℹ️ O valor descontado \\xe9 baseado no c\\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1514,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1510,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1508,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Conforme informado no formul\\xe1rio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1526,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1525,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold mb-3\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1531,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-800 border border-yellow-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-100 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Exemplo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1537,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Contrato de R$ 10.000 com IR de R$ 2.000 = R$ 8.000 restantes.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1537,\n                                                columnNumber: 105\n                                            }, this),\n                                            \"• Sistema cadastra: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: [\n                                                    Math.floor(((0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(watch(\"valorInvestimento\")) - ((contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) || 0)) / 5000),\n                                                    \" cota(s)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1538,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1538,\n                                                columnNumber: 164\n                                            }, this),\n                                            \"• Valor complementar: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1539,\n                                                columnNumber: 39\n                                            }, this),\n                                            \" dever\\xe1 ser pago separadamente.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1536,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1535,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-800 border border-green-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Valor Parcial Permitido:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1544,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Neste caso, o contrato pode ser enviado com o valor atual (n\\xe3o m\\xfaltiplo de R$ 5.000), pois o sistema reconhece que haver\\xe1 um dep\\xf3sito adicional posterior para completar as cotas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1543,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1523,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1211,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1551,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1558,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1574,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1573,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1586,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1596,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1608,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1613,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1614,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1615,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1609,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1607,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1555,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1621,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1632,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1637,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1638,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1639,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1633,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1631,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1642,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1652,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1657,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1658,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1653,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1651,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1620,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1553,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1552,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1665,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1674,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1664,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1207,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1711,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1716,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1717,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1715,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1714,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1713,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1712,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1728,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1736,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1733,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1732,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1748,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1749,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1747,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1731,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1730,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1729,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"S2p+YJ6E2UICSmXYuSm0HDLU9Bk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});