export const cpfMask = (value: string) => {
  return value
    .replace(/\D/g, '')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d{1,2})/, '$1-$2')
    .replace(/(-\d{2})\d+?$/, '$1');
};

export const cnpjMask = (value: string) => {
  return value
    .replace(/\D/g, '')
    .replace(/^(\d{2})(\d)/, '$1.$2')
    .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
    .replace(/\.(\d{3})(\d)/, '.$1/$2')
    .replace(/(\d{4})(\d)/, '$1-$2')
    .replace(/(-\d{2})\d+?$/, '$1');
};

export const valueMask = (value: string) => {
    // Se o valor estiver vazio ou for undefined/null, retornar string vazia
    if (!value || value.trim() === '') {
      return '';
    }

    // Remover todos os caracteres não numéricos
    const numericOnly = value.replace(/\D/g, '');

    // Se não há dígitos, retornar string vazia
    if (numericOnly === '') {
      return '';
    }

    // Aplicar máscara apenas se há pelo menos 1 dígito
    return numericOnly
      .replace(/(\d{1})(\d{14})$/, '$1.$2') // coloca ponto antes dos ultimos digitos
      .replace(/(\d{1})(\d{11})$/, '$1.$2') // coloca ponto antes dos ultimos 11 digitos
      .replace(/(\d{1})(\d{8})$/, '$1.$2') // coloca ponto antes dos ultimos 8 digitos
      .replace(/(\d{1})(\d{5})$/, '$1.$2') // coloca ponto antes dos ultimos 5 digitos
      .replace(/(\d{1})(\d{1,2})$/, '$1,$2'); // coloca virgula antes dos ultimos 2 digitos
};

export const phoneMask = (value: string) => {
  return value
    .replace(/\D/g, '') // Remove tudo que não for dígito
    .replace(/^55/, '') // Remove "55" se estiver no início
    .replace(/^(\d{2})(\d)/g, '($1) $2') // Coloca parênteses no DDD
    .replace(/(\d)(\d{4})$/, '$1-$2'); // Coloca hífen
};
export const getDateFormat = (value: string) =>
  value.replace(/\D/g, '').replace(/^(\d{2})(\d{2})(\d)/, '$1/$2/$3');

export const hideCpf = (value: string) => {
  return value.replace(/(\d{3})/, '•••').replace(/(\d{1})(\d{1,2})$/, '••');
};

export const hideCnpj = (value: string) => {
  return value.replace(/(\d{3})/, '•••').replace(/(\d{1})(\d{1,2})$/, '••');
};

export const clearLetters = (value: string) => {
  return value.replace(/\D/g, '');
};

export const cepMask = (value: string) => {
  return value
    .replace(/\D/g, '')                
    .replace(/(\d{5})(\d)/, '$1-$2')
    .replace(/(-\d{3})\d+?$/, '$1')
}
