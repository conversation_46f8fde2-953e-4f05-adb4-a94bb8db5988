{"c": ["app/layout", "app/contratos/alterar/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js", "(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cprojetos%5C3%5Cica-invest-contracts%5Csrc%5Capp%5Ccontratos%5Calterar%5Cpage.tsx&server=false!", "(app-pages-browser)/./src/app/contratos/alterar/page.tsx", "(app-pages-browser)/./src/components/Inputs/InputText/index.tsx", "(app-pages-browser)/./src/components/Inputs/InputText/style.css"]}