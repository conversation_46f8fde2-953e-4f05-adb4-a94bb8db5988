import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsCNPJ, IsCPF } from 'brazilian-class-validator';
import { Transform, Type } from 'class-transformer';
import {
  IsString,
  IsNotEmpty,
  MinLength,
  IsOptional,
  IsEnum,
  IsEmail,
  IsNumber,
  IsPositive,
  Min,
  Max,
  IsInt,
  Matches,
  ValidateNested,
  IsBoolean,
  IsArray,
  IsUUID,
  ValidateIf,
  IsDateString,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { IsValidName } from 'src/shared/decorators/is-valid-name.decorator';
import { SanitizeDocument } from 'src/shared/decorators/sanitize-documents';
import { ToBoolean } from 'src/shared/decorators/to-boolean.decorator';
import { ToNumber } from 'src/shared/decorators/to-number.decorator';
import { generateDate } from 'src/shared/functions/generate-date';

/* Enums conforme definidos no seu código */

export enum PersonType {
  PF = 'PF',
  PJ = 'PJ',
}

export enum Role {
  broker = 'broker',
  advisor = 'advisor',
}

export enum ContractType {
  SCP = 'SCP',
  MUTUO = 'MUTUO',
}

export enum PaymentMethod {
  PIX = 'pix',
  BANK_TRANSFER = 'bank_transfer',
  BOLETO = 'boleto',
}

export enum InvestorProfile {
  CONSERVATIVE = 'conservative',
  MODERATE = 'moderate',
  AGGRESSIVE = 'aggressive',
}

export enum CompanyLegalType {
  MEI = 'MEI',
  EI = 'EI',
  EIRELI = 'EIRELI',
  LTDA = 'LTDA',
  SLU = 'SLU',
  SA = 'SA',
  SS = 'SS',
  CONSORCIO = 'CONSORCIO',
}

/**
 * Valida se o investimento tem uma quantidade de cotas válida para contrato SCP
 * @param validationOptions
 * @returns
 */
function ValidateInvestmentWithContractType(
  validationOptions?: ValidationOptions,
) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'validateInvestmentWithContractType',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: InvestmentDetailsDto, args: ValidationArguments) {
          const parent = args.object as CreateNewContractDto;

          if (parent.contractType !== ContractType.SCP) return true;

          return (
            value?.quotaQuantity != null &&
            Number.isInteger(value.quotaQuantity) &&
            value.quotaQuantity > 0
          );
        },
        defaultMessage() {
          return 'Quantidade de cotas é obrigatória para contrato SCP e deve ser um número inteiro maior que zero';
        },
      },
    });
  };
}

/* DTOs convertidos para classes com validação */

export class AddressDto {
  @ApiProperty({
    description: 'Rua (ex: Av. Paulista)',
    example: 'Av. Paulista',
  })
  @IsNotEmpty({ message: 'Rua não pode estar vazia' })
  @MinLength(3, { message: 'A rua deve ter no mínimo 3 caracteres' })
  street!: string;

  @ApiProperty({ description: 'Cidade (ex: São Paulo)', example: 'São Paulo' })
  @IsNotEmpty({ message: 'Cidade não pode estar vazia' })
  @MinLength(2, { message: 'A cidade deve ter no mínimo 2 caracteres' })
  city!: string;

  @ApiProperty({ description: 'Estado (ex: SP)', example: 'SP' })
  @Matches(/^[A-Z]{2}$/, {
    message: 'Estado inválido. Use o formato XX (ex: SP)',
  })
  state!: string;

  @ApiProperty({
    description: 'Bairro (ex: Bela Vista)',
    example: 'Bela Vista',
  })
  @IsNotEmpty({ message: 'Bairro não pode estar vazio' })
  neighborhood!: string;

  @ApiProperty({
    description: 'CEP (ex: 01310-100 ou 01310100)',
    example: '01310100',
    pattern: '^\\d{5}-?\\d{3}$',
  })
  @IsNotEmpty({ message: 'CEP não pode estar vazio' })
  @Matches(/^\d{8}$/, {
    message: 'CEP inválido. Use o formato XXXXXXXX (ex: 01310100)',
  })
  postalCode!: string;

  @ApiProperty({ description: 'Número (ex: 1000)', example: '1000' })
  @IsNotEmpty({ message: 'Número não pode estar vazio' })
  number!: string;

  @ApiPropertyOptional({ description: 'Complemento (ex: Apt 101)' })
  @IsOptional()
  @IsString()
  complement?: string;
}

export class IndividualDto {
  @ApiProperty({ description: 'Nome completo (ex: João da Silva)' })
  @IsNotEmpty({ message: 'Nome completo não pode estar vazio' })
  @IsValidName({
    message:
      'Nome completo inválido. Use apenas letras, espaços, hífens e caracteres acentuados',
  })
  fullName!: string;

  @ApiProperty({ description: 'CPF (ex: 12345678900)', example: '' })
  @IsCPF({
    message: 'CPF inválido. Use o formato XXXXXXXXXXX (ex: 12345678900)',
  })
  cpf!: string;

  @ApiProperty({ description: 'RG (ex: 123456789)', example: '123456789' })
  @IsNotEmpty({ message: 'RG não pode estar vazio' })
  rg!: string;

  @ApiProperty({ description: 'Órgão emissor (ex: SSP)' })
  @IsNotEmpty({ message: 'Órgão emissor não pode estar vazio' })
  issuingAgency!: string;

  @ApiProperty({ description: 'Nacionalidade (ex: Brasileiro)' })
  @IsNotEmpty({ message: 'Nacionalidade não pode estar vazia' })
  nationality!: string;

  @ApiProperty({ description: 'Ocupação (ex: Engenheiro)' })
  @IsNotEmpty({ message: 'Ocupação não pode estar vazia' })
  @IsValidName({
    message:
      'Ocupação inválida. Use apenas letras, espaços, hífens e caracteres acentuados',
  })
  occupation!: string;

  @ApiProperty({
    description: 'Data de nascimento (ex: 1990-01-01)',
    example: generateDate(-(365 * 20)),
  })
  @IsDateString({}, { message: 'Data de nascimento inválida' })
  @IsNotEmpty({ message: 'Data de nascimento é obrigatória' })
  birthDate!: string;

  @ApiProperty({
    description: 'Email (ex: <EMAIL>)',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'E-mail inválido' })
  email!: string;

  @ApiProperty({
    description: 'Telefone celular (ex: 5548999999999)',
    example: '48999999999',
  })
  @Matches(/^55(\d{2})\d{8,9}$/, {
    message: 'Telefone inválido. (ex: 5548999999999)',
  })
  phone!: string;

  @ApiProperty({
    description: 'Nome da mãe (ex: Maria da Silva)',
    example: 'Maria da Silva',
  })
  @IsValidName({
    message:
      'Nome da mãe inválido. Use apenas letras, espaços, hífens e caracteres acentuados',
  })
  @IsNotEmpty({ message: 'Nome da mãe não pode estar vazio' })
  motherName!: string;

  @ApiProperty({
    description: 'Endereço',
    type: AddressDto,
    example: {
      street: 'Av. Paulista',
      number: '1000',
      city: 'São Paulo',
      state: 'SP',
      postalCode: '01310100',
      neighborhood: 'Bela Vista',
      complement: 'Apt 101',
    },
  })
  @ValidateNested()
  @Type(() => AddressDto)
  address!: AddressDto;
}

export class CompanyDto {
  @ApiProperty({
    description: 'Razão social',
    example: 'ACME Ltda',
  })
  @IsString({ message: 'Razão Social é obrigatória' })
  @IsNotEmpty({ message: 'Razão social não pode estar vazia' })
  corporateName!: string;

  @ApiProperty({
    description: 'CNPJ (ex: 12345678000199)',
    example: '12345678000199',
  })
  @IsNotEmpty({ message: 'CNPJ é obrigatório' })
  @IsCNPJ({
    message:
      'CNPJ inválido. Use o formato XXXXXXXXXXXXXXX (ex: 12345678000199)',
  })
  cnpj!: string;

  @ApiProperty({
    description: 'Tipo jurídico da empresa',
    enum: CompanyLegalType,
    example: CompanyLegalType.LTDA,
  })
  @IsEnum(CompanyLegalType, { message: 'Tipo de empresa inválido' })
  type!: CompanyLegalType;

  @ApiProperty({ description: 'Endereço', type: AddressDto })
  @ValidateNested()
  @Type(() => AddressDto)
  address!: AddressDto;

  @ApiProperty({ description: 'Representante legal', type: IndividualDto })
  @ValidateNested()
  @Type(() => IndividualDto)
  representative!: IndividualDto;
}

export class InvestmentDetailsDto {
  @ApiProperty({
    description: 'Valor do investimento',
    type: Number,
  })
  @ToNumber()
  @IsNumber(
    { allowNaN: false },
    { message: 'Valor do investimento é obrigatório' },
  )
  @IsPositive({ message: 'Valor deve ser maior que zero' })
  amount!: number;

  @ApiProperty({
    description: 'Taxa de remuneração',
    type: Number,
  })
  @ToNumber()
  @IsNumber(
    { allowNaN: false },
    { message: 'Taxa de remuneração é obrigatória' },
  )
  @IsPositive({ message: 'Taxa de remuneração deve ser maior que zero' })
  monthlyRate!: number;

  @ApiProperty({
    description: 'Prazo em meses',
    type: Number,
  })
  @IsNumber({}, { message: 'Prazo em meses é obrigatório' })
  @IsInt({ message: 'O prazo deve ser um número inteiro' })
  @IsPositive({ message: 'Prazo deve ser maior que zero' })
  @IsNotEmpty({ message: 'Prazo em meses não pode ser vazio' })
  durationInMonths!: number;

  @ApiProperty({
    description: 'Método de pagamento',
    enum: PaymentMethod,
    example: PaymentMethod.PIX,
  })
  @IsEnum(PaymentMethod, {
    message:
      'Método de pagamento inválido. Tipos válidos: pix, bank_transfer, boleto',
  })
  paymentMethod!: PaymentMethod;

  @ApiProperty({
    description: 'Data de início (ex: 2025-01-01)',
    example: generateDate(365),
  })
  @IsDateString({}, { message: 'Data de fim de contrato inválida' })
  @IsNotEmpty({ message: 'Data de fim do contrato não pode ser vazia' })
  endDate!: string;

  @ApiProperty({
    description: 'Perfil do investidor',
    type: String,
    enum: InvestorProfile,
    example: 'conservative, moderate, aggressive',
  })
  @IsEnum(InvestorProfile, {
    message:
      'Perfil do investidor inválido. Tipos válidos: conservative, moderate, aggressive',
  })
  profile!: InvestorProfile;

  @ApiPropertyOptional({
    description: 'Quantidade de cotas (ex: 1)',
    example: 2,
  })
  @IsOptional()
  quotaQuantity?: number;

  @ApiPropertyOptional({
    description: 'É debênture? (ex: false)',
    example: false,
  })
  @ToBoolean()
  @IsBoolean({ message: 'Campo Debênture é obrigatório' })
  isDebenture?: boolean;
}

export class AdvisorAssignmentDto {
  @ApiProperty({
    description: 'ID do assessor (ex: advisor-uuid)',
    example: '',
  })
  @IsUUID('all', { message: 'ID do assessor inválido' })
  advisorId!: string;

  @ApiProperty({
    description: 'Porcentagem do assessor (%) (ex: 10)',
    example: 10,
  })
  @ToNumber()
  @IsNumber({ allowNaN: false }, { message: 'Taxa do assessor é obrigatória' })
  @Min(0.01, { message: 'Porcentagem deve ser maior que zero' })
  @Max(100, { message: 'Porcentagem não pode ser maior que 100' })
  rate!: number;
}

export class BankAccountDto {
  @ApiProperty({
    description: 'Nome do banco (ex: ICA Bank)',
    example: 'ICA Bank',
  })
  @IsNotEmpty({ message: 'Nome do banco não pode estar vazio' })
  @MinLength(2, { message: 'Nome do banco muito curto' })
  bank!: string;

  @ApiProperty({
    description: 'Agência bancária (ex: 0001)',
    example: '0001',
  })
  @IsNotEmpty({ message: 'Agência não pode estar vazia' })
  @MinLength(2, { message: 'Agência inválida' })
  agency!: string;

  @ApiProperty({
    description: 'Conta bancária (ex: 1234567)',
    example: '1234567',
  })
  @IsNotEmpty({ message: 'Conta não pode estar vazia' })
  @MinLength(3, { message: 'Conta inválida' })
  account!: string;

  @ApiPropertyOptional({ description: 'Chave PIX (ex: <EMAIL>)' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      // If it's just DDD + number (e.g., ***********)
      if (value.match(/^\d{2}9\d{8}$/)) {
        return '+55' + value;
      }
      // If it has 55 but no + (e.g., 55***********)
      if (value.match(/^55\d{2}9\d{8}$/)) {
        return '+' + value;
      }
    }
    return value as string;
  })
  @Matches(
    /^(\+55\d{2}9\d{8}|[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}|\d{11}|\d{14}|[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})$/,
    {
      message:
        'Chave PIX inválida. Use CPF (11 dígitos), CNPJ (14 dígitos), telefone (+55+DDD+Telefone), email ou chave aleatória.',
    },
  )
  pix?: string;
}

export class CreateNewContractDto {
  @ApiProperty({
    description: 'Tipo de role de investidor',
    type: String,
    enum: Role,
    example: 'broker ou assessor',
  })
  @IsEnum(Role, {
    message: 'Tipo de perfil inválido. Tipos válidos: broker e assessor',
  })
  @IsNotEmpty({ message: 'Perfil é obrigatório' })
  role!: Role;

  @ApiProperty({
    description: 'Tipo de pessoa',
    type: String,
    enum: PersonType,
    example: 'PF ou PJ',
  })
  @IsEnum(PersonType, {
    message: 'Tipo de pessoa inválido. Tipos válidos: PF e PJ',
  })
  @IsNotEmpty({ message: 'Tipo de pessoa é obrigatório' })
  personType!: PersonType;

  @ApiProperty({
    description: 'Tipo de pessoa',
    type: String,
    enum: ContractType,
    example: 'SCP ou MUTUO',
  })
  @IsEnum(ContractType, {
    message: 'Tipo de contrato inválido. Tipos válidos: MUTUO ou SCP',
  })
  @IsNotEmpty({ message: 'Tipo de contrato é obrigatório' })
  contractType!: ContractType;

  @ApiProperty({
    description: 'Lista de assessores',
    type: Array<AdvisorAssignmentDto>,
    isArray: true,
    example: [
      {
        advisorId: '550e8400-e29b-41d4-a716-446655440000',
        rate: 10,
      },
    ],
  })
  @IsArray({ message: 'Assessor deve ser uma lista' })
  @ValidateNested({ each: true })
  @Type(() => AdvisorAssignmentDto)
  advisors!: AdvisorAssignmentDto[];

  @ApiProperty({
    description: 'Investimento',
    type: InvestmentDetailsDto,
    example: {
      amount: 10000,
      monthlyRate: 1.5,
      durationInMonths: 12,
      paymentMethod: PaymentMethod.PIX,
      endDate: generateDate(365),
      profile: InvestorProfile.MODERATE,
      quotaQuantity: 2,
      isDebenture: false,
    },
  })
  @ValidateInvestmentWithContractType()
  @ValidateNested()
  @Type(() => InvestmentDetailsDto)
  investment!: InvestmentDetailsDto;

  @ApiProperty({
    description: 'Conta bancária',
    type: BankAccountDto,
    example: {
      bank: 'ICA Bank',
      agency: '0001',
      account: '1234567',
      pix: '<EMAIL>',
    },
  })
  @ValidateNested()
  @Type(() => BankAccountDto)
  bankAccount!: BankAccountDto;

  @ApiPropertyOptional({
    description:
      'Dados do investidor pessoa física (obrigatório se personType for PF)',
    type: IndividualDto,
  })
  @ValidateIf((o) => o.personType === PersonType.PF)
  @ValidateNested()
  @Type(() => IndividualDto)
  individual?: IndividualDto;

  @ApiPropertyOptional({
    description: 'Dados da empresa (obrigatório se personType for PJ)',
    type: CompanyDto,
  })
  @ValidateIf((o) => o.personType === PersonType.PJ)
  @ValidateNested()
  @Type(() => CompanyDto)
  company?: CompanyDto;

  // ✅ CAMPOS DE UPGRADE
  @ApiPropertyOptional({
    description: 'Indica se é um upgrade de contrato existente',
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @ToBoolean()
  isUpgrade?: boolean;

  @ApiPropertyOptional({
    description: 'ID do contrato original que deve ser marcado como expired (obrigatório se isUpgrade for true)',
    type: String,
    example: 'uuid-do-contrato-original',
  })
  @IsOptional()
  @ValidateIf((o) => o.isUpgrade === true)
  @IsNotEmpty({ message: 'ID do contrato original é obrigatório para upgrades' })
  @ValidateIf((o) => o.originalContractId !== undefined && o.originalContractId !== null && o.originalContractId !== '')
  @IsUUID('all', { message: 'ID do contrato original deve ser um UUID válido' })
  originalContractId?: string;

  @ApiPropertyOptional({
    description: 'Tipo de upgrade sendo realizado',
    type: String,
    example: 'modalidade ou rentabilidade',
  })
  @IsOptional()
  @IsString({ message: 'Tipo de upgrade deve ser uma string' })
  upgradeType?: string;
}
