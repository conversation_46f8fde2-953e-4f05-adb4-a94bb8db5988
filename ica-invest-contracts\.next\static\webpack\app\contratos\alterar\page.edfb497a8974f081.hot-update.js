"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [irOpcaoSelecionada, setIrOpcaoSelecionada] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se uma opção de IR foi selecionada\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    const [scpValidationError, setScpValidationError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Erro de validação SCP customizado\n    // Função para validar valor SCP\n    const validateScpValue = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)((valor, modalidade)=>{\n        if (modalidade !== \"SCP\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Se o valor estiver vazio, limpar erro e retornar true (será validado pelo Yup)\n        if (!valor || valor.trim() === \"\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        const valorNumerico = Number(valor.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n        // Se há mensagem de complemento, valor parcial é permitido\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - showComplementMessage:\", showComplementMessage);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valor:\", valor);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valorNumerico:\", valorNumerico);\n        if (showComplementMessage) {\n            console.log(\"✅ SCP: Valor parcial permitido devido ao dep\\xf3sito adicional\");\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Validação normal: múltiplo de 5.000 e mínimo de 30.000\n        if (valorNumerico < 30000) {\n            console.log(\"❌ SCP: Valor menor que R$ 30.000\");\n            setScpValidationError(\"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\");\n            return false;\n        }\n        if (valorNumerico % 5000 !== 0) {\n            console.log(\"❌ SCP: Valor n\\xe3o \\xe9 m\\xfaltiplo de R$ 5.000\");\n            setScpValidationError(\"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\");\n            return false;\n        }\n        console.log(\"✅ SCP: Valor v\\xe1lido\");\n        setScpValidationError(\"\");\n        return true;\n    }, [\n        showComplementMessage\n    ]);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\") {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            if (valorNumerico > 0) {\n                const cotas = Math.floor(valorNumerico / 5000);\n                setValue(\"quotaQuantity\", cotas.toString());\n            } else {\n                setValue(\"quotaQuantity\", \"0\");\n            }\n        } else if (modalidade === \"SCP\") {\n            // Se não há valor ou valor está vazio, limpar cotas\n            setValue(\"quotaQuantity\", \"0\");\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando contrato anterior era MUTUO\n        if (data.modalidade === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validação específica para SCP usando função customizada\n        if (!validateScpValue(data.valorInvestimento, data.modalidade)) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(scpValidationError);\n            return;\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D UseEffect SCP - Condi\\xe7\\xf5es:\");\n        console.log(\"  modalidade === 'SCP':\", modalidade === \"SCP\");\n        console.log(\"  valorInvestimento:\", valorInvestimento);\n        console.log(\"  valorInvestimento.trim() !== '':\", valorInvestimento && valorInvestimento.trim() !== \"\");\n        console.log(\"  irDesconto:\", irDesconto);\n        console.log(\"  contractDetails?.totalIR:\", contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR);\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\" && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            console.log(\"=== C\\xc1LCULO SCP COM DESCONTO IR ===\");\n            console.log(\"Valor original:\", valorNumerico);\n            console.log(\"IR a descontar:\", contractDetails.totalIR);\n            console.log(\"Valor ap\\xf3s desconto:\", valorAposDesconto);\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                // Arredondar para BAIXO (menos cotas) - pegar apenas cotas completas\n                const cotasCompletas = Math.floor(valorAposDesconto / 5000);\n                const valorCotasCompletas = cotasCompletas * 5000;\n                const valorComplementarNecessario = valorAposDesconto - valorCotasCompletas;\n                console.log(\"Resto:\", resto);\n                console.log(\"Cotas completas:\", cotasCompletas);\n                console.log(\"Valor das cotas completas:\", valorCotasCompletas);\n                console.log(\"Valor complementar necess\\xe1rio:\", valorComplementarNecessario);\n                // Atualizar o valor do investimento para o valor das cotas completas + IR\n                const novoValorInvestimento = valorCotasCompletas + contractDetails.totalIR;\n                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)((novoValorInvestimento * 100).toString());\n                setValue(\"valorInvestimento\", valorFormatado);\n                // Atualizar quantidade de cotas\n                setValue(\"quotaQuantity\", cotasCompletas.toString());\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n                console.log(\"✅ showComplementMessage ativado - valor parcial permitido\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"⚠️ Valor ajustado para \".concat(cotasCompletas, \" cotas completas. Valor complementar de \").concat(new Intl.NumberFormat(\"pt-BR\", {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }).format(valorComplementarNecessario), \" ser\\xe1 necess\\xe1rio.\"));\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR,\n        setValue\n    ]);\n    // Validação em tempo real para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (valorInvestimento && modalidade) {\n            validateScpValue(valorInvestimento, modalidade);\n        }\n    }, [\n        valorInvestimento,\n        modalidade,\n        validateScpValue,\n        showComplementMessage\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, IR é opcional mas pode ser aplicado\n            setIrCalculado(true);\n            setIrOpcaoSelecionada(false); // Permitir seleção de IR para SCP\n            // NÃO resetar irDeposito e irDesconto para SCP - deixar o usuário escolher\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setIrOpcaoSelecionada(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 968,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 972,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 977,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 978,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 971,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 982,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 993,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 992,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1003,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1018,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1017,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1045,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1044,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1055,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1054,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1066,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1079,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1064,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1091,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1089,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1103,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1114,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1124,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 970,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 969,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1137,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1140,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1150,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1139,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1162,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1173,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1172,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1161,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1138,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1185,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1187,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1186,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1199,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1197,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 967,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1214,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1228,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1229,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1231,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1236,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1235,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"valorInvestimento\",\n                                            width: \"100%\",\n                                            error: !!errors.valorInvestimento || !!scpValidationError,\n                                            errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message) || scpValidationError,\n                                            label: \"Valor do Investimento\",\n                                            placeholder: \"ex: R$ 50.000,00\",\n                                            setValue: (e)=>{\n                                                console.log(\"Input SCP - Valor recebido:\", e);\n                                                const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                                console.log(\"Input SCP - Valor com m\\xe1scara:\", maskedValue);\n                                                setValue(\"valorInvestimento\", maskedValue, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1245,\n                                            columnNumber: 15\n                                        }, this),\n                                        showComplementMessage && scpValidationError === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-xs mt-1\",\n                                            children: \"✅ Valor parcial permitido (dep\\xf3sito adicional ser\\xe1 necess\\xe1rio)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1262,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1271,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1270,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1279,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1281,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1277,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1288,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1293,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1294,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1296,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1299,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1292,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1291,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1305,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1304,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1313,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1316,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1322,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1324,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1325,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1331,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1312,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1336,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1335,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1342,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1343,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1341,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1353,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1352,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1290,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1289,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1287,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1368,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1366,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) !== \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1379,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1378,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1377,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-900 border border-orange-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Altera\\xe7\\xe3o M\\xdaTUO → SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1389,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Como o contrato anterior era M\\xdaTUO, \\xe9 necess\\xe1rio tratar o IR acumulado. A tabela de IR ser\\xe1 exibida para que voc\\xea possa escolher como proceder com o desconto.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1388,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1387,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"A\\xe7\\xe3o Obrigat\\xf3ria:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1399,\n                                        columnNumber: 18\n                                    }, this),\n                                    ' Clique no bot\\xe3o \"\"Calcular IR\"\" acima antes de prosseguir.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1398,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1397,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-900 border border-green-500 rounded-lg p-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-200 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IR Calculado:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1409,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Agora selecione uma das op\\xe7\\xf5es abaixo \",\n                                            watch(\"modalidade\") === \"MUTUO\" ? \"(obrigat\\xf3rio)\" : \"(opcional)\",\n                                            \":\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1407,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDeposito\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDeposito\", true);\n                                                            setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Restaurar valor original se existir (caso estava com desconto)\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                            }\n                                                        } else {\n                                                            setValue(\"irDeposito\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDesconto\")); // Verifica se a outra está marcada\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1414,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1413,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDesconto\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDesconto\", true);\n                                                            setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                            const valorAtual = watch(\"valorInvestimento\");\n                                                            if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                                // Salvar valor original se ainda não foi salvo\n                                                                if (!valorOriginalInvestimento) {\n                                                                    setValorOriginalInvestimento(valorAtual);\n                                                                }\n                                                                const valorNumerico = parseValor(valorAtual);\n                                                                const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                                // Debug logs\n                                                                console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                                console.log(\"Valor atual (string):\", valorAtual);\n                                                                console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                                console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                                const valorComDesconto = valorNumerico - valorIRTabela;\n                                                                console.log(\"Valor com desconto:\", valorComDesconto);\n                                                                // Verificar se o valor com desconto é positivo\n                                                                if (valorComDesconto <= 0) {\n                                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                    setValue(\"irDesconto\", false);\n                                                                    return;\n                                                                }\n                                                                // Aplicar o valor com desconto\n                                                                // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                                const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                                console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                                console.log(\"Valor formatado:\", valorFormatado);\n                                                                setValue(\"valorInvestimento\", valorFormatado);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                    style: \"currency\",\n                                                                    currency: \"BRL\"\n                                                                }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                            } else {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                                setValue(\"irDesconto\", false);\n                                                            }\n                                                            // Mostrar aviso de adendo\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                        } else {\n                                                            setValue(\"irDesconto\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDeposito\")); // Verifica se a outra está marcada\n                                                            // Restaurar valor original se existir\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                            }\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1438,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1437,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1412,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        watch(\"irDesconto\") && valorOriginalInvestimento && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-blue-200 font-bold mb-2\",\n                                    children: \"\\uD83D\\uDCB0 Desconto de IR Aplicado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-200 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor original:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1514,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                valorOriginalInvestimento\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1514,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor total do IR (da tabela):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1515,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                new Intl.NumberFormat(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                }).format(contractDetails.totalIR)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1515,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor final (com desconto):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1516,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                watch(\"valorInvestimento\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1516,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-200 text-xs mt-2\",\n                                            children: \"ℹ️ O valor descontado \\xe9 baseado no c\\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1517,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1513,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1511,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1527,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Conforme informado no formul\\xe1rio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1529,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1528,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold mb-3\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1534,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1532,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-800 border border-yellow-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-100 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Exemplo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1540,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Contrato de R$ 10.000 com IR de R$ 2.000 = R$ 8.000 restantes.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1540,\n                                                columnNumber: 105\n                                            }, this),\n                                            \"• Sistema cadastra: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: [\n                                                    Math.floor(((0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(watch(\"valorInvestimento\")) - ((contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) || 0)) / 5000),\n                                                    \" cota(s)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 164\n                                            }, this),\n                                            \"• Valor complementar: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1542,\n                                                columnNumber: 39\n                                            }, this),\n                                            \" dever\\xe1 ser pago separadamente.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1539,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1538,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-800 border border-green-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Valor Parcial Permitido:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1547,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Neste caso, o contrato pode ser enviado com o valor atual (n\\xe3o m\\xfaltiplo de R$ 5.000), pois o sistema reconhece que haver\\xe1 um dep\\xf3sito adicional posterior para completar as cotas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1546,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1545,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1526,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1212,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1554,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1561,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1577,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1576,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1589,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1599,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1611,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1616,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1617,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1618,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1612,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1610,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1558,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1624,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1635,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1640,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1641,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1642,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1636,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1634,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1645,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1655,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1660,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1661,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1656,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1654,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1623,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1556,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1555,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1668,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1677,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1667,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1208,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1714,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1719,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1720,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1718,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1717,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1716,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1715,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1731,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1739,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1736,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1735,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1751,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1752,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1750,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1734,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1733,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1732,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"S2p+YJ6E2UICSmXYuSm0HDLU9Bk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});