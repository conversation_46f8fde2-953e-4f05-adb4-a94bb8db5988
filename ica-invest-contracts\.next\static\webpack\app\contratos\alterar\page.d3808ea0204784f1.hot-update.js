"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [irOpcaoSelecionada, setIrOpcaoSelecionada] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se uma opção de IR foi selecionada\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    const [scpValidationError, setScpValidationError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Erro de validação SCP customizado\n    // Função para validar valor SCP\n    const validateScpValue = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)((valor, modalidade)=>{\n        if (modalidade !== \"SCP\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Se o valor estiver vazio, limpar erro e retornar true (será validado pelo Yup)\n        if (!valor || valor.trim() === \"\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        const valorNumerico = Number(valor.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n        // Se há mensagem de complemento, valor parcial é permitido\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - showComplementMessage:\", showComplementMessage);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valor:\", valor);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valorNumerico:\", valorNumerico);\n        if (showComplementMessage) {\n            console.log(\"✅ SCP: Valor parcial permitido devido ao dep\\xf3sito adicional\");\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Validação normal: múltiplo de 5.000 e mínimo de 30.000\n        if (valorNumerico < 30000) {\n            console.log(\"❌ SCP: Valor menor que R$ 30.000\");\n            setScpValidationError(\"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\");\n            return false;\n        }\n        console.log(\"✅ SCP: Valor v\\xe1lido\");\n        setScpValidationError(\"\");\n        return true;\n    }, [\n        showComplementMessage\n    ]);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\") {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            if (valorNumerico > 0) {\n                const cotas = Math.floor(valorNumerico / 5000);\n                setValue(\"quotaQuantity\", cotas.toString());\n            } else {\n                setValue(\"quotaQuantity\", \"0\");\n            }\n        } else if (modalidade === \"SCP\") {\n            // Se não há valor ou valor está vazio, limpar cotas\n            setValue(\"quotaQuantity\", \"0\");\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 377,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando contrato anterior era MUTUO\n        if (data.modalidade === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validação específica para SCP usando função customizada\n        if (!validateScpValue(data.valorInvestimento, data.modalidade)) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(scpValidationError);\n            return;\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            var _contractData_contracts_, _contractData_contracts;\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Validar se temos o ID do contrato original para upgrade\n            const originalContractId = contractData === null || contractData === void 0 ? void 0 : (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.idContrato;\n            console.log(\"\\uD83D\\uDD0D Debug Upgrade - contractData:\", contractData);\n            console.log(\"\\uD83D\\uDD0D Debug Upgrade - originalContractId:\", originalContractId);\n            console.log(\"\\uD83D\\uDD0D Debug Upgrade - tipo:\", tipo);\n            if (!originalContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: ID do contrato original n\\xe3o encontrado. N\\xe3o \\xe9 poss\\xedvel realizar upgrade.\");\n                return;\n            }\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                // ✅ CAMPOS DE UPGRADE - Identificar que é um upgrade e qual contrato deve ser expirado\n                isUpgrade: true,\n                originalContractId: originalContractId,\n                upgradeType: tipo,\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"=== DADOS DE UPGRADE ===\");\n            console.log(\"IsUpgrade:\", requestData.isUpgrade);\n            console.log(\"OriginalContractId:\", requestData.originalContractId);\n            console.log(\"UpgradeType:\", requestData.upgradeType);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D UseEffect SCP - Condi\\xe7\\xf5es:\");\n        console.log(\"  modalidade === 'SCP':\", modalidade === \"SCP\");\n        console.log(\"  valorInvestimento:\", valorInvestimento);\n        console.log(\"  valorInvestimento.trim() !== '':\", valorInvestimento && valorInvestimento.trim() !== \"\");\n        console.log(\"  irDesconto:\", irDesconto);\n        console.log(\"  contractDetails?.totalIR:\", contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR);\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\" && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            console.log(\"=== C\\xc1LCULO SCP COM DESCONTO IR ===\");\n            console.log(\"Valor original:\", valorNumerico);\n            console.log(\"IR a descontar:\", contractDetails.totalIR);\n            console.log(\"Valor ap\\xf3s desconto:\", valorAposDesconto);\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                // Arredondar para BAIXO (menos cotas) - pegar apenas cotas completas\n                const cotasCompletas = Math.floor(valorAposDesconto / 5000);\n                const valorCotasCompletas = cotasCompletas * 5000;\n                const valorComplementarNecessario = valorAposDesconto - valorCotasCompletas;\n                console.log(\"Resto:\", resto);\n                console.log(\"Cotas completas:\", cotasCompletas);\n                console.log(\"Valor das cotas completas:\", valorCotasCompletas);\n                console.log(\"Valor complementar necess\\xe1rio:\", valorComplementarNecessario);\n                // Atualizar o valor do investimento para o valor das cotas completas + IR\n                const novoValorInvestimento = valorCotasCompletas + contractDetails.totalIR;\n                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)((novoValorInvestimento * 100).toString());\n                setValue(\"valorInvestimento\", valorFormatado);\n                // Atualizar quantidade de cotas\n                setValue(\"quotaQuantity\", cotasCompletas.toString());\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n                console.log(\"✅ showComplementMessage ativado - valor parcial permitido\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"⚠️ Valor ajustado para \".concat(cotasCompletas, \" cotas completas. Valor complementar de \").concat(new Intl.NumberFormat(\"pt-BR\", {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }).format(valorComplementarNecessario), \" ser\\xe1 necess\\xe1rio.\"));\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR,\n        setValue\n    ]);\n    // Validação em tempo real para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (valorInvestimento && modalidade) {\n            validateScpValue(valorInvestimento, modalidade);\n        }\n    }, [\n        valorInvestimento,\n        modalidade,\n        validateScpValue,\n        showComplementMessage\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, IR é opcional mas pode ser aplicado\n            setIrCalculado(true);\n            setIrOpcaoSelecionada(false); // Permitir seleção de IR para SCP\n            // NÃO resetar irDeposito e irDesconto para SCP - deixar o usuário escolher\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setIrOpcaoSelecionada(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 982,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 991,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 985,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1017,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1016,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1005,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1047,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1046,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1031,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1058,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1069,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1068,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1079,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1092,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1078,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1104,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1117,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1128,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1138,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 984,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 983,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1151,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1155,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1165,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1164,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1153,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1177,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1176,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1187,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1186,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1175,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1152,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1199,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1201,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1207,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1200,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1213,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1211,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 981,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1228,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1242,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1243,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1230,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1245,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1250,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1249,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1229,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"valorInvestimento\",\n                                            width: \"100%\",\n                                            error: !!errors.valorInvestimento || !!scpValidationError,\n                                            errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message) || scpValidationError,\n                                            label: \"Valor do Investimento\",\n                                            placeholder: \"ex: R$ 50.000,00\",\n                                            setValue: (e)=>{\n                                                const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                                setValue(\"valorInvestimento\", maskedValue, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1259,\n                                            columnNumber: 15\n                                        }, this),\n                                        showComplementMessage && scpValidationError === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-xs mt-1\",\n                                            children: \"✅ Valor parcial permitido (dep\\xf3sito adicional ser\\xe1 necess\\xe1rio)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1258,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1291,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1293,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1289,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1305,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1306,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1307,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1308,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1309,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1310,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1311,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1303,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1317,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1316,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1325,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1328,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1334,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1335,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1336,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1343,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1324,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1348,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1347,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1354,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1355,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1364,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1314,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1302,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1301,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1299,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1380,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1378,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) !== \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1391,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1390,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1389,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-900 border border-orange-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Altera\\xe7\\xe3o M\\xdaTUO → SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1401,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Como o contrato anterior era M\\xdaTUO, \\xe9 necess\\xe1rio tratar o IR acumulado. A tabela de IR ser\\xe1 exibida para que voc\\xea possa escolher como proceder com o desconto.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1400,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1399,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"A\\xe7\\xe3o Obrigat\\xf3ria:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1411,\n                                        columnNumber: 18\n                                    }, this),\n                                    ' Clique no bot\\xe3o \"\"Calcular IR\"\" acima antes de prosseguir.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1410,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1409,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-900 border border-green-500 rounded-lg p-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-200 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IR Calculado:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1421,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Agora selecione uma das op\\xe7\\xf5es abaixo \",\n                                            watch(\"modalidade\") === \"MUTUO\" ? \"(obrigat\\xf3rio)\" : \"(opcional)\",\n                                            \":\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1420,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1419,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDeposito\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDeposito\", true);\n                                                            setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Restaurar valor original se existir (caso estava com desconto)\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                            }\n                                                        } else {\n                                                            setValue(\"irDeposito\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDesconto\")); // Verifica se a outra está marcada\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1426,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1425,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDesconto\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDesconto\", true);\n                                                            setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                            const valorAtual = watch(\"valorInvestimento\");\n                                                            if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                                // Salvar valor original se ainda não foi salvo\n                                                                if (!valorOriginalInvestimento) {\n                                                                    setValorOriginalInvestimento(valorAtual);\n                                                                }\n                                                                const valorNumerico = parseValor(valorAtual);\n                                                                const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                                // Debug logs\n                                                                console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                                console.log(\"Valor atual (string):\", valorAtual);\n                                                                console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                                console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                                const valorComDesconto = valorNumerico - valorIRTabela;\n                                                                console.log(\"Valor com desconto:\", valorComDesconto);\n                                                                // Verificar se o valor com desconto é positivo\n                                                                if (valorComDesconto <= 0) {\n                                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                    setValue(\"irDesconto\", false);\n                                                                    return;\n                                                                }\n                                                                // Aplicar o valor com desconto\n                                                                // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                                const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                                console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                                console.log(\"Valor formatado:\", valorFormatado);\n                                                                setValue(\"valorInvestimento\", valorFormatado);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                    style: \"currency\",\n                                                                    currency: \"BRL\"\n                                                                }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                            } else {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                                setValue(\"irDesconto\", false);\n                                                            }\n                                                            // Mostrar aviso de adendo\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                        } else {\n                                                            setValue(\"irDesconto\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDeposito\")); // Verifica se a outra está marcada\n                                                            // Restaurar valor original se existir\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                            }\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1450,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1424,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        watch(\"irDesconto\") && valorOriginalInvestimento && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-blue-200 font-bold mb-2\",\n                                    children: \"\\uD83D\\uDCB0 Desconto de IR Aplicado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-200 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor original:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1526,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                valorOriginalInvestimento\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1526,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor total do IR (da tabela):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1527,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                new Intl.NumberFormat(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                }).format(contractDetails.totalIR)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1527,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor final (com desconto):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1528,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                watch(\"valorInvestimento\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1528,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-200 text-xs mt-2\",\n                                            children: \"ℹ️ O valor descontado \\xe9 baseado no c\\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1525,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1523,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Conforme informado no formul\\xe1rio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1541,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1540,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold mb-3\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1546,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1544,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-800 border border-yellow-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-100 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Exemplo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Contrato de R$ 10.000 com IR de R$ 2.000 = R$ 8.000 restantes.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 105\n                                            }, this),\n                                            \"• Sistema cadastra: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: [\n                                                    Math.floor(((0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(watch(\"valorInvestimento\")) - ((contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) || 0)) / 5000),\n                                                    \" cota(s)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1553,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1553,\n                                                columnNumber: 164\n                                            }, this),\n                                            \"• Valor complementar: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1554,\n                                                columnNumber: 39\n                                            }, this),\n                                            \" dever\\xe1 ser pago separadamente.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1551,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1550,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-800 border border-green-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Valor Parcial Permitido:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1559,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Neste caso, o contrato pode ser enviado com o valor atual (n\\xe3o m\\xfaltiplo de R$ 5.000), pois o sistema reconhece que haver\\xe1 um dep\\xf3sito adicional posterior para completar as cotas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1558,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1557,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1538,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1226,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1566,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1573,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1589,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1588,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1601,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1611,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1623,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1628,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1629,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1630,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1624,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1622,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1570,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1636,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1647,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1652,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1653,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1654,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1648,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1646,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1657,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1667,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1672,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1673,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1668,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1666,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1635,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1568,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1567,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1680,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1689,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1679,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1222,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1726,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1731,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1732,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1730,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1729,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1728,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1727,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1743,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1751,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1748,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1747,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1763,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1764,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1762,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1746,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1745,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1744,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"S2p+YJ6E2UICSmXYuSm0HDLU9Bk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});