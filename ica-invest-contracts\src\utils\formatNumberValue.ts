export default function formatNumberValue(value: string) {
  // Se o valor estiver vazio, undefined ou null, retornar 0
  if (!value || value.trim() === '') {
    return 0;
  }

  // Remover pontos (separadores de milhares) e substituir vírgula por ponto (decimal)
  const cleanValue = value.replaceAll('.', '').replaceAll(',', '.');

  // Se após limpeza não há nada ou só caracteres não numéricos, retornar 0
  if (cleanValue === '' || isNaN(Number(cleanValue))) {
    return 0;
  }

  return Number(cleanValue);
}