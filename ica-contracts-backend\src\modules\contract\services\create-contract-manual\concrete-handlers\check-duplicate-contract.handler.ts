import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';
import { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';
import { Repository, Equal } from 'typeorm';
import { NotificationTypeEnum } from 'src/shared/database/typeorm/entities/notification.entity';
import { AbstractContractHandler } from '../abstract-contract.handler';
import type { ContractContext } from '../contract.context';

@Injectable()
export class CheckDuplicateContractHandler extends AbstractContractHandler<
  ContractContext,
  void
> {
  private readonly logger = new Logger(CheckDuplicateContractHandler.name);

  constructor(
    @InjectRepository(ContractEntity)
    private readonly contractRepository: Repository<ContractEntity>,
    private readonly createNotificationService: CreateNotificationService,
  ) {
    super();
  }

  async handle(context: ContractContext): Promise<void> {
    this.logger.debug('Iniciando verificação de contrato duplicado.');

    const { dto } = context;
    this.logger.debug(
      `Verificando duplicidade de contrato para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`,
    );
    this.logger.debug(`IsUpgrade: ${dto.isUpgrade}, OriginalContractId: ${dto.originalContractId}`);

    try {
      const existContract = await this.contractRepository.findOne({
        where: {
          status: 'ACTIVE', 
          investor: [
            { owner: { cpf: Equal(dto.individual?.cpf) } },
            { business: { cnpj: Equal(dto.company?.cnpj) } },
          ],
        },
      });

      if (existContract) {
        // ✅ LÓGICA DE UPGRADE: Se é upgrade, marcar contrato original como expired
        if (dto.isUpgrade && dto.originalContractId) {
          this.logger.log(
            `Upgrade detectado: marcando contrato ${dto.originalContractId} como EXPIRED`,
          );

          // Verificar se o contrato original existe e está ativo
          const originalContract = await this.contractRepository.findOne({
            where: { id: dto.originalContractId, status: 'ACTIVE' },
          });

          if (!originalContract) {
            throw new BadRequestException(
              'Contrato original não encontrado ou não está ativo.',
            );
          }

          // Marcar contrato original como EXPIRED
          await this.contractRepository.update(dto.originalContractId, {
            status: 'EXPIRED',
          });

          this.logger.log(
            `Contrato ${dto.originalContractId} marcado como EXPIRED para upgrade`,
          );

          // Criar notificação de upgrade
          await this.createNotificationService.create({
            userOwnerRoleRelationId: context.userProfile.id,
            description: `Upgrade de contrato realizado. Contrato anterior foi marcado como expirado e novo contrato está sendo criado.`,
            title: `Upgrade de Contrato Realizado`,
            type: NotificationTypeEnum.DUPLICATED_DOCUMENT, // Pode criar um novo tipo se necessário
            contractId: originalContract.id,
            investorId: originalContract.investorId,
          });
        } else {
          // ✅ LÓGICA ORIGINAL: Se não é upgrade, lançar erro de duplicidade
          this.logger.warn(
            `Contrato duplicado encontrado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`,
          );

          await this.createNotificationService.create({
            userOwnerRoleRelationId: context.userProfile.id,
            description: `Foi identificada uma nova tentativa de registro de contrato utilizando um CPF já vinculado a outro contrato ativo no sistema.`,
            title: `Nova Tentativa de Duplicidade!`,
            type: NotificationTypeEnum.DUPLICATED_DOCUMENT,
            contractId: existContract.id,
            investorId: existContract.investorId,
          });

          throw new BadRequestException(
            'Já existe um contrato ativo para este documento.',
          );
        }
      }

      this.logger.log(
        `Nenhum contrato duplicado encontrado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`,
      );
      await super.handle(context);
    } catch (error) {
      this.logger.error(
        `Erro durante a verificação de contrato duplicado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`,
        error.stack,
      );
      throw new BadRequestException(error) ;
    }
  }
}
