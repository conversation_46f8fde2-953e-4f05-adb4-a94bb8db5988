{"version": 3, "file": "check-duplicate-contract.handler.js", "sourceRoot": "/", "sources": ["modules/contract/services/create-contract-manual/concrete-handlers/check-duplicate-contract.handler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAyE;AACzE,6CAAmD;AACnD,gHAA2G;AAC3G,qGAAsF;AACtF,qCAA4C;AAC5C,6GAAgG;AAChG,4EAAuE;AAIhE,IAAM,6BAA6B,qCAAnC,MAAM,6BAA8B,SAAQ,mDAGlD;IAGC,YAEE,kBAA+D,EAC9C,yBAAoD;QAErE,KAAK,EAAE,CAAC;QAHS,uBAAkB,GAAlB,kBAAkB,CAA4B;QAC9C,8BAAyB,GAAzB,yBAAyB,CAA2B;QALtD,WAAM,GAAG,IAAI,eAAM,CAAC,+BAA6B,CAAC,IAAI,CAAC,CAAC;IAQzE,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,OAAwB;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAElE,MAAM,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yDAAyD,GAAG,CAAC,UAAU,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,CACpG,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,SAAS,yBAAyB,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAEhG,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;gBAC1D,KAAK,EAAE;oBACL,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE;wBACR,EAAE,KAAK,EAAE,EAAE,GAAG,EAAE,IAAA,eAAK,EAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,EAAE;wBAC9C,EAAE,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAA,eAAK,EAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;qBACjD;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,aAAa,EAAE,CAAC;gBAElB,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,kBAAkB,EAAE,CAAC;oBAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,wCAAwC,GAAG,CAAC,kBAAkB,eAAe,CAC9E,CAAC;oBAGF,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;wBAC7D,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE;qBACxD,CAAC,CAAC;oBAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACtB,MAAM,IAAI,4BAAmB,CAC3B,qDAAqD,CACtD,CAAC;oBACJ,CAAC;oBAGD,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE;wBAC3D,MAAM,EAAE,SAAS;qBAClB,CAAC,CAAC;oBAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,YAAY,GAAG,CAAC,kBAAkB,oCAAoC,CACvE,CAAC;oBAGF,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;wBAC1C,uBAAuB,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE;wBAC/C,WAAW,EAAE,+GAA+G;wBAC5H,KAAK,EAAE,+BAA+B;wBACtC,IAAI,EAAE,0CAAoB,CAAC,mBAAmB;wBAC9C,UAAU,EAAE,gBAAgB,CAAC,EAAE;wBAC/B,UAAU,EAAE,gBAAgB,CAAC,UAAU;qBACxC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBAEN,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,mDAAmD,GAAG,CAAC,UAAU,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,CAC9F,CAAC;oBAEF,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;wBAC1C,uBAAuB,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE;wBAC/C,WAAW,EAAE,+HAA+H;wBAC5I,KAAK,EAAE,gCAAgC;wBACvC,IAAI,EAAE,0CAAoB,CAAC,mBAAmB;wBAC9C,UAAU,EAAE,aAAa,CAAC,EAAE;wBAC5B,UAAU,EAAE,aAAa,CAAC,UAAU;qBACrC,CAAC,CAAC;oBAEH,MAAM,IAAI,4BAAmB,CAC3B,kDAAkD,CACnD,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,0DAA0D,GAAG,CAAC,UAAU,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,CACrG,CAAC;YACF,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sEAAsE,GAAG,CAAC,UAAU,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,EAChH,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,4BAAmB,CAAC,KAAK,CAAC,CAAE;QACxC,CAAC;IACH,CAAC;CACF,CAAA;AAvGY,sEAA6B;wCAA7B,6BAA6B;IADzC,IAAA,mBAAU,GAAE;IAQR,WAAA,IAAA,0BAAgB,EAAC,gCAAc,CAAC,CAAA;qCACI,oBAAU;QACH,uDAAyB;GAT5D,6BAA6B,CAuGzC", "sourcesContent": ["import { Injectable, BadRequestException, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { CreateNotificationService } from 'src/modules/notifications/services/create-notification.service';\r\nimport { ContractEntity } from 'src/shared/database/typeorm/entities/contract.entity';\r\nimport { Repository, Equal } from 'typeorm';\r\nimport { NotificationTypeEnum } from 'src/shared/database/typeorm/entities/notification.entity';\r\nimport { AbstractContractHandler } from '../abstract-contract.handler';\r\nimport type { ContractContext } from '../contract.context';\r\n\r\n@Injectable()\r\nexport class CheckDuplicateContractHandler extends AbstractContractHandler<\r\n  ContractContext,\r\n  void\r\n> {\r\n  private readonly logger = new Logger(CheckDuplicateContractHandler.name);\r\n\r\n  constructor(\r\n    @InjectRepository(ContractEntity)\r\n    private readonly contractRepository: Repository<ContractEntity>,\r\n    private readonly createNotificationService: CreateNotificationService,\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  async handle(context: ContractContext): Promise<void> {\r\n    this.logger.debug('Iniciando verificação de contrato duplicado.');\r\n\r\n    const { dto } = context;\r\n    this.logger.debug(\r\n      `Verificando duplicidade de contrato para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`,\r\n    );\r\n    this.logger.debug(`IsUpgrade: ${dto.isUpgrade}, OriginalContractId: ${dto.originalContractId}`);\r\n\r\n    try {\r\n      const existContract = await this.contractRepository.findOne({\r\n        where: {\r\n          status: 'ACTIVE', \r\n          investor: [\r\n            { owner: { cpf: Equal(dto.individual?.cpf) } },\r\n            { business: { cnpj: Equal(dto.company?.cnpj) } },\r\n          ],\r\n        },\r\n      });\r\n\r\n      if (existContract) {\r\n        // ✅ LÓGICA DE UPGRADE: Se é upgrade, marcar contrato original como expired\r\n        if (dto.isUpgrade && dto.originalContractId) {\r\n          this.logger.log(\r\n            `Upgrade detectado: marcando contrato ${dto.originalContractId} como EXPIRED`,\r\n          );\r\n\r\n          // Verificar se o contrato original existe e está ativo\r\n          const originalContract = await this.contractRepository.findOne({\r\n            where: { id: dto.originalContractId, status: 'ACTIVE' },\r\n          });\r\n\r\n          if (!originalContract) {\r\n            throw new BadRequestException(\r\n              'Contrato original não encontrado ou não está ativo.',\r\n            );\r\n          }\r\n\r\n          // Marcar contrato original como EXPIRED\r\n          await this.contractRepository.update(dto.originalContractId, {\r\n            status: 'EXPIRED',\r\n          });\r\n\r\n          this.logger.log(\r\n            `Contrato ${dto.originalContractId} marcado como EXPIRED para upgrade`,\r\n          );\r\n\r\n          // Criar notificação de upgrade\r\n          await this.createNotificationService.create({\r\n            userOwnerRoleRelationId: context.userProfile.id,\r\n            description: `Upgrade de contrato realizado. Contrato anterior foi marcado como expirado e novo contrato está sendo criado.`,\r\n            title: `Upgrade de Contrato Realizado`,\r\n            type: NotificationTypeEnum.DUPLICATED_DOCUMENT, // Pode criar um novo tipo se necessário\r\n            contractId: originalContract.id,\r\n            investorId: originalContract.investorId,\r\n          });\r\n        } else {\r\n          // ✅ LÓGICA ORIGINAL: Se não é upgrade, lançar erro de duplicidade\r\n          this.logger.warn(\r\n            `Contrato duplicado encontrado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`,\r\n          );\r\n\r\n          await this.createNotificationService.create({\r\n            userOwnerRoleRelationId: context.userProfile.id,\r\n            description: `Foi identificada uma nova tentativa de registro de contrato utilizando um CPF já vinculado a outro contrato ativo no sistema.`,\r\n            title: `Nova Tentativa de Duplicidade!`,\r\n            type: NotificationTypeEnum.DUPLICATED_DOCUMENT,\r\n            contractId: existContract.id,\r\n            investorId: existContract.investorId,\r\n          });\r\n\r\n          throw new BadRequestException(\r\n            'Já existe um contrato ativo para este documento.',\r\n          );\r\n        }\r\n      }\r\n\r\n      this.logger.log(\r\n        `Nenhum contrato duplicado encontrado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`,\r\n      );\r\n      await super.handle(context);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Erro durante a verificação de contrato duplicado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`,\r\n        error.stack,\r\n      );\r\n      throw new BadRequestException(error) ;\r\n    }\r\n  }\r\n}\r\n"]}