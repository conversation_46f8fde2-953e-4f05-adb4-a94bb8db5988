"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            console.log(\"\\uD83D\\uDD0D Dados do contrato carregados:\", response.data);\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [irOpcaoSelecionada, setIrOpcaoSelecionada] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se uma opção de IR foi selecionada\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    const [scpValidationError, setScpValidationError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Erro de validação SCP customizado\n    // Função para validar valor SCP\n    const validateScpValue = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)((valor, modalidade)=>{\n        if (modalidade !== \"SCP\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Se o valor estiver vazio, limpar erro e retornar true (será validado pelo Yup)\n        if (!valor || valor.trim() === \"\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        const valorNumerico = Number(valor.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n        // Se há mensagem de complemento, valor parcial é permitido\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - showComplementMessage:\", showComplementMessage);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valor:\", valor);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valorNumerico:\", valorNumerico);\n        if (showComplementMessage) {\n            console.log(\"✅ SCP: Valor parcial permitido devido ao dep\\xf3sito adicional\");\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Validação normal: múltiplo de 5.000 e mínimo de 30.000\n        if (valorNumerico < 30000) {\n            console.log(\"❌ SCP: Valor menor que R$ 30.000\");\n            setScpValidationError(\"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\");\n            return false;\n        }\n        console.log(\"✅ SCP: Valor v\\xe1lido\");\n        setScpValidationError(\"\");\n        return true;\n    }, [\n        showComplementMessage\n    ]);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                console.log(\"\\uD83D\\uDD0D Debug Valor Investimento:\", {\n                    valorOriginal: contract.investmentValue,\n                    valorString: valorInvestimento,\n                    valorComMascara: valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\"\n                });\n                // Aplicar máscara no valor do investimento se houver valor\n                // Se o valor vem como número, converter para centavos antes da máscara\n                let valorFormatado = \"\";\n                if (valorInvestimento) {\n                    // Se é um número, multiplicar por 100 para converter para centavos\n                    if (typeof valorInvestimento === \"number\" || !isNaN(Number(valorInvestimento))) {\n                        const valorEmCentavos = Math.round(Number(valorInvestimento) * 100);\n                        valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                    } else {\n                        // Se já é string formatada, usar diretamente\n                        valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento);\n                    }\n                }\n                setValue(\"valorInvestimento\", valorFormatado);\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\") {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            if (valorNumerico > 0) {\n                const cotas = Math.floor(valorNumerico / 5000);\n                setValue(\"quotaQuantity\", cotas.toString());\n            } else {\n                setValue(\"quotaQuantity\", \"0\");\n            }\n        } else if (modalidade === \"SCP\") {\n            // Se não há valor ou valor está vazio, limpar cotas\n            setValue(\"quotaQuantity\", \"0\");\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 396,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando há mudança de MUTUO para SCP\n        if (data.modalidade === \"MUTUO\" || data.modalidade === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validação específica para SCP usando função customizada\n        if (!validateScpValue(data.valorInvestimento, data.modalidade)) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(scpValidationError);\n            return;\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Verificar se é realmente um upgrade (tem tipo definido)\n            const isUpgrade = !!tipo;\n            let originalContractId = null;\n            if (isUpgrade) {\n                // Buscar contrato ativo para upgrade\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - contractData completo:\", JSON.stringify(contractData, null, 2));\n                if (!(contractData === null || contractData === void 0 ? void 0 : contractData.contracts) || contractData.contracts.length === 0) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: Nenhum contrato encontrado para este investidor.\");\n                    return;\n                }\n                // Procurar por contrato ativo\n                const activeContract = contractData.contracts.find((contract)=>{\n                    var _contract_contractStatus;\n                    const status = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n                    return status === \"ACTIVE\" || status === \"ATIVO\";\n                });\n                if (!activeContract) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: Nenhum contrato ativo encontrado. Apenas contratos ativos podem ser alterados.\");\n                    return;\n                }\n                originalContractId = activeContract.id;\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - Contrato ativo encontrado:\", activeContract);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - originalContractId:\", originalContractId);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - tipo:\", tipo);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - originalContractId \\xe9 UUID?\", /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(originalContractId));\n                if (!originalContractId) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: ID do contrato ativo n\\xe3o encontrado. Estrutura de dados inv\\xe1lida.\");\n                    return;\n                }\n            }\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                // ✅ CAMPOS DE UPGRADE - Só incluir se for realmente um upgrade\n                ...isUpgrade && {\n                    isUpgrade: true,\n                    originalContractId: originalContractId,\n                    upgradeType: tipo\n                },\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"=== DADOS DE UPGRADE ===\");\n            console.log(\"\\xc9 upgrade?\", isUpgrade);\n            console.log(\"IsUpgrade:\", requestData.isUpgrade);\n            console.log(\"OriginalContractId:\", requestData.originalContractId);\n            console.log(\"UpgradeType:\", requestData.upgradeType);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        // Debug: verificar qual campo contém o tipo do contrato\n        console.log(\"\\uD83D\\uDD0D Debug Contract Type Fields:\", {\n            tags: contract.tags,\n            contractType: contract.contractType,\n            type: contract.type,\n            investment: contract.investment,\n            investmentValue: contract.investmentValue,\n            quotesAmount: contract.quotesAmount,\n            fullContract: contract\n        });\n        // Determinar o tipo REAL do contrato baseado nas regras de negócio:\n        // - SCP: valor fixo de R$ 5.000 por cota, mínimo R$ 30.000 (6 cotas)\n        // - MÚTUO: valor livre\n        const contractValue = parseFloat(contract.investmentValue) || 0;\n        const quotesAmount = parseInt(contract.quotesAmount) || 0;\n        let realContractType = \"MUTUO\"; // padrão\n        // Se tem cotas e o valor é múltiplo de 5000, provavelmente é SCP\n        if (quotesAmount > 0 && contractValue >= 30000 && contractValue % 5000 === 0) {\n            realContractType = \"SCP\";\n        }\n        console.log(\"\\uD83D\\uDD0D Contract Analysis:\", {\n            tagsFromAPI: contract.tags,\n            contractValue,\n            quotesAmount,\n            isMultipleOf5000: contractValue % 5000 === 0,\n            realContractType\n        });\n        return {\n            details,\n            totalIR,\n            contractType: realContractType\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D UseEffect SCP - Condi\\xe7\\xf5es:\");\n        console.log(\"  modalidade === 'SCP':\", modalidade === \"SCP\");\n        console.log(\"  valorInvestimento:\", valorInvestimento);\n        console.log(\"  valorInvestimento.trim() !== '':\", valorInvestimento && valorInvestimento.trim() !== \"\");\n        console.log(\"  irDesconto:\", irDesconto);\n        console.log(\"  contractDetails?.totalIR:\", contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR);\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\" && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            console.log(\"=== C\\xc1LCULO SCP COM DESCONTO IR ===\");\n            console.log(\"Valor original:\", valorNumerico);\n            console.log(\"IR a descontar:\", contractDetails.totalIR);\n            console.log(\"Valor ap\\xf3s desconto:\", valorAposDesconto);\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                // Arredondar para BAIXO (menos cotas) - pegar apenas cotas completas\n                const cotasCompletas = Math.floor(valorAposDesconto / 5000);\n                const valorCotasCompletas = cotasCompletas * 5000;\n                const valorComplementarNecessario = valorAposDesconto - valorCotasCompletas;\n                console.log(\"Resto:\", resto);\n                console.log(\"Cotas completas:\", cotasCompletas);\n                console.log(\"Valor das cotas completas:\", valorCotasCompletas);\n                console.log(\"Valor complementar necess\\xe1rio:\", valorComplementarNecessario);\n                // Atualizar o valor do investimento para o valor das cotas completas + IR\n                const novoValorInvestimento = valorCotasCompletas + contractDetails.totalIR;\n                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)((novoValorInvestimento * 100).toString());\n                setValue(\"valorInvestimento\", valorFormatado);\n                // Atualizar quantidade de cotas\n                setValue(\"quotaQuantity\", cotasCompletas.toString());\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n                console.log(\"✅ showComplementMessage ativado - valor parcial permitido\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"⚠️ Valor ajustado para \".concat(cotasCompletas, \" cotas completas. Valor complementar de \").concat(new Intl.NumberFormat(\"pt-BR\", {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }).format(valorComplementarNecessario), \" ser\\xe1 necess\\xe1rio.\"));\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR,\n        setValue\n    ]);\n    // Validação em tempo real para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (valorInvestimento && modalidade) {\n            validateScpValue(valorInvestimento, modalidade);\n        }\n    }, [\n        valorInvestimento,\n        modalidade,\n        validateScpValue,\n        showComplementMessage\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, IR é opcional mas pode ser aplicado\n            setIrCalculado(true);\n            setIrOpcaoSelecionada(false); // Permitir seleção de IR para SCP\n            // NÃO resetar irDeposito e irDesconto para SCP - deixar o usuário escolher\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setIrOpcaoSelecionada(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1061,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1070,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1071,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1064,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1075,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1074,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1085,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1096,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1095,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1084,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1126,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1138,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1148,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1159,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1172,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1183,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1196,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1207,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1206,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1217,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1063,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1062,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1230,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1234,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1233,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1244,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1243,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1232,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1256,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1255,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1266,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1265,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1254,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1231,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1278,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1280,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1279,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1292,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1290,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1060,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1307,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1321,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1309,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1324,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1308,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"valorInvestimento\",\n                                            width: \"100%\",\n                                            error: !!errors.valorInvestimento || !!scpValidationError,\n                                            errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message) || scpValidationError,\n                                            label: \"Valor do Investimento\",\n                                            placeholder: \"ex: R$ 50.000,00\",\n                                            setValue: (e)=>{\n                                                const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                                setValue(\"valorInvestimento\", maskedValue, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1330,\n                                            columnNumber: 15\n                                        }, this),\n                                        showComplementMessage && scpValidationError === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-xs mt-1\",\n                                            children: \"✅ Valor parcial permitido (dep\\xf3sito adicional ser\\xe1 necess\\xe1rio)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1345,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1329,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1354,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1353,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1306,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1362,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1360,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1371,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1377,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1378,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1379,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1380,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1381,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1382,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1375,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1374,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1388,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1387,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1399,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1406,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1407,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1408,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1414,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1395,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1419,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1418,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1425,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1426,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1424,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1436,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1385,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1373,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1372,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1370,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center text-white text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                className: \"mr-2\",\n                                                checked: watch(\"irDeposito\"),\n                                                onChange: (e)=>{\n                                                    if (e.target.checked) {\n                                                        setValue(\"irDeposito\", true);\n                                                        setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                        setIrOpcaoSelecionada(true);\n                                                        // Restaurar valor original se existir (caso estava com desconto)\n                                                        if (valorOriginalInvestimento) {\n                                                            setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                        }\n                                                    } else {\n                                                        setValue(\"irDeposito\", false);\n                                                        setIrOpcaoSelecionada(!!watch(\"irDesconto\")); // Verifica se a outra está marcada\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1461,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1460,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center text-white text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                className: \"mr-2\",\n                                                checked: watch(\"irDesconto\"),\n                                                onChange: (e)=>{\n                                                    if (e.target.checked) {\n                                                        setValue(\"irDesconto\", true);\n                                                        setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                        setIrOpcaoSelecionada(true);\n                                                        // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                        const valorAtual = watch(\"valorInvestimento\");\n                                                        if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                            // Salvar valor original se ainda não foi salvo\n                                                            if (!valorOriginalInvestimento) {\n                                                                setValorOriginalInvestimento(valorAtual);\n                                                            }\n                                                            const valorNumerico = parseValor(valorAtual);\n                                                            const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                            // Debug logs\n                                                            console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                            console.log(\"Valor atual (string):\", valorAtual);\n                                                            console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                            console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                            const valorComDesconto = valorNumerico - valorIRTabela;\n                                                            console.log(\"Valor com desconto:\", valorComDesconto);\n                                                            // Verificar se o valor com desconto é positivo\n                                                            if (valorComDesconto <= 0) {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                setValue(\"irDesconto\", false);\n                                                                return;\n                                                            }\n                                                            // Aplicar o valor com desconto\n                                                            // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                            const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                            const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                            console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                            console.log(\"Valor formatado:\", valorFormatado);\n                                                            setValue(\"valorInvestimento\", valorFormatado);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                style: \"currency\",\n                                                                currency: \"BRL\"\n                                                            }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                        } else {\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                            setValue(\"irDesconto\", false);\n                                                        }\n                                                        // Mostrar aviso de adendo\n                                                        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                    } else {\n                                                        setValue(\"irDesconto\", false);\n                                                        setIrOpcaoSelecionada(!!watch(\"irDeposito\")); // Verifica se a outra está marcada\n                                                        // Restaurar valor original se existir\n                                                        if (valorOriginalInvestimento) {\n                                                            setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                        }\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1484,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1459,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false),\n                        watch(\"irDesconto\") && valorOriginalInvestimento && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-blue-200 font-bold mb-2\",\n                                    children: \"\\uD83D\\uDCB0 Desconto de IR Aplicado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1559,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-200 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor original:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1561,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                valorOriginalInvestimento\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1561,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor total do IR (da tabela):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1562,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                new Intl.NumberFormat(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                }).format(contractDetails.totalIR)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1562,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor final (com desconto):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1563,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                watch(\"valorInvestimento\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1563,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-200 text-xs mt-2\",\n                                            children: \"ℹ️ O valor descontado \\xe9 baseado no c\\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1564,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1560,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1558,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1574,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Conforme informado no formul\\xe1rio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1576,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1575,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold mb-3\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1581,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1579,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-800 border border-yellow-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-100 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Exemplo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1587,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Contrato de R$ 10.000 com IR de R$ 2.000 = R$ 8.000 restantes.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1587,\n                                                columnNumber: 105\n                                            }, this),\n                                            \"• Sistema cadastra: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: [\n                                                    Math.floor(((0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(watch(\"valorInvestimento\")) - ((contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) || 0)) / 5000),\n                                                    \" cota(s)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1588,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1588,\n                                                columnNumber: 164\n                                            }, this),\n                                            \"• Valor complementar: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1589,\n                                                columnNumber: 39\n                                            }, this),\n                                            \" dever\\xe1 ser pago separadamente.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1586,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1585,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-800 border border-green-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Valor Parcial Permitido:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1594,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Neste caso, o contrato pode ser enviado com o valor atual (n\\xe3o m\\xfaltiplo de R$ 5.000), pois o sistema reconhece que haver\\xe1 um dep\\xf3sito adicional posterior para completar as cotas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1593,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1592,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1573,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1305,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1601,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1608,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1624,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1636,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1646,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1658,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1663,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1664,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1665,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1659,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1657,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1605,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1671,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1682,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1687,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1688,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1689,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1683,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1681,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1692,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1702,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1707,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1708,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1703,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1701,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1670,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1603,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1602,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1715,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1724,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1714,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1301,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1761,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1766,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1767,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1765,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1764,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1763,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1762,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1778,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1786,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1783,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1782,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1798,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1799,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1797,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1781,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1780,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1779,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"S2p+YJ6E2UICSmXYuSm0HDLU9Bk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});