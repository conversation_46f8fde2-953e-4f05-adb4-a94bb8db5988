"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-validation\", \"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            // Verificar se é múltiplo de 5000\n            return numericValue > 0 && numericValue % 5000 === 0;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            console.log(\"\\uD83D\\uDD0D Dados do contrato carregados:\", response.data);\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    const [scpValidationError, setScpValidationError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Erro de validação SCP customizado\n    // Função para validar valor SCP\n    const validateScpValue = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)((valor, modalidade)=>{\n        if (modalidade !== \"SCP\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Se o valor estiver vazio, limpar erro e retornar true (será validado pelo Yup)\n        if (!valor || valor.trim() === \"\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        const valorNumerico = Number(valor.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valor:\", valor);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valorNumerico:\", valorNumerico);\n        // Validação: deve ser múltiplo de 5.000\n        if (valorNumerico <= 0) {\n            console.log(\"❌ SCP: Valor deve ser maior que zero\");\n            setScpValidationError(\"Para contratos SCP, o valor deve ser maior que zero\");\n            return false;\n        }\n        if (valorNumerico % 5000 !== 0) {\n            console.log(\"❌ SCP: Valor n\\xe3o \\xe9 m\\xfaltiplo de R$ 5.000\");\n            setScpValidationError(\"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000 (ex: R$ 5.000, R$ 10.000, R$ 15.000...)\");\n            return false;\n        }\n        console.log(\"✅ SCP: Valor v\\xe1lido - m\\xfaltiplo de R$ 5.000\");\n        setScpValidationError(\"\");\n        return true;\n    }, []);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                console.log(\"\\uD83D\\uDD0D Debug Valor Investimento:\", {\n                    valorOriginal: contract.investmentValue,\n                    valorString: valorInvestimento,\n                    valorComMascara: valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\"\n                });\n                // Aplicar máscara no valor do investimento se houver valor\n                // Se o valor vem como número, converter para centavos antes da máscara\n                let valorFormatado = \"\";\n                if (valorInvestimento) {\n                    // Se é um número, multiplicar por 100 para converter para centavos\n                    if (typeof valorInvestimento === \"number\" || !isNaN(Number(valorInvestimento))) {\n                        const valorEmCentavos = Math.round(Number(valorInvestimento) * 100);\n                        valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                    } else {\n                        // Se já é string formatada, usar diretamente\n                        valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento);\n                    }\n                }\n                setValue(\"valorInvestimento\", valorFormatado);\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\") {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            if (valorNumerico > 0 && valorNumerico % 5000 === 0) {\n                // Só calcular cotas se for múltiplo exato de 5000\n                const cotas = valorNumerico / 5000;\n                setValue(\"quotaQuantity\", cotas.toString());\n            } else {\n                setValue(\"quotaQuantity\", \"0\");\n            }\n        } else if (modalidade === \"SCP\") {\n            // Se não há valor ou valor está vazio, limpar cotas\n            setValue(\"quotaQuantity\", \"0\");\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 393,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando há mudança de MUTUO para SCP\n        if (data.modalidade === \"MUTUO\" || data.modalidade === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validação específica para SCP usando função customizada\n        if (!validateScpValue(data.valorInvestimento, data.modalidade)) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(scpValidationError);\n            return;\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Verificar se é realmente um upgrade (tem tipo definido)\n            const isUpgrade = !!tipo;\n            let originalContractId = null;\n            if (isUpgrade) {\n                // Buscar contrato ativo para upgrade\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - contractData completo:\", JSON.stringify(contractData, null, 2));\n                if (!(contractData === null || contractData === void 0 ? void 0 : contractData.contracts) || contractData.contracts.length === 0) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: Nenhum contrato encontrado para este investidor.\");\n                    return;\n                }\n                // Procurar por contrato ativo\n                const activeContract = contractData.contracts.find((contract)=>{\n                    var _contract_contractStatus;\n                    const status = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n                    return status === \"ACTIVE\" || status === \"ATIVO\";\n                });\n                if (!activeContract) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: Nenhum contrato ativo encontrado. Apenas contratos ativos podem ser alterados.\");\n                    return;\n                }\n                originalContractId = activeContract.id;\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - Contrato ativo encontrado:\", activeContract);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - originalContractId:\", originalContractId);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - tipo:\", tipo);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - originalContractId \\xe9 UUID?\", /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(originalContractId));\n                if (!originalContractId) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: ID do contrato ativo n\\xe3o encontrado. Estrutura de dados inv\\xe1lida.\");\n                    return;\n                }\n            }\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                // ✅ CAMPOS DE UPGRADE - Só incluir se for realmente um upgrade\n                ...isUpgrade && {\n                    isUpgrade: true,\n                    originalContractId: originalContractId,\n                    upgradeType: tipo\n                },\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"=== DADOS DE UPGRADE ===\");\n            console.log(\"\\xc9 upgrade?\", isUpgrade);\n            console.log(\"IsUpgrade:\", requestData.isUpgrade);\n            console.log(\"OriginalContractId:\", requestData.originalContractId);\n            console.log(\"UpgradeType:\", requestData.upgradeType);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        // Debug: verificar qual campo contém o tipo do contrato\n        console.log(\"\\uD83D\\uDD0D Debug Contract Type Fields:\", {\n            tags: contract.tags,\n            contractType: contract.contractType,\n            type: contract.type,\n            investment: contract.investment,\n            investmentValue: contract.investmentValue,\n            quotesAmount: contract.quotesAmount,\n            fullContract: contract\n        });\n        // Determinar o tipo REAL do contrato baseado nas regras de negócio:\n        // - SCP: valor múltiplo de R$ 5.000 por cota\n        // - MÚTUO: valor livre\n        const contractValue = parseFloat(contract.investmentValue) || 0;\n        const quotesAmount = parseInt(contract.quotesAmount) || 0;\n        let realContractType = \"MUTUO\"; // padrão\n        // Se tem cotas e o valor é múltiplo de 5000, provavelmente é SCP\n        if (quotesAmount > 0 && contractValue > 0 && contractValue % 5000 === 0) {\n            realContractType = \"SCP\";\n        }\n        console.log(\"\\uD83D\\uDD0D Contract Analysis:\", {\n            tagsFromAPI: contract.tags,\n            contractValue,\n            quotesAmount,\n            isMultipleOf5000: contractValue % 5000 === 0,\n            realContractType\n        });\n        return {\n            details,\n            totalIR,\n            contractType: realContractType\n        };\n    }, [\n        contractData\n    ]);\n    // Validação em tempo real para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (valorInvestimento && modalidade) {\n            validateScpValue(valorInvestimento, modalidade);\n        }\n    }, [\n        valorInvestimento,\n        modalidade,\n        validateScpValue\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, IR é opcional mas pode ser aplicado\n            setIrCalculado(true);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 999,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1009,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1004,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1013,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1012,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1024,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1033,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1022,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1064,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1048,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1076,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1075,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1086,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1085,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1097,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1096,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1095,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1122,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1121,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1134,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1155,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1001,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1000,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1168,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1171,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1182,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1181,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1170,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1193,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1204,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1203,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1169,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1216,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1218,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1224,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1217,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1230,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1228,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 998,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1259,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1260,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1247,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1262,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1246,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        placeholder: \"ex: R$ 50.000,00\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1267,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1287,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1244,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1295,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1293,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1309,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1310,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1311,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1312,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1314,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1315,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1308,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1321,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1320,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1329,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1332,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1338,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1339,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1340,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1341,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1347,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1328,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1352,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1351,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1358,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1359,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1357,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1369,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1368,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1306,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1305,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1303,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center text-white text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                className: \"mr-2\",\n                                                checked: watch(\"irDeposito\"),\n                                                onChange: (e)=>{\n                                                    if (e.target.checked) {\n                                                        setValue(\"irDeposito\", true);\n                                                        setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                        // Restaurar valor original se existir (caso estava com desconto)\n                                                        if (valorOriginalInvestimento) {\n                                                            setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                        }\n                                                    } else {\n                                                        setValue(\"irDeposito\", false);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1394,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center text-white text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                className: \"mr-2\",\n                                                checked: watch(\"irDesconto\"),\n                                                onChange: (e)=>{\n                                                    if (e.target.checked) {\n                                                        setValue(\"irDesconto\", true);\n                                                        setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                        // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                        const valorAtual = watch(\"valorInvestimento\");\n                                                        if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                            // Salvar valor original se ainda não foi salvo\n                                                            if (!valorOriginalInvestimento) {\n                                                                setValorOriginalInvestimento(valorAtual);\n                                                            }\n                                                            const valorNumerico = parseValor(valorAtual);\n                                                            const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                            // Debug logs\n                                                            console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                            console.log(\"Valor atual (string):\", valorAtual);\n                                                            console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                            console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                            const valorComDesconto = valorNumerico - valorIRTabela;\n                                                            console.log(\"Valor com desconto:\", valorComDesconto);\n                                                            // Verificar se o valor com desconto é positivo\n                                                            if (valorComDesconto <= 0) {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                setValue(\"irDesconto\", false);\n                                                                return;\n                                                            }\n                                                            // Aplicar o valor com desconto\n                                                            // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                            const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                            const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                            console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                            console.log(\"Valor formatado:\", valorFormatado);\n                                                            setValue(\"valorInvestimento\", valorFormatado);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                style: \"currency\",\n                                                                currency: \"BRL\"\n                                                            }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                        } else {\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                            setValue(\"irDesconto\", false);\n                                                        }\n                                                        // Mostrar aviso de adendo\n                                                        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                    } else {\n                                                        setValue(\"irDesconto\", false);\n                                                        // Restaurar valor original se existir\n                                                        if (valorOriginalInvestimento) {\n                                                            setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                        }\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1418,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1417,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1392,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1243,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1493,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1500,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1516,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1528,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1538,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1550,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1555,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1556,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1557,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1551,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1549,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1497,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1563,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1574,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1579,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1580,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1581,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1575,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1573,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1584,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1594,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1599,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1600,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1595,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1593,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1562,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1495,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1494,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1607,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1616,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1606,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1239,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1653,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1658,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1659,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1657,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1656,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1655,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1654,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1670,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1678,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1675,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1674,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1690,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1691,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1689,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1673,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1672,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1671,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"UKHvDMHD1DiKiEIfSCmHr2m7fLs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});