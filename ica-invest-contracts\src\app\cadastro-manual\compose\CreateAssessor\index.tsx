import SelectCustom from "@/components/SelectCustom"
import { useState, useEffect } from "react";
import SelectSearch from "@/components/SelectSearch";
import { getUserProfile, getUser } from "@/functions/getUserData";
import { useQuery } from "@tanstack/react-query";
import api from "@/core/api";
import { cnpjMask } from "@/utils/masks";
import AssessorRegisterPf from "./registerForms/AssessorRegisterPf";
import AssessorRegisterPj from "./registerForms/AssessorRegisterPj";

export const AssessorCreate = () => {
    const [typeAccount, setTypeAccount] = useState('pf')
    const userProfile = getUserProfile();
    const user = getUser();

    // ✅ CORREÇÃO: Usar roleId em vez de user.id
    const [brokerId, setBrokerId] = useState(
        userProfile.name === "broker" ? userProfile.roleId : ""
    );

    const { data: brokers = [] } = useQuery({
        queryKey: ["brokers", userProfile.name],
        queryFn: async () => {
            const response = await api.get(
                userProfile.name === "superadmin"
                    ? "/wallets/list-brokers"
                    : "/wallets/admin/brokers"
            );
            return response.data;
        },
        enabled: userProfile.name !== "advisor" && userProfile.name !== "broker",
    });

    // Debug logs removidos - problema resolvido
    // console.log("user:", user.id);
    // console.log("brokers:", brokers);


    return (
        <div>
            <div className="m-3">
                <div className=' mb-5 flex items-center gap-4'>
                    <div className="mb-5">
                        <p className="text-white mb-1">Tipo de conta</p>
                        <SelectCustom
                            value={typeAccount}
                            onChange={({ target }) => setTypeAccount(target.value)}
                        >
                            <option value={'pf'}>Pessoa Física</option>
                            <option value={'pj'}>Pessoa Jurídica</option>
                        </SelectCustom>
                    </div>
                    {/* Só mostra o select se for superadmin */}
                    {userProfile.name === "superadmin" && (
                        <div className="md:w-3/4 mb-5">
                            <SelectSearch
                                label="Vincular ao Broker"
                                items={brokers}
                                value={brokerId}
                                setValue={setBrokerId}
                            />
                        </div>
                    )}
                </div>

            </div>

            {typeAccount === 'pj' ? <AssessorRegisterPj typeCreate="advisor" brokerId={brokerId} /> : <AssessorRegisterPf typeCreate="advisor" brokerId={brokerId} />}
        </div>
    );

}