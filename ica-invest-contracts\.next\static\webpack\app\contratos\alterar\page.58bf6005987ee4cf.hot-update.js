"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [irOpcaoSelecionada, setIrOpcaoSelecionada] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se uma opção de IR foi selecionada\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    const [scpValidationError, setScpValidationError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Erro de validação SCP customizado\n    // Função para validar valor SCP\n    const validateScpValue = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)((valor, modalidade)=>{\n        if (modalidade !== \"SCP\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Se o valor estiver vazio, limpar erro e retornar true (será validado pelo Yup)\n        if (!valor || valor.trim() === \"\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        const valorNumerico = Number(valor.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n        // Se há mensagem de complemento, valor parcial é permitido\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - showComplementMessage:\", showComplementMessage);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valor:\", valor);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valorNumerico:\", valorNumerico);\n        if (showComplementMessage) {\n            console.log(\"✅ SCP: Valor parcial permitido devido ao dep\\xf3sito adicional\");\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Validação normal: múltiplo de 5.000 e mínimo de 30.000\n        if (valorNumerico < 30000) {\n            console.log(\"❌ SCP: Valor menor que R$ 30.000\");\n            setScpValidationError(\"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\");\n            return false;\n        }\n        console.log(\"✅ SCP: Valor v\\xe1lido\");\n        setScpValidationError(\"\");\n        return true;\n    }, [\n        showComplementMessage\n    ]);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\") {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            if (valorNumerico > 0) {\n                const cotas = Math.floor(valorNumerico / 5000);\n                setValue(\"quotaQuantity\", cotas.toString());\n            } else {\n                setValue(\"quotaQuantity\", \"0\");\n            }\n        } else if (modalidade === \"SCP\") {\n            // Se não há valor ou valor está vazio, limpar cotas\n            setValue(\"quotaQuantity\", \"0\");\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 413,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 377,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando contrato anterior era MUTUO\n        if (data.modalidade === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validação específica para SCP usando função customizada\n        if (!validateScpValue(data.valorInvestimento, data.modalidade)) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(scpValidationError);\n            return;\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            var _contractData_contracts_, _contractData_contracts;\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Validar se temos o ID do contrato original para upgrade\n            const originalContractId = contractData === null || contractData === void 0 ? void 0 : (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.idContrato;\n            console.log(\"\\uD83D\\uDD0D Debug Upgrade - contractData:\", contractData);\n            console.log(\"\\uD83D\\uDD0D Debug Upgrade - originalContractId:\", originalContractId);\n            console.log(\"\\uD83D\\uDD0D Debug Upgrade - tipo:\", tipo);\n            if (!originalContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: ID do contrato original n\\xe3o encontrado. N\\xe3o \\xe9 poss\\xedvel realizar upgrade.\");\n                return;\n            }\n            if (!tipo) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: Tipo de upgrade n\\xe3o definido. Acesse via op\\xe7\\xf5es de upgrade.\");\n                return;\n            }\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                // ✅ CAMPOS DE UPGRADE - Identificar que é um upgrade e qual contrato deve ser expirado\n                isUpgrade: true,\n                originalContractId: originalContractId,\n                upgradeType: tipo,\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"=== DADOS DE UPGRADE ===\");\n            console.log(\"IsUpgrade:\", requestData.isUpgrade);\n            console.log(\"OriginalContractId:\", requestData.originalContractId);\n            console.log(\"UpgradeType:\", requestData.upgradeType);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D UseEffect SCP - Condi\\xe7\\xf5es:\");\n        console.log(\"  modalidade === 'SCP':\", modalidade === \"SCP\");\n        console.log(\"  valorInvestimento:\", valorInvestimento);\n        console.log(\"  valorInvestimento.trim() !== '':\", valorInvestimento && valorInvestimento.trim() !== \"\");\n        console.log(\"  irDesconto:\", irDesconto);\n        console.log(\"  contractDetails?.totalIR:\", contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR);\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\" && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            console.log(\"=== C\\xc1LCULO SCP COM DESCONTO IR ===\");\n            console.log(\"Valor original:\", valorNumerico);\n            console.log(\"IR a descontar:\", contractDetails.totalIR);\n            console.log(\"Valor ap\\xf3s desconto:\", valorAposDesconto);\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                // Arredondar para BAIXO (menos cotas) - pegar apenas cotas completas\n                const cotasCompletas = Math.floor(valorAposDesconto / 5000);\n                const valorCotasCompletas = cotasCompletas * 5000;\n                const valorComplementarNecessario = valorAposDesconto - valorCotasCompletas;\n                console.log(\"Resto:\", resto);\n                console.log(\"Cotas completas:\", cotasCompletas);\n                console.log(\"Valor das cotas completas:\", valorCotasCompletas);\n                console.log(\"Valor complementar necess\\xe1rio:\", valorComplementarNecessario);\n                // Atualizar o valor do investimento para o valor das cotas completas + IR\n                const novoValorInvestimento = valorCotasCompletas + contractDetails.totalIR;\n                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)((novoValorInvestimento * 100).toString());\n                setValue(\"valorInvestimento\", valorFormatado);\n                // Atualizar quantidade de cotas\n                setValue(\"quotaQuantity\", cotasCompletas.toString());\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n                console.log(\"✅ showComplementMessage ativado - valor parcial permitido\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"⚠️ Valor ajustado para \".concat(cotasCompletas, \" cotas completas. Valor complementar de \").concat(new Intl.NumberFormat(\"pt-BR\", {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }).format(valorComplementarNecessario), \" ser\\xe1 necess\\xe1rio.\"));\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR,\n        setValue\n    ]);\n    // Validação em tempo real para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (valorInvestimento && modalidade) {\n            validateScpValue(valorInvestimento, modalidade);\n        }\n    }, [\n        valorInvestimento,\n        modalidade,\n        validateScpValue,\n        showComplementMessage\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, IR é opcional mas pode ser aplicado\n            setIrCalculado(true);\n            setIrOpcaoSelecionada(false); // Permitir seleção de IR para SCP\n            // NÃO resetar irDeposito e irDesconto para SCP - deixar o usuário escolher\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setIrOpcaoSelecionada(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 987,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 996,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 997,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 992,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 990,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1001,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1000,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1011,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1022,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1010,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1038,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1037,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1052,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1051,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1064,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1073,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1084,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1098,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1083,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1110,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1122,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1143,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1142,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 989,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 988,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1156,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1160,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1170,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1169,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1158,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1182,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1181,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1192,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1191,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1180,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1157,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1204,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1206,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1205,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1218,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1216,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 986,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1233,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1247,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1248,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1235,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1250,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1255,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1254,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1234,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"valorInvestimento\",\n                                            width: \"100%\",\n                                            error: !!errors.valorInvestimento || !!scpValidationError,\n                                            errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message) || scpValidationError,\n                                            label: \"Valor do Investimento\",\n                                            placeholder: \"ex: R$ 50.000,00\",\n                                            setValue: (e)=>{\n                                                const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                                setValue(\"valorInvestimento\", maskedValue, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 15\n                                        }, this),\n                                        showComplementMessage && scpValidationError === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-xs mt-1\",\n                                            children: \"✅ Valor parcial permitido (dep\\xf3sito adicional ser\\xe1 necess\\xe1rio)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1279,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1263,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1288,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1232,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1296,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1298,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1294,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1310,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1311,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1312,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1314,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1315,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1316,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1309,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1322,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1321,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1330,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1333,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1339,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1340,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1341,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1342,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1348,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1329,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1353,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1352,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1359,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1360,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1358,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1307,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1306,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1304,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1385,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1383,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) !== \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1396,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1395,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1394,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-900 border border-orange-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Altera\\xe7\\xe3o M\\xdaTUO → SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1406,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Como o contrato anterior era M\\xdaTUO, \\xe9 necess\\xe1rio tratar o IR acumulado. A tabela de IR ser\\xe1 exibida para que voc\\xea possa escolher como proceder com o desconto.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1405,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1404,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"A\\xe7\\xe3o Obrigat\\xf3ria:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1416,\n                                        columnNumber: 18\n                                    }, this),\n                                    ' Clique no bot\\xe3o \"\"Calcular IR\"\" acima antes de prosseguir.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1415,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1414,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-900 border border-green-500 rounded-lg p-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-200 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IR Calculado:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1426,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Agora selecione uma das op\\xe7\\xf5es abaixo \",\n                                            watch(\"modalidade\") === \"MUTUO\" ? \"(obrigat\\xf3rio)\" : \"(opcional)\",\n                                            \":\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1425,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDeposito\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDeposito\", true);\n                                                            setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Restaurar valor original se existir (caso estava com desconto)\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                            }\n                                                        } else {\n                                                            setValue(\"irDeposito\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDesconto\")); // Verifica se a outra está marcada\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1431,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1430,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDesconto\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDesconto\", true);\n                                                            setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                            const valorAtual = watch(\"valorInvestimento\");\n                                                            if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                                // Salvar valor original se ainda não foi salvo\n                                                                if (!valorOriginalInvestimento) {\n                                                                    setValorOriginalInvestimento(valorAtual);\n                                                                }\n                                                                const valorNumerico = parseValor(valorAtual);\n                                                                const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                                // Debug logs\n                                                                console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                                console.log(\"Valor atual (string):\", valorAtual);\n                                                                console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                                console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                                const valorComDesconto = valorNumerico - valorIRTabela;\n                                                                console.log(\"Valor com desconto:\", valorComDesconto);\n                                                                // Verificar se o valor com desconto é positivo\n                                                                if (valorComDesconto <= 0) {\n                                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                    setValue(\"irDesconto\", false);\n                                                                    return;\n                                                                }\n                                                                // Aplicar o valor com desconto\n                                                                // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                                const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                                console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                                console.log(\"Valor formatado:\", valorFormatado);\n                                                                setValue(\"valorInvestimento\", valorFormatado);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                    style: \"currency\",\n                                                                    currency: \"BRL\"\n                                                                }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                            } else {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                                setValue(\"irDesconto\", false);\n                                                            }\n                                                            // Mostrar aviso de adendo\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                        } else {\n                                                            setValue(\"irDesconto\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDeposito\")); // Verifica se a outra está marcada\n                                                            // Restaurar valor original se existir\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                            }\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1455,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1429,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        watch(\"irDesconto\") && valorOriginalInvestimento && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-blue-200 font-bold mb-2\",\n                                    children: \"\\uD83D\\uDCB0 Desconto de IR Aplicado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-200 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor original:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1531,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                valorOriginalInvestimento\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1531,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor total do IR (da tabela):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1532,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                new Intl.NumberFormat(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                }).format(contractDetails.totalIR)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1532,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor final (com desconto):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1533,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                watch(\"valorInvestimento\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1533,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-200 text-xs mt-2\",\n                                            children: \"ℹ️ O valor descontado \\xe9 baseado no c\\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1534,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1530,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1528,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1544,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Conforme informado no formul\\xe1rio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1546,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1545,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold mb-3\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1551,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1549,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-800 border border-yellow-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-100 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Exemplo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1557,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Contrato de R$ 10.000 com IR de R$ 2.000 = R$ 8.000 restantes.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1557,\n                                                columnNumber: 105\n                                            }, this),\n                                            \"• Sistema cadastra: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: [\n                                                    Math.floor(((0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(watch(\"valorInvestimento\")) - ((contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) || 0)) / 5000),\n                                                    \" cota(s)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1558,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1558,\n                                                columnNumber: 164\n                                            }, this),\n                                            \"• Valor complementar: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1559,\n                                                columnNumber: 39\n                                            }, this),\n                                            \" dever\\xe1 ser pago separadamente.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1556,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-800 border border-green-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Valor Parcial Permitido:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1564,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Neste caso, o contrato pode ser enviado com o valor atual (n\\xe3o m\\xfaltiplo de R$ 5.000), pois o sistema reconhece que haver\\xe1 um dep\\xf3sito adicional posterior para completar as cotas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1563,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1562,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1543,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1231,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1571,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1578,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1594,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1593,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1606,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1616,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1628,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1633,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1634,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1635,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1629,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1627,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1575,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1641,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1652,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1657,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1658,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1659,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1653,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1651,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1662,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1672,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1677,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1678,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1673,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1671,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1640,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1573,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1572,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1685,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1694,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1684,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1227,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1731,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1736,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1737,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1735,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1734,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1733,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1732,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1748,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1756,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1753,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1752,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1768,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1769,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1767,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1751,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1750,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1749,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"S2p+YJ6E2UICSmXYuSm0HDLU9Bk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});