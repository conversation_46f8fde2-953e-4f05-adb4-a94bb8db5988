import Contact from "@/models/contract";

export function calculateTotalContractValue(contract: Contact): string {
  const contractValue = Number(contract?.valorInvestimento || 0);  
  const addendumValue = contract?.addendum?.reduce((total, addendum) => total + Number(addendum.value || 0), 0) || 0;
  const totalValue = contractValue + addendumValue;
  return totalValue.toLocaleString("pt-br", {
    style: "currency",
    currency: "BRL",
  });
}
export function calculateTotalContractValueNumber(contract: Contact): number {
  const contractValue = Number(contract?.valorInvestimento || 0);
  const addendumValue = contract?.addendum?.reduce((total, addendum) => total + Number(addendum.value || 0), 0) || 0;
  return contractValue + addendumValue;
} 