import { useCallback, useState, useMemo, useEffect } from "react";
import { schemaRegisterManualBroker, SchemaRegisterManualBroker } from "@/utils/schemas/schemasValidation";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { getUserProfile } from "@/functions/getUserData";
import api from "@/core/api";
import returnError from "@/functions/returnError";
import { toast } from "react-toastify";
import formatName from "@/utils/formatName";
import isValidUF from "@/utils/isValidUf";
import { isValidateCPF } from "@/utils/validate-documents";
import validatePhoneNumber from "@/utils/validatePhoneNumber";
import {
    cepMask,
    clearLetters,
    cnpjMask,
    cpfMask,
    phoneMask,
    valueMask,
} from "@/utils/masks";
import Input from "@/components/Input";
import SelectSearch from "@/components/SelectSearch";
import SelectCustom from "@/components/SelectCustom";
import Button from "@/components/Button/Button";
import Dropzone from "@/components/Dropzone";
import { getZipCode } from "@/functions/getZipCode";


interface IProps {
    typeCreate: "advisor";
    hide?: boolean;
    brokerId?: string;
}


export default function AssessorRegisterPf({ typeCreate, hide, brokerId }: IProps) {
    const userProfile = getUserProfile();
    const [accountCreated, setAccountCreated] = useState(false);
    const [accountCreatedId, setAccountCreatedId] = useState("");
    const [password, setPassword] = useState("");
    const [document, setDocumet] = useState<FileList>()
    const [typeAccount, setTypeAccount] = useState("");
    const [residence, setResidence] = useState<FileList>();
    const [parcer, setParcer] = useState<FileList>();
    const [documentName, setDocumentName] = useState<string>("");
    const [residenceName, setResidenceName] = useState<string>("");
    const [parcerName, setParcerName] = useState<string>("");
    const [adminId, setAdminId] = useState("");

    const [documensSended, setDocumentsSended] = useState({
        document: false,
        residence: false,
        parcer: false,
    });

    const {
        register,
        handleSubmit,
        watch,
        setValue,
        reset,
        formState: { errors, isValid },
        getValues,
    } = useForm<SchemaRegisterManualBroker>({
        resolver: yupResolver(schemaRegisterManualBroker),
        mode: "all"
    })

    const { data: brokers = [] } = useQuery({
        queryKey: ["brokers", userProfile.name],
        queryFn: async () => {
            const response = await api.get(
                userProfile.name === "superadmin"
                    ? "/wallets/list-brokers"
                    : "/wallets/admin/brokers"
            );
            return response.data;
        },
        enabled: userProfile.name !== "advisor" && userProfile.name !== "broker",
    });

    const createWalletMutation = useMutation({
        mutationFn: async (payload: any) => {
            const response = await api.post(`/create-wallets/advisor`, payload);
            return response.data;
        },
        onSuccess: (data) => {
            toast.success("Acesso cadastrado com sucesso!");
            setAccountCreated(true);
            setAccountCreatedId(data.id);
            sendDocuments(data.id);
            setPassword("");
            reset();
            setDocumet(undefined);
            setResidence(undefined);
            setParcer(undefined);
        },
        onError: (error) => {
            returnError(error, `Erro ao cadastrar o Broker`);
        },
    });

    const uploadDocumentsMutation = useMutation({
        mutationFn: async ({
            id,
            type,
            file,
        }: {
            id: string;
            type: string;
            file: File;
        }) => {
            const form = new FormData();
            form.append("id", id);
            form.append("type", type);
            form.append("file", file);
            return await api.post("/uploads/documents", form);
        },
        onSuccess: (_, variables) => {
            const typeMapping: { [key: string]: string } = {
                'rg': 'document',
                'proof_residence': 'residence',
                'contract': 'parcer',

            };


            const stateKey = typeMapping[variables.type.toLowerCase()];
            if (stateKey) {
                handleSendDocument(stateKey);
            }
        },
        onError: (error) => {
            returnError(error, "Erro ao enviar o documento");
        },
    });


    const handleSendDocument = useCallback((type: string) => {
        setDocumentsSended((prevState) => ({
            ...prevState,
            [type]: true,
        }));
    }, []);

    const handleRequiredDocs = useMemo(() => {
        if (typeCreate === "advisor") {

            if (typeAccount === "pf") {
                return {
                    document: true,
                    card: false,
                    mei: false,
                    residence: true,
                    social: false,
                    parcer: !hide,
                };
            }
        }
    }, [typeCreate, typeAccount, hide]);

    const requiredDocs = handleRequiredDocs;

    const sendDocuments = useCallback(
        (id: string) => {
            const documentsToSend = [
                {
                    file: document?.[0],
                    type: "RG",
                    title: "Documento de identidade",
                    required: requiredDocs?.document,
                },
                {
                    file: residence?.[0],
                    type: "PROOF_RESIDENCE",
                    title: "Comprovante de residência",
                    required: requiredDocs?.residence,
                },
                {
                    file: parcer?.[0],
                    type: "CONTRACT",
                    title: "Contrato de parceria",
                    required: requiredDocs?.parcer,
                },
            ].filter((doc) => doc.required && doc.file);

            toast.info("Enviando documentos anexados...", {
                autoClose: false,
                toastId: "sendDocuments",
            });

            const timeoutId = setTimeout(() => {
                toast.dismiss("sendDocuments");
            }, 30000);

            documentsToSend.forEach((doc, index) => {
                uploadDocumentsMutation.mutateAsync({
                    id,
                    type: doc.type,
                    file: doc.file!,
                })
                    .then(() => {
                        if (index === documentsToSend.length - 1) {
                            clearTimeout(timeoutId);
                            toast.dismiss("sendDocuments");
                        }
                    })
                    .catch(() => {
                        clearTimeout(timeoutId);
                        toast.dismiss("sendDocuments");
                    });
            });
        },
        [
            document,
            residence,
            parcer,
            requiredDocs,
            uploadDocumentsMutation,
        ]
    );

    const ownerCep = useQuery({
        queryKey: ["address", watch("ownerCep")],
        queryFn: async () => {
            const data = await getZipCode(clearLetters(watch("ownerCep")));
            if (data) {
                setValue("ownerCity", data.city, { shouldValidate: true });
                setValue("ownerState", data.state, { shouldValidate: true });
                setValue("ownerNeighborhood", data.neighborhood, {
                    shouldValidate: true,
                });
                setValue("ownerStreet", data.street, { shouldValidate: true });
            }
        },
        enabled: watch("ownerCep")?.length === 9,
    });

    const onSubmit = useCallback(
        async (data: SchemaRegisterManualBroker) => {
            if (accountCreated) {
                return sendDocuments(accountCreatedId);
            }
            if (!isValidateCPF(clearLetters(String(data.cpf || "")))) {
                return toast.warn("CPF do investidor inválido!");
            }
            if (!validatePhoneNumber(data.phoneNumber)) {
                return toast.warn("Número de telefone inválido");
            }
            if (!isValidUF(data.ownerState)) {
                return toast.warn("Estado inválido");
            }
            const owner = {
                birthDate: data.birthDate,
                socialName: formatName(data.fullName),
                isTaxable: data.isTaxable === "s" ? true : false,
                fullName: data.fullName,
                cpf: clearLetters(String(data.cpf || "")),
                email: data.email,
                phoneNumber: `55${clearLetters(data.phoneNumber || "")}`,
                motherName: formatName(data.motherName),
                pep: data.pep === "s" ? true : false,
                password: password !== "" ? password : undefined,
                address: {
                    cep: clearLetters(data.ownerCep || ""),
                    city: data.ownerCity,
                    state: data.ownerState,
                    neighborhood: data.ownerNeighborhood,
                    street: data.ownerStreet,
                    complement: data.ownerComplement,
                    number: data.ownerNumber,
                },
            };
            const payload = {
                brokerId,
                create: {
                    accountType: "PHYSICAL",
                    owner,
                },
                partPercent: data.participationPercentage,
            };
            await createWalletMutation.mutateAsync(payload);
        },
        [accountCreated, accountCreatedId, password, sendDocuments, createWalletMutation]
    );


    const handleRemoveDocument = () => {
        setDocumet(undefined);
        setDocumentName("");
    };
    const handleRemoveResidence = () => {
        setResidence(undefined);
        setResidenceName("");
    };

    const handleRemoveContract = () => {
        setParcer(undefined);
        setParcerName("");
    };

    useEffect(() => {
        setValue('isPf', true);
    }, [setValue]);

    return (
        <div className="md:px-5">
            <form
                onSubmit={handleSubmit(onSubmit)}
                onKeyDown={(e) => {
                    if (e.key === "Enter") {
                        e.preventDefault();
                    }
                }}
            >
                <div className="m-auto">

                    <div>
                        <p className="text-lg text-white font-bold">Dados Pessoais</p>
                        <div className="mb-10 m-auto">
                            <div className="flex md:flex-row flex-col w-full gap-4 justify-between mt-2">
                                <div className="md:w-2/4">
                                    <p className="text-white mb-1">
                                        Nome Completo{" "}
                                        <b className="text-red-500 font-light text-sm">
                                            {errors.fullName && `- ${errors.fullName.message}`}
                                        </b>
                                    </p>
                                    <input
                                        {...register("fullName")}
                                        className={`h-12 w-full px-4 text-white rounded-xl ${errors.fullName ? "ring-[#f33636]" : "ring-[#FF9900]"
                                            } ring-1 ring-inset bg-black flex-1`}
                                    />
                                </div>
                                <div className="md:w-2/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            CPF{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.cpf && `- ${errors.cpf.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("cpf")}
                                            onChange={({ target }) => {
                                                const value = target.value;
                                                setValue("cpf", cpfMask(value));
                                            }}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.cpf ? "ring-[#f33636]" : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                                <div className="md:w-1/4">
                                    <div className="">
                                        <p className="text-white mb-1">
                                            Emitir Taxa
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.isTaxable && `- ${errors.isTaxable.message}`}
                                            </b>
                                        </p>
                                        <SelectCustom
                                            value={watch("isTaxable")}
                                            onChange={(e) => setValue("isTaxable", e.target.value)}
                                        >
                                            <option disabled selected>
                                                Selecione
                                            </option>
                                            <option value={"s"}>Sim</option>
                                            <option value={"n"}>Não</option>
                                        </SelectCustom>
                                    </div>
                                </div>
                                <div className="md:w-1/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Data de Nascimento{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.birthDate && `- ${errors.birthDate.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("birthDate")}
                                            type="date"
                                            max={new Date().toISOString().split("T")[0]} // impede datas futuras
                                            min="1900-01-01" // impede datas muito antigas
                                            onInput={(e) => {
                                                const input = e.target as HTMLInputElement;
                                                if (input.value.length > 10) {
                                                    input.value = input.value.slice(0, 10);
                                                }
                                            }}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.birthDate ? "ring-[#f33636]" : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="flex md:flex-row flex-col w-full gap-4 justify-between mt-4">
                                <div className="md:w-1/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Telefone{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.phoneNumber &&
                                                    `- ${errors.phoneNumber.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("phoneNumber")}
                                            maxLength={15}
                                            onChange={({ target }) =>
                                                setValue(
                                                    "phoneNumber",
                                                    String(phoneMask(target.value))
                                                )
                                            }
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.phoneNumber
                                                ? "ring-[#f33636]"
                                                : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                                <div className="md:w-2/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            E-mail{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.email && `- ${errors.email.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("email")}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.email ? "ring-[#f33636]" : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                                <div className="md:w-2/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Taxa de participação em %
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.participationPercentage &&
                                                    `- ${errors.participationPercentage.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("participationPercentage")}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.participationPercentage
                                                ? "ring-[#f33636]"
                                                : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                                <div className="md:w-2/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Nome da mãe{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.motherName &&
                                                    `- ${errors.motherName.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("motherName")}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.motherName
                                                ? "ring-[#f33636]"
                                                : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="flex md:flex-row flex-col w-full gap-4 justify-between mt-4">
                                <div className="md:w-1/4">
                                    <div>
                                        <Input
                                            id=""
                                            label="Senha"
                                            type="password"
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                            name="password"
                                        />
                                    </div>
                                </div>
                                <div className="md:w-1/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Pessoa politicamente exposta?{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.pep && `- ${errors.pep.message}`}
                                            </b>
                                        </p>
                                        <SelectCustom
                                            value={watch("pep")}
                                            onChange={(e) => setValue("pep", e.target.value)}
                                        >
                                            <option disabled selected>
                                                Selecione
                                            </option>
                                            <option value={"s"}>Sim</option>
                                            <option value={"n"}>Não</option>
                                        </SelectCustom>
                                    </div>
                                </div>
                                <div className="md:w-1/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            CEP{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.ownerCep && `- ${errors.ownerCep.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("ownerCep")}
                                            onChange={({ target }) =>
                                                setValue("ownerCep", cepMask(target.value))
                                            }
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerCep ? "ring-[#f33636]" : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                                <div className="md:w-1/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Cidade{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.ownerCity && `- ${errors.ownerCity.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("ownerCity")}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerCity ? "ring-[#f33636]" : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="flex md:flex-row flex-col w-full gap-4 justify-between mt-4">
                                <div className="md:w-1/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Estado{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.ownerState &&
                                                    `- ${errors.ownerState.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("ownerState")}
                                            maxLength={2}
                                            onChange={({ target }) =>
                                                setValue("ownerState", target.value.toUpperCase())
                                            }
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerState
                                                ? "ring-[#f33636]"
                                                : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                                <div className="md:w-2/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Bairro{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.ownerNeighborhood &&
                                                    `- ${errors.ownerNeighborhood.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("ownerNeighborhood")}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerNeighborhood
                                                ? "ring-[#f33636]"
                                                : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                                <div className="md:w-1/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Número{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.ownerNumber &&
                                                    `- ${errors.ownerNumber.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("ownerNumber")}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerNumber
                                                ? "ring-[#f33636]"
                                                : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="flex md:flex-row flex-col w-full gap-4 mt-4">
                                <div className="md:w-2/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Rua{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.ownerStreet &&
                                                    `- ${errors.ownerStreet.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("ownerStreet")}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerStreet
                                                ? "ring-[#f33636]"
                                                : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                                <div className="md:w-2/4">
                                    <div>
                                        <p className="text-white mb-1">
                                            Complemento{" "}
                                            <b className="text-red-500 font-light text-sm">
                                                {errors.ownerComplement &&
                                                    `- ${errors.ownerComplement.message}`}
                                            </b>
                                        </p>
                                        <input
                                            {...register("ownerComplement")}
                                            className={`h-12 w-full px-4 text-white rounded-xl ${errors.ownerComplement
                                                ? "ring-[#f33636]"
                                                : "ring-[#FF9900]"
                                                } ring-1 ring-inset bg-black flex-1`}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col md:flex-row gap-2 mt-2 mb-10 text-white">
                        <div>
                            <p className="mb-1">Documento de identidade</p>
                            <Dropzone
                                onFileUploaded={setDocumet}
                                fileName={documentName}
                                onRemoveFile={handleRemoveDocument}
                            />
                        </div>
                        <div>
                            <p className="mb-1">Comprovante de residência</p>
                            <Dropzone
                                onFileUploaded={setResidence}
                                fileName={residenceName}
                                onRemoveFile={handleRemoveResidence}
                            />
                        </div>
                        <div>
                            <p className="mb-1">Contrato de parceria</p>
                            <Dropzone
                                onFileUploaded={setParcer}
                                fileName={parcerName}
                                onRemoveFile={handleRemoveContract}
                            />
                        </div>
                    </div>
                    <div className="md:w-52 mb-10">
                        <Button
                            label="Enviar"
                            loading={createWalletMutation.isPending}
                            disabled={
                                createWalletMutation.isPending ||
                                !isValid ||
                                !document ||
                                !residence ||
                                !parcer
                            }
                        />
                    </div>
                </div>
            </form>
        </div>
    );


}