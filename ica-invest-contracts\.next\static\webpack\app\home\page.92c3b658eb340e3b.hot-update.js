"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/home/<USER>",{

/***/ "(app-pages-browser)/./src/utils/masks.ts":
/*!****************************!*\
  !*** ./src/utils/masks.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cepMask: function() { return /* binding */ cepMask; },\n/* harmony export */   clearLetters: function() { return /* binding */ clearLetters; },\n/* harmony export */   cnpjMask: function() { return /* binding */ cnpjMask; },\n/* harmony export */   cpfMask: function() { return /* binding */ cpfMask; },\n/* harmony export */   getDateFormat: function() { return /* binding */ getDateFormat; },\n/* harmony export */   hideCnpj: function() { return /* binding */ hideCnpj; },\n/* harmony export */   hideCpf: function() { return /* binding */ hideCpf; },\n/* harmony export */   phoneMask: function() { return /* binding */ phoneMask; },\n/* harmony export */   valueMask: function() { return /* binding */ valueMask; }\n/* harmony export */ });\nconst cpfMask = (value)=>{\n    return value.replace(/\\D/g, \"\").replace(/(\\d{3})(\\d)/, \"$1.$2\").replace(/(\\d{3})(\\d)/, \"$1.$2\").replace(/(\\d{3})(\\d{1,2})/, \"$1-$2\").replace(/(-\\d{2})\\d+?$/, \"$1\");\n};\nconst cnpjMask = (value)=>{\n    return value.replace(/\\D/g, \"\").replace(/^(\\d{2})(\\d)/, \"$1.$2\").replace(/^(\\d{2})\\.(\\d{3})(\\d)/, \"$1.$2.$3\").replace(/\\.(\\d{3})(\\d)/, \".$1/$2\").replace(/(\\d{4})(\\d)/, \"$1-$2\").replace(/(-\\d{2})\\d+?$/, \"$1\");\n};\nconst valueMask = (value)=>{\n    // Se o valor estiver vazio ou for undefined/null, retornar string vazia\n    if (!value || value.trim() === \"\") {\n        return \"\";\n    }\n    // Remover todos os caracteres não numéricos\n    const numericOnly = value.replace(/\\D/g, \"\");\n    // Se não há dígitos, retornar string vazia\n    if (numericOnly === \"\") {\n        return \"\";\n    }\n    // Aplicar máscara apenas se há pelo menos 1 dígito\n    return numericOnly.replace(/(\\d{1})(\\d{14})$/, \"$1.$2\") // coloca ponto antes dos ultimos digitos\n    .replace(/(\\d{1})(\\d{11})$/, \"$1.$2\") // coloca ponto antes dos ultimos 11 digitos\n    .replace(/(\\d{1})(\\d{8})$/, \"$1.$2\") // coloca ponto antes dos ultimos 8 digitos\n    .replace(/(\\d{1})(\\d{5})$/, \"$1.$2\") // coloca ponto antes dos ultimos 5 digitos\n    .replace(/(\\d{1})(\\d{1,2})$/, \"$1,$2\"); // coloca virgula antes dos ultimos 2 digitos\n};\nconst phoneMask = (value)=>{\n    return value.replace(/\\D/g, \"\") // Remove tudo que não for dígito\n    .replace(/^55/, \"\") // Remove \"55\" se estiver no início\n    .replace(/^(\\d{2})(\\d)/g, \"($1) $2\") // Coloca parênteses no DDD\n    .replace(/(\\d)(\\d{4})$/, \"$1-$2\"); // Coloca hífen\n};\nconst getDateFormat = (value)=>value.replace(/\\D/g, \"\").replace(/^(\\d{2})(\\d{2})(\\d)/, \"$1/$2/$3\");\nconst hideCpf = (value)=>{\n    return value.replace(/(\\d{3})/, \"•••\").replace(/(\\d{1})(\\d{1,2})$/, \"••\");\n};\nconst hideCnpj = (value)=>{\n    return value.replace(/(\\d{3})/, \"•••\").replace(/(\\d{1})(\\d{1,2})$/, \"••\");\n};\nconst clearLetters = (value)=>{\n    return value.replace(/\\D/g, \"\");\n};\nconst cepMask = (value)=>{\n    return value.replace(/\\D/g, \"\").replace(/(\\d{5})(\\d)/, \"$1-$2\").replace(/(-\\d{3})\\d+?$/, \"$1\");\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/masks.ts\n"));

/***/ })

});