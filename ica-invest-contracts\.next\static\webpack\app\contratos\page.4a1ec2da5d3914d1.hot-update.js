"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/page",{

/***/ "(app-pages-browser)/./src/utils/formatNumberValue.ts":
/*!****************************************!*\
  !*** ./src/utils/formatNumberValue.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ formatNumberValue; }\n/* harmony export */ });\nfunction formatNumberValue(value) {\n    // Se o valor estiver vazio, undefined ou null, retornar 0\n    if (!value || value.trim() === \"\") {\n        return 0;\n    }\n    // Remover pontos (separadores de milhares) e substituir vírgula por ponto (decimal)\n    const cleanValue = value.replaceAll(\".\", \"\").replaceAll(\",\", \".\");\n    // Se após limpeza não há nada ou só caracteres não numéricos, retornar 0\n    if (cleanValue === \"\" || isNaN(Number(cleanValue))) {\n        return 0;\n    }\n    return Number(cleanValue);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy91dGlscy9mb3JtYXROdW1iZXJWYWx1ZS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0Esa0JBQWtCQyxLQUFhO0lBQ3JELDBEQUEwRDtJQUMxRCxJQUFJLENBQUNBLFNBQVNBLE1BQU1DLElBQUksT0FBTyxJQUFJO1FBQ2pDLE9BQU87SUFDVDtJQUVBLG9GQUFvRjtJQUNwRixNQUFNQyxhQUFhRixNQUFNRyxVQUFVLENBQUMsS0FBSyxJQUFJQSxVQUFVLENBQUMsS0FBSztJQUU3RCx5RUFBeUU7SUFDekUsSUFBSUQsZUFBZSxNQUFNRSxNQUFNQyxPQUFPSCxjQUFjO1FBQ2xELE9BQU87SUFDVDtJQUVBLE9BQU9HLE9BQU9IO0FBQ2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy91dGlscy9mb3JtYXROdW1iZXJWYWx1ZS50cz84YzljIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGZvcm1hdE51bWJlclZhbHVlKHZhbHVlOiBzdHJpbmcpIHtcclxuICAvLyBTZSBvIHZhbG9yIGVzdGl2ZXIgdmF6aW8sIHVuZGVmaW5lZCBvdSBudWxsLCByZXRvcm5hciAwXHJcbiAgaWYgKCF2YWx1ZSB8fCB2YWx1ZS50cmltKCkgPT09ICcnKSB7XHJcbiAgICByZXR1cm4gMDtcclxuICB9XHJcblxyXG4gIC8vIFJlbW92ZXIgcG9udG9zIChzZXBhcmFkb3JlcyBkZSBtaWxoYXJlcykgZSBzdWJzdGl0dWlyIHbDrXJndWxhIHBvciBwb250byAoZGVjaW1hbClcclxuICBjb25zdCBjbGVhblZhbHVlID0gdmFsdWUucmVwbGFjZUFsbCgnLicsICcnKS5yZXBsYWNlQWxsKCcsJywgJy4nKTtcclxuXHJcbiAgLy8gU2UgYXDDs3MgbGltcGV6YSBuw6NvIGjDoSBuYWRhIG91IHPDsyBjYXJhY3RlcmVzIG7Do28gbnVtw6lyaWNvcywgcmV0b3JuYXIgMFxyXG4gIGlmIChjbGVhblZhbHVlID09PSAnJyB8fCBpc05hTihOdW1iZXIoY2xlYW5WYWx1ZSkpKSB7XHJcbiAgICByZXR1cm4gMDtcclxuICB9XHJcblxyXG4gIHJldHVybiBOdW1iZXIoY2xlYW5WYWx1ZSk7XHJcbn0iXSwibmFtZXMiOlsiZm9ybWF0TnVtYmVyVmFsdWUiLCJ2YWx1ZSIsInRyaW0iLCJjbGVhblZhbHVlIiwicmVwbGFjZUFsbCIsImlzTmFOIiwiTnVtYmVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/formatNumberValue.ts\n"));

/***/ })

});