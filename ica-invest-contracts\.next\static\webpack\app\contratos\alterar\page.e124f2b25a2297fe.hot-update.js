"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-validation\", \"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            // Verificar se é múltiplo de 5000\n            return numericValue > 0 && numericValue % 5000 === 0;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            console.log(\"\\uD83D\\uDD0D Dados do contrato carregados:\", response.data);\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [irOpcaoSelecionada, setIrOpcaoSelecionada] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se uma opção de IR foi selecionada\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    const [scpValidationError, setScpValidationError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Erro de validação SCP customizado\n    // Função para validar valor SCP\n    const validateScpValue = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)((valor, modalidade)=>{\n        if (modalidade !== \"SCP\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Se o valor estiver vazio, limpar erro e retornar true (será validado pelo Yup)\n        if (!valor || valor.trim() === \"\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        const valorNumerico = Number(valor.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valor:\", valor);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valorNumerico:\", valorNumerico);\n        // Validação: deve ser múltiplo de 5.000\n        if (valorNumerico <= 0) {\n            console.log(\"❌ SCP: Valor deve ser maior que zero\");\n            setScpValidationError(\"Para contratos SCP, o valor deve ser maior que zero\");\n            return false;\n        }\n        if (valorNumerico % 5000 !== 0) {\n            console.log(\"❌ SCP: Valor n\\xe3o \\xe9 m\\xfaltiplo de R$ 5.000\");\n            setScpValidationError(\"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000 (ex: R$ 5.000, R$ 10.000, R$ 15.000...)\");\n            return false;\n        }\n        console.log(\"✅ SCP: Valor v\\xe1lido - m\\xfaltiplo de R$ 5.000\");\n        setScpValidationError(\"\");\n        return true;\n    }, []);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                console.log(\"\\uD83D\\uDD0D Debug Valor Investimento:\", {\n                    valorOriginal: contract.investmentValue,\n                    valorString: valorInvestimento,\n                    valorComMascara: valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\"\n                });\n                // Aplicar máscara no valor do investimento se houver valor\n                // Se o valor vem como número, converter para centavos antes da máscara\n                let valorFormatado = \"\";\n                if (valorInvestimento) {\n                    // Se é um número, multiplicar por 100 para converter para centavos\n                    if (typeof valorInvestimento === \"number\" || !isNaN(Number(valorInvestimento))) {\n                        const valorEmCentavos = Math.round(Number(valorInvestimento) * 100);\n                        valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                    } else {\n                        // Se já é string formatada, usar diretamente\n                        valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento);\n                    }\n                }\n                setValue(\"valorInvestimento\", valorFormatado);\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\") {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            if (valorNumerico > 0 && valorNumerico % 5000 === 0) {\n                // Só calcular cotas se for múltiplo exato de 5000\n                const cotas = valorNumerico / 5000;\n                setValue(\"quotaQuantity\", cotas.toString());\n            } else {\n                setValue(\"quotaQuantity\", \"0\");\n            }\n        } else if (modalidade === \"SCP\") {\n            // Se não há valor ou valor está vazio, limpar cotas\n            setValue(\"quotaQuantity\", \"0\");\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 396,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando há mudança de MUTUO para SCP\n        if (data.modalidade === \"MUTUO\" || data.modalidade === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validação específica para SCP usando função customizada\n        if (!validateScpValue(data.valorInvestimento, data.modalidade)) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(scpValidationError);\n            return;\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Verificar se é realmente um upgrade (tem tipo definido)\n            const isUpgrade = !!tipo;\n            let originalContractId = null;\n            if (isUpgrade) {\n                // Buscar contrato ativo para upgrade\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - contractData completo:\", JSON.stringify(contractData, null, 2));\n                if (!(contractData === null || contractData === void 0 ? void 0 : contractData.contracts) || contractData.contracts.length === 0) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: Nenhum contrato encontrado para este investidor.\");\n                    return;\n                }\n                // Procurar por contrato ativo\n                const activeContract = contractData.contracts.find((contract)=>{\n                    var _contract_contractStatus;\n                    const status = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n                    return status === \"ACTIVE\" || status === \"ATIVO\";\n                });\n                if (!activeContract) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: Nenhum contrato ativo encontrado. Apenas contratos ativos podem ser alterados.\");\n                    return;\n                }\n                originalContractId = activeContract.id;\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - Contrato ativo encontrado:\", activeContract);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - originalContractId:\", originalContractId);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - tipo:\", tipo);\n                console.log(\"\\uD83D\\uDD0D Debug Upgrade - originalContractId \\xe9 UUID?\", /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(originalContractId));\n                if (!originalContractId) {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"❌ Erro: ID do contrato ativo n\\xe3o encontrado. Estrutura de dados inv\\xe1lida.\");\n                    return;\n                }\n            }\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                // ✅ CAMPOS DE UPGRADE - Só incluir se for realmente um upgrade\n                ...isUpgrade && {\n                    isUpgrade: true,\n                    originalContractId: originalContractId,\n                    upgradeType: tipo\n                },\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"=== DADOS DE UPGRADE ===\");\n            console.log(\"\\xc9 upgrade?\", isUpgrade);\n            console.log(\"IsUpgrade:\", requestData.isUpgrade);\n            console.log(\"OriginalContractId:\", requestData.originalContractId);\n            console.log(\"UpgradeType:\", requestData.upgradeType);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        // Debug: verificar qual campo contém o tipo do contrato\n        console.log(\"\\uD83D\\uDD0D Debug Contract Type Fields:\", {\n            tags: contract.tags,\n            contractType: contract.contractType,\n            type: contract.type,\n            investment: contract.investment,\n            investmentValue: contract.investmentValue,\n            quotesAmount: contract.quotesAmount,\n            fullContract: contract\n        });\n        // Determinar o tipo REAL do contrato baseado nas regras de negócio:\n        // - SCP: valor múltiplo de R$ 5.000 por cota\n        // - MÚTUO: valor livre\n        const contractValue = parseFloat(contract.investmentValue) || 0;\n        const quotesAmount = parseInt(contract.quotesAmount) || 0;\n        let realContractType = \"MUTUO\"; // padrão\n        // Se tem cotas e o valor é múltiplo de 5000, provavelmente é SCP\n        if (quotesAmount > 0 && contractValue > 0 && contractValue % 5000 === 0) {\n            realContractType = \"SCP\";\n        }\n        console.log(\"\\uD83D\\uDD0D Contract Analysis:\", {\n            tagsFromAPI: contract.tags,\n            contractValue,\n            quotesAmount,\n            isMultipleOf5000: contractValue % 5000 === 0,\n            realContractType\n        });\n        return {\n            details,\n            totalIR,\n            contractType: realContractType\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    // Validação em tempo real para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (valorInvestimento && modalidade) {\n            validateScpValue(valorInvestimento, modalidade);\n        }\n    }, [\n        valorInvestimento,\n        modalidade,\n        validateScpValue,\n        showComplementMessage\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, IR é opcional mas pode ser aplicado\n            setIrCalculado(true);\n            setIrOpcaoSelecionada(false); // Permitir seleção de IR para SCP\n            // NÃO resetar irDeposito e irDesconto para SCP - deixar o usuário escolher\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setIrOpcaoSelecionada(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1008,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1012,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1017,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1011,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1022,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1021,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1043,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1031,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1059,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1058,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1073,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1072,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1057,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1084,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1095,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1094,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1119,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1131,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1130,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1143,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1154,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1010,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1009,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1177,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1181,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1180,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1191,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1190,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1179,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1203,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1202,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1213,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1212,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1201,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1178,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1225,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1226,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1239,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1237,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1007,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1254,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1268,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1256,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1271,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1255,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"valorInvestimento\",\n                                            width: \"100%\",\n                                            error: !!errors.valorInvestimento || !!scpValidationError,\n                                            errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message) || scpValidationError,\n                                            label: \"Valor do Investimento\",\n                                            placeholder: \"ex: R$ 50.000,00\",\n                                            setValue: (e)=>{\n                                                const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                                setValue(\"valorInvestimento\", maskedValue, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1277,\n                                            columnNumber: 15\n                                        }, this),\n                                        showComplementMessage && scpValidationError === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-xs mt-1\",\n                                            children: \"✅ Valor parcial permitido (dep\\xf3sito adicional ser\\xe1 necess\\xe1rio)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1276,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1301,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1253,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1309,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1311,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1307,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1323,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1324,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1326,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1327,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1328,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1329,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1321,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1334,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1343,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1346,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1352,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1353,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1354,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1355,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1361,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1342,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1366,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1365,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1372,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1371,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1383,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1382,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1320,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1319,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1317,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center text-white text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                className: \"mr-2\",\n                                                checked: watch(\"irDeposito\"),\n                                                onChange: (e)=>{\n                                                    if (e.target.checked) {\n                                                        setValue(\"irDeposito\", true);\n                                                        setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                        setIrOpcaoSelecionada(true);\n                                                        // Restaurar valor original se existir (caso estava com desconto)\n                                                        if (valorOriginalInvestimento) {\n                                                            setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                        }\n                                                    } else {\n                                                        setValue(\"irDeposito\", false);\n                                                        setIrOpcaoSelecionada(!!watch(\"irDesconto\")); // Verifica se a outra está marcada\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1408,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1407,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center text-white text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                className: \"mr-2\",\n                                                checked: watch(\"irDesconto\"),\n                                                onChange: (e)=>{\n                                                    if (e.target.checked) {\n                                                        setValue(\"irDesconto\", true);\n                                                        setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                        setIrOpcaoSelecionada(true);\n                                                        // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                        const valorAtual = watch(\"valorInvestimento\");\n                                                        if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                            // Salvar valor original se ainda não foi salvo\n                                                            if (!valorOriginalInvestimento) {\n                                                                setValorOriginalInvestimento(valorAtual);\n                                                            }\n                                                            const valorNumerico = parseValor(valorAtual);\n                                                            const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                            // Debug logs\n                                                            console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                            console.log(\"Valor atual (string):\", valorAtual);\n                                                            console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                            console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                            const valorComDesconto = valorNumerico - valorIRTabela;\n                                                            console.log(\"Valor com desconto:\", valorComDesconto);\n                                                            // Verificar se o valor com desconto é positivo\n                                                            if (valorComDesconto <= 0) {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                setValue(\"irDesconto\", false);\n                                                                return;\n                                                            }\n                                                            // Aplicar o valor com desconto\n                                                            // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                            const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                            const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                            console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                            console.log(\"Valor formatado:\", valorFormatado);\n                                                            setValue(\"valorInvestimento\", valorFormatado);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                style: \"currency\",\n                                                                currency: \"BRL\"\n                                                            }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                        } else {\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                            setValue(\"irDesconto\", false);\n                                                        }\n                                                        // Mostrar aviso de adendo\n                                                        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                    } else {\n                                                        setValue(\"irDesconto\", false);\n                                                        setIrOpcaoSelecionada(!!watch(\"irDeposito\")); // Verifica se a outra está marcada\n                                                        // Restaurar valor original se existir\n                                                        if (valorOriginalInvestimento) {\n                                                            setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                        }\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1432,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1431,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1406,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Conforme informado no formul\\xe1rio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1510,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1509,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold mb-3\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1515,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1513,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-800 border border-yellow-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-100 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Exemplo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1521,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Contrato de R$ 10.000 com IR de R$ 2.000 = R$ 8.000 restantes.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1521,\n                                                columnNumber: 105\n                                            }, this),\n                                            \"• Sistema cadastra: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: [\n                                                    Math.floor(((0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(watch(\"valorInvestimento\")) - ((contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) || 0)) / 5000),\n                                                    \" cota(s)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1522,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1522,\n                                                columnNumber: 164\n                                            }, this),\n                                            \"• Valor complementar: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1523,\n                                                columnNumber: 39\n                                            }, this),\n                                            \" dever\\xe1 ser pago separadamente.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1520,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1519,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-800 border border-green-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Valor Parcial Permitido:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1528,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Neste caso, o contrato pode ser enviado com o valor atual (n\\xe3o m\\xfaltiplo de R$ 5.000), pois o sistema reconhece que haver\\xe1 um dep\\xf3sito adicional posterior para completar as cotas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1527,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1526,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1507,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1252,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1535,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1542,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1558,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1557,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1570,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1580,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1592,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1597,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1598,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1599,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1593,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1591,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1539,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1605,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1616,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1621,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1622,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1623,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1617,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1615,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1626,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1636,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1641,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1642,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1637,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1635,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1604,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1537,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1536,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1649,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1658,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1648,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1248,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1695,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1700,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1701,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1699,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1698,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1697,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1696,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1712,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1720,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1717,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1716,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1732,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1733,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1731,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1715,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1714,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1713,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"+QztcNxdjnBdozgT0DMPQZPjj9s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});