"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CheckDuplicateContractHandler_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckDuplicateContractHandler = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const create_notification_service_1 = require("../../../../notifications/services/create-notification.service");
const contract_entity_1 = require("../../../../../shared/database/typeorm/entities/contract.entity");
const typeorm_2 = require("typeorm");
const notification_entity_1 = require("../../../../../shared/database/typeorm/entities/notification.entity");
const abstract_contract_handler_1 = require("../abstract-contract.handler");
let CheckDuplicateContractHandler = CheckDuplicateContractHandler_1 = class CheckDuplicateContractHandler extends abstract_contract_handler_1.AbstractContractHandler {
    constructor(contractRepository, createNotificationService) {
        super();
        this.contractRepository = contractRepository;
        this.createNotificationService = createNotificationService;
        this.logger = new common_1.Logger(CheckDuplicateContractHandler_1.name);
    }
    async handle(context) {
        this.logger.debug('Iniciando verificação de contrato duplicado.');
        const { dto } = context;
        this.logger.debug(`Verificando duplicidade de contrato para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`);
        this.logger.debug(`IsUpgrade: ${dto.isUpgrade}, OriginalContractId: ${dto.originalContractId}`);
        try {
            const existContract = await this.contractRepository.findOne({
                where: {
                    status: 'ACTIVE',
                    investor: [
                        { owner: { cpf: (0, typeorm_2.Equal)(dto.individual?.cpf) } },
                        { business: { cnpj: (0, typeorm_2.Equal)(dto.company?.cnpj) } },
                    ],
                },
            });
            if (existContract) {
                if (dto.isUpgrade && dto.originalContractId) {
                    this.logger.log(`Upgrade detectado: marcando contrato ${dto.originalContractId} como EXPIRED`);
                    const originalContract = await this.contractRepository.findOne({
                        where: { id: dto.originalContractId, status: 'ACTIVE' },
                    });
                    if (!originalContract) {
                        throw new common_1.BadRequestException('Contrato original não encontrado ou não está ativo.');
                    }
                    await this.contractRepository.update(dto.originalContractId, {
                        status: 'EXPIRED',
                    });
                    this.logger.log(`Contrato ${dto.originalContractId} marcado como EXPIRED para upgrade`);
                    await this.createNotificationService.create({
                        userOwnerRoleRelationId: context.userProfile.id,
                        description: `Upgrade de contrato realizado. Contrato anterior foi marcado como expirado e novo contrato está sendo criado.`,
                        title: `Upgrade de Contrato Realizado`,
                        type: notification_entity_1.NotificationTypeEnum.DUPLICATED_DOCUMENT,
                        contractId: originalContract.id,
                        investorId: originalContract.investorId,
                    });
                }
                else {
                    this.logger.warn(`Contrato duplicado encontrado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`);
                    await this.createNotificationService.create({
                        userOwnerRoleRelationId: context.userProfile.id,
                        description: `Foi identificada uma nova tentativa de registro de contrato utilizando um CPF já vinculado a outro contrato ativo no sistema.`,
                        title: `Nova Tentativa de Duplicidade!`,
                        type: notification_entity_1.NotificationTypeEnum.DUPLICATED_DOCUMENT,
                        contractId: existContract.id,
                        investorId: existContract.investorId,
                    });
                    throw new common_1.BadRequestException('Já existe um contrato ativo para este documento.');
                }
            }
            this.logger.log(`Nenhum contrato duplicado encontrado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`);
            await super.handle(context);
        }
        catch (error) {
            this.logger.error(`Erro durante a verificação de contrato duplicado para o documento: ${dto.individual?.cpf ?? dto.company?.cnpj}`, error.stack);
            throw new common_1.BadRequestException(error);
        }
    }
};
exports.CheckDuplicateContractHandler = CheckDuplicateContractHandler;
exports.CheckDuplicateContractHandler = CheckDuplicateContractHandler = CheckDuplicateContractHandler_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contract_entity_1.ContractEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        create_notification_service_1.CreateNotificationService])
], CheckDuplicateContractHandler);
//# sourceMappingURL=check-duplicate-contract.handler.js.map