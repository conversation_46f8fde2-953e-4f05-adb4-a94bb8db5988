"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contratos/alterar/page",{

/***/ "(app-pages-browser)/./src/app/contratos/alterar/page.tsx":
/*!********************************************!*\
  !*** ./src/app/contratos/alterar/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlterarContrato; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header/index.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar/index.tsx\");\n/* harmony import */ var _utils_masks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/masks */ \"(app-pages-browser)/./src/utils/masks.ts\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Inputs/InputText */ \"(app-pages-browser)/./src/components/Inputs/InputText/index.tsx\");\n/* harmony import */ var _components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/SelectCustom */ \"(app-pages-browser)/./src/components/SelectCustom/index.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @hookform/resolvers/yup */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/yup/dist/yup.mjs\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! yup */ \"(app-pages-browser)/./node_modules/yup/index.esm.js\");\n/* harmony import */ var _functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/functions/getDataFilter */ \"(app-pages-browser)/./src/functions/getDataFilter.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _functions_returnError__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/functions/returnError */ \"(app-pages-browser)/./src/functions/returnError.ts\");\n/* harmony import */ var _core_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/core/api */ \"(app-pages-browser)/./src/core/api.ts\");\n/* harmony import */ var _functions_getUserData__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/functions/getUserData */ \"(app-pages-browser)/./src/functions/getUserData.ts\");\n/* harmony import */ var _utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/formatNumberValue */ \"(app-pages-browser)/./src/utils/formatNumberValue.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Schema imports removed since using custom field names\n\n\n\n\n\n\n// Função para máscara de telefone que preserva o código do país\nconst phoneWithCountryMask = (value)=>{\n    const cleaned = value.replace(/\\D/g, \"\");\n    // Se começar com 55 e tiver 13 dígitos (55 + DDD + 9 dígitos)\n    if (cleaned.startsWith(\"55\") && cleaned.length === 13) {\n        return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Se não começar com 55 e tiver 11 dígitos (DDD + 9 dígitos), adicionar 55\n    if (!cleaned.startsWith(\"55\") && cleaned.length === 11) {\n        const withCountryCode = \"55\" + cleaned;\n        return withCountryCode.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{4})$/, \"$1 ($2) $3-$4\");\n    }\n    // Para números em construção, aplicar máscara progressiva\n    if (cleaned.length <= 13) {\n        if (cleaned.startsWith(\"55\")) {\n            // Já tem código do país\n            if (cleaned.length <= 4) {\n                return cleaned.replace(/^(\\d{2})(\\d{0,2})$/, \"$1 ($2\");\n            } else if (cleaned.length <= 9) {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{0,5})$/, \"$1 ($2) $3\");\n            } else {\n                return cleaned.replace(/^(\\d{2})(\\d{2})(\\d{5})(\\d{0,4})$/, \"$1 ($2) $3-$4\");\n            }\n        } else {\n            // Não tem código do país, usar máscara normal\n            return (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.phoneMask)(value);\n        }\n    }\n    return value;\n};\n// Schema de validação\nconst validationSchema = yup__WEBPACK_IMPORTED_MODULE_12__.object().shape({\n    tipoContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Selecione o tipo de contrato\"),\n    nomeCompleto: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Nome completo obrigat\\xf3rio\"),\n    identidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Identidade obrigat\\xf3ria\"),\n    celular: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Celular obrigat\\xf3rio\"),\n    cpf: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CPF/CNPJ obrigat\\xf3rio\"),\n    dataNascimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    nomeMae: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    email: yup__WEBPACK_IMPORTED_MODULE_12__.string().email(\"E-mail inv\\xe1lido\").required(\"E-mail obrigat\\xf3rio\"),\n    cep: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"CEP obrigat\\xf3rio\"),\n    cidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Cidade obrigat\\xf3ria\"),\n    endereco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Endere\\xe7o obrigat\\xf3rio\"),\n    numero: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"N\\xfamero obrigat\\xf3rio\"),\n    complemento: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    estado: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Estado obrigat\\xf3rio\"),\n    banco: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Banco obrigat\\xf3rio\"),\n    conta: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Conta obrigat\\xf3ria\"),\n    agencia: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Ag\\xeancia obrigat\\xf3ria\"),\n    chavePix: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Chave PIX obrigat\\xf3ria\"),\n    modalidade: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Modalidade obrigat\\xf3ria\").test(\"scp-to-mutuo-validation\", \"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\", function(value) {\n        // Esta validação será aplicada dinamicamente no contexto do componente\n        return true; // A validação real é feita no onChange e onSubmit\n    }),\n    valorInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Valor do investimento obrigat\\xf3rio\").test(\"scp-minimum\", \"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\", function(value) {\n        const modalidade = this.parent.modalidade;\n        if (modalidade === \"SCP\" && value) {\n            // Usar formatNumberValue para converter corretamente valores com máscara brasileira\n            const numericValue = Number(value.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n            return numericValue >= 30000;\n        }\n        return true;\n    }),\n    prazoInvestimento: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Prazo do investimento obrigat\\xf3rio\"),\n    taxaRemuneracao: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Taxa de remunera\\xe7\\xe3o obrigat\\xf3ria\"),\n    comprarCom: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Forma de compra obrigat\\xf3ria\"),\n    inicioContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"In\\xedcio do contrato obrigat\\xf3rio\"),\n    fimContrato: yup__WEBPACK_IMPORTED_MODULE_12__.string(),\n    perfil: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Perfil obrigat\\xf3rio\"),\n    debenture: yup__WEBPACK_IMPORTED_MODULE_12__.string().required(\"Deb\\xeanture obrigat\\xf3ria\"),\n    observacoes: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    quotaQuantity: yup__WEBPACK_IMPORTED_MODULE_12__.string().optional(),\n    // Campos de IR (obrigatórios para MUTUO)\n    irDeposito: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional(),\n    irDesconto: yup__WEBPACK_IMPORTED_MODULE_12__.boolean().optional()\n});\nfunction AlterarContrato() {\n    var _contractData_contracts_, _contractData_contracts;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const tipo = searchParams.get(\"tipo\"); // \"rentabilidade\" ou \"modalidade\"\n    const investorId = searchParams.get(\"investorId\");\n    const userProfile = (0,_functions_getUserData__WEBPACK_IMPORTED_MODULE_16__.getUserProfile)();\n    // Buscar dados do contrato atual\n    const { data: contractData, isLoading: isLoadingContract } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery)({\n        queryKey: [\n            \"contract\",\n            investorId\n        ],\n        queryFn: async ()=>{\n            if (!investorId) return null;\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].get(\"/contract/\".concat(investorId));\n            return response.data;\n        },\n        enabled: !!investorId\n    });\n    // Form is managed by react-hook-form with yup resolver\n    const { register, handleSubmit, watch, setValue, trigger, formState: { errors, isValid, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm)({\n        resolver: (0,_hookform_resolvers_yup__WEBPACK_IMPORTED_MODULE_11__.yupResolver)(validationSchema),\n        mode: \"all\",\n        defaultValues: {\n            //PÁGINA 1\n            tipoContrato: \"pf\",\n            nomeCompleto: \"\",\n            identidade: \"\",\n            celular: \"\",\n            cpf: \"\",\n            dataNascimento: \"\",\n            nomeMae: \"\",\n            email: \"\",\n            cep: \"\",\n            cidade: \"\",\n            endereco: \"\",\n            numero: \"\",\n            complemento: \"\",\n            estado: \"\",\n            banco: \"\",\n            conta: \"\",\n            agencia: \"\",\n            chavePix: \"\",\n            observacoes: \"\",\n            //PÁGINA 2\n            modalidade: \"MUTUO\",\n            valorInvestimento: \"\",\n            prazoInvestimento: \"\",\n            taxaRemuneracao: \"\",\n            comprarCom: \"pix\",\n            inicioContrato: \"\",\n            fimContrato: \"\",\n            perfil: \"conservative\",\n            debenture: \"n\",\n            quotaQuantity: \"\",\n            // Opções de IR (mutuamente exclusivas)\n            irDeposito: false,\n            irDesconto: false\n        }\n    });\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(1); // 1 = parte 1, 2 = parte 2\n    const [aliquotaIR, setAliquotaIR] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\");\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [valorComplementar, setValorComplementar] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(0);\n    const [showComplementMessage, setShowComplementMessage] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [irCalculado, setIrCalculado] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se o IR foi calculado\n    const [irOpcaoSelecionada, setIrOpcaoSelecionada] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false); // Controla se uma opção de IR foi selecionada\n    const [valorOriginalInvestimento, setValorOriginalInvestimento] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Armazena valor original antes do desconto\n    const [scpValidationError, setScpValidationError] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(\"\"); // Erro de validação SCP customizado\n    // Função para validar valor SCP\n    const validateScpValue = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)((valor, modalidade)=>{\n        if (modalidade !== \"SCP\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Se o valor estiver vazio, limpar erro e retornar true (será validado pelo Yup)\n        if (!valor || valor.trim() === \"\") {\n            setScpValidationError(\"\");\n            return true;\n        }\n        const valorNumerico = Number(valor.replaceAll(\".\", \"\").replaceAll(\",\", \".\")) || 0;\n        // Se há mensagem de complemento, valor parcial é permitido\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - showComplementMessage:\", showComplementMessage);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valor:\", valor);\n        console.log(\"\\uD83D\\uDD0D Valida\\xe7\\xe3o SCP - valorNumerico:\", valorNumerico);\n        if (showComplementMessage) {\n            console.log(\"✅ SCP: Valor parcial permitido devido ao dep\\xf3sito adicional\");\n            setScpValidationError(\"\");\n            return true;\n        }\n        // Validação normal: múltiplo de 5.000 e mínimo de 30.000\n        if (valorNumerico < 30000) {\n            console.log(\"❌ SCP: Valor menor que R$ 30.000\");\n            setScpValidationError(\"Para contratos SCP, o valor m\\xednimo \\xe9 de R$ 30.000\");\n            return false;\n        }\n        if (valorNumerico % 5000 !== 0) {\n            console.log(\"❌ SCP: Valor n\\xe3o \\xe9 m\\xfaltiplo de R$ 5.000\");\n            setScpValidationError(\"Para contratos SCP, o valor deve ser m\\xfaltiplo de R$ 5.000\");\n            return false;\n        }\n        console.log(\"✅ SCP: Valor v\\xe1lido\");\n        setScpValidationError(\"\");\n        return true;\n    }, [\n        showComplementMessage\n    ]);\n    // Identificar modalidade atual do contrato\n    const currentContractModality = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        return contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\";\n    }, [\n        contractData\n    ]);\n    // Pré-preencher formulário quando os dados chegarem\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (contractData && !isLoadingContract) {\n            var _contractData_contracts;\n            const contract = (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0]; // Pega o primeiro contrato\n            if (contract) {\n                var _contract_purchasedWith, _contract_purchasedWith1, _contract_purchasedWith2, _contract_purchasedWith3;\n                // Dados pessoais\n                setValue(\"nomeCompleto\", contract.investorName || \"\");\n                // Aplicar máscara no documento (CPF/CNPJ)\n                const documento = contractData.document || \"\";\n                if (documento) {\n                    const documentoComMascara = documento.length <= 11 ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(documento) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(documento);\n                    setValue(\"cpf\", documentoComMascara);\n                } else {\n                    setValue(\"cpf\", \"\");\n                }\n                setValue(\"identidade\", contractData.rg || \"\");\n                setValue(\"email\", contractData.email || \"\");\n                // Aplicar máscara no telefone (preservando o formato original para o backend)\n                const telefone = contractData.phone || \"\";\n                setValue(\"celular\", telefone ? phoneWithCountryMask(telefone) : \"\");\n                // Pré-preencher nome da mãe e data de nascimento com dados do investidor\n                setValue(\"nomeMae\", contractData.motherName || \"\");\n                setValue(\"dataNascimento\", contractData.birthDate || \"\");\n                // Endereço\n                const cep = contractData.zipCode || \"\";\n                setValue(\"cep\", cep ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(cep) : \"\");\n                setValue(\"cidade\", contractData.city || \"\");\n                setValue(\"endereco\", contractData.address || \"\");\n                setValue(\"numero\", contractData.addressNumber || \"\");\n                setValue(\"complemento\", contractData.complement || \"\");\n                setValue(\"estado\", contractData.state || \"\");\n                // Dados bancários\n                setValue(\"banco\", contractData.bank || \"\");\n                setValue(\"agencia\", contractData.branch || \"\");\n                setValue(\"conta\", contractData.accountNumber || \"\");\n                setValue(\"chavePix\", contractData.phone || contractData.email || \"\"); // Usar telefone ou email como PIX padrão\n                // Dados do investimento (do contrato atual)\n                const valorInvestimento = contract.investmentValue || \"\";\n                // Aplicar máscara no valor do investimento se houver valor\n                setValue(\"valorInvestimento\", valorInvestimento ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorInvestimento) : \"\");\n                setValue(\"taxaRemuneracao\", contract.investmentYield || \"\");\n                setValue(\"prazoInvestimento\", contract.investmentTerm || \"\");\n                // Mapear forma de pagamento\n                let paymentMethod = \"pix\"; // padrão\n                if (((_contract_purchasedWith = contract.purchasedWith) === null || _contract_purchasedWith === void 0 ? void 0 : _contract_purchasedWith.includes(\"TRANSFER\\xcaNCIA\")) || ((_contract_purchasedWith1 = contract.purchasedWith) === null || _contract_purchasedWith1 === void 0 ? void 0 : _contract_purchasedWith1.includes(\"BANC\\xc1RIA\"))) {\n                    paymentMethod = \"bank_transfer\";\n                } else if ((_contract_purchasedWith2 = contract.purchasedWith) === null || _contract_purchasedWith2 === void 0 ? void 0 : _contract_purchasedWith2.includes(\"PIX\")) {\n                    paymentMethod = \"pix\";\n                } else if ((_contract_purchasedWith3 = contract.purchasedWith) === null || _contract_purchasedWith3 === void 0 ? void 0 : _contract_purchasedWith3.includes(\"BOLETO\")) {\n                    paymentMethod = \"boleto\";\n                }\n                setValue(\"comprarCom\", paymentMethod);\n                setValue(\"inicioContrato\", contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart).format(\"YYYY-MM-DD\") : \"\");\n                // Modalidade baseada nas tags\n                setValue(\"modalidade\", contract.tags === \"P2P\" ? \"MUTUO\" : \"SCP\");\n                console.log(\"Formul\\xe1rio pr\\xe9-preenchido com dados do contrato:\", contractData);\n            }\n        }\n    }, [\n        contractData,\n        isLoadingContract,\n        setValue\n    ]);\n    // Calcular quantidade de cotas automaticamente para SCP baseado no valor do investimento\n    const modalidade = watch(\"modalidade\");\n    const valorInvestimento = watch(\"valorInvestimento\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\") {\n            // Usar formatNumberValue para converter corretamente o valor com máscara brasileira\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            if (valorNumerico > 0) {\n                const cotas = Math.floor(valorNumerico / 5000);\n                setValue(\"quotaQuantity\", cotas.toString());\n            } else {\n                setValue(\"quotaQuantity\", \"0\");\n            }\n        } else if (modalidade === \"SCP\") {\n            // Se não há valor ou valor está vazio, limpar cotas\n            setValue(\"quotaQuantity\", \"0\");\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        setValue\n    ]);\n    // Verificar se há contratos válidos (ativos) para habilitar o botão\n    const hasActiveContracts = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return false;\n        const contract = contractData.contracts[0];\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        // Verificar se o contrato principal está ativo\n        const mainContractActive = contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\";\n        // Verificar se há pelo menos um aditivo ativo\n        let hasActiveAddendum = false;\n        if (contract.addendum && contract.addendum.length > 0) {\n            hasActiveAddendum = contract.addendum.some((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                return addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\";\n            });\n        }\n        return mainContractActive || hasActiveAddendum;\n    }, [\n        contractData\n    ]);\n    // Funções para navegação das opções de upgrade\n    const handleRentabilityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=rentabilidade&investorId=\".concat(investorId));\n    };\n    const handleModalityClick = ()=>{\n        router.push(\"/contratos/alterar?tipo=modalidade&investorId=\".concat(investorId));\n    };\n    // Componente para as opções de upgrade\n    const upgradeContractOptions = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-6\",\n                    children: \"Escolha o tipo de altera\\xe7\\xe3o\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleRentabilityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Rentabilidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a rentabilidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:flex-row flex-col gap-2 justify-between cursor-pointer\",\n                    onClick: handleModalityClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#1C1C1C] w-full p-5 rounded-lg border-[#FF9900] border hover:bg-[#2C2C2C] transition-colors\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-bold text-xl leading-[18px] weight-700 text-[20px] lh-[18px] mb-4 text-white\",\n                                            children: \"Mudan\\xe7a de Modalidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ml-3 font-regular weight-400 text-[15px] lh-[18px] text-gray-300\",\n                                            children: \"Clique aqui para mudar a modalidade do contrato do investidor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white text-lg font-bold\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, this);\n    };\n    function calcularAliquotaIR() {\n        let showToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        // Pega o prazo do investimento em meses e converte para dias\n        const prazoMeses = Number(watch(\"prazoInvestimento\"));\n        if (!prazoMeses || isNaN(prazoMeses)) {\n            setAliquotaIR(\"\");\n            setIrCalculado(false);\n            if (showToast) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warn(\"Preencha o prazo do investimento para calcular o IR.\");\n            }\n            return;\n        }\n        const prazoDias = prazoMeses * 30;\n        let aliquota = \"\";\n        if (prazoDias <= 180) {\n            aliquota = \"22,5%\";\n        } else if (prazoDias <= 360) {\n            aliquota = \"20%\";\n        } else if (prazoDias <= 720) {\n            aliquota = \"17,5%\";\n        } else {\n            aliquota = \"15%\";\n        }\n        setAliquotaIR(aliquota);\n        setIrCalculado(true);\n        if (showToast) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"IR calculado com sucesso! Agora selecione uma das op\\xe7\\xf5es de IR abaixo.\");\n        }\n    }\n    const onSubmit = async (data)=>{\n        console.log(\"Iniciando submiss\\xe3o do formul\\xe1rio...\", data);\n        // Validações específicas para MUTUO ou quando contrato anterior era MUTUO\n        if (data.modalidade === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") {\n            if (!irCalculado) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Clique no bot\\xe3o 'Calcular IR' antes de prosseguir.\");\n                return;\n            }\n            if (!data.irDeposito && !data.irDesconto) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Obrigat\\xf3rio: Selecione uma das op\\xe7\\xf5es de IR (dep\\xf3sito ou desconto).\");\n                return;\n            }\n        }\n        // Validação específica para SCP usando função customizada\n        if (!validateScpValue(data.valorInvestimento, data.modalidade)) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(scpValidationError);\n            return;\n        }\n        // Validar se está tentando mudar de SCP para MUTUO\n        if (currentContractModality === \"SCP\" && data.modalidade === \"MUTUO\") {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n            return;\n        }\n        console.log(\"Investor ID:\", investorId);\n        try {\n            const isPJ = data.tipoContrato === \"pj\";\n            // Máscara correta para CPF/CNPJ\n            const documento = isPJ ? data.cpf.replace(/\\D/g, \"\") // CNPJ\n             : data.cpf.replace(/\\D/g, \"\"); // CPF\n            console.log(\"Dados processados:\", {\n                isPJ,\n                documento,\n                userProfile\n            });\n            // Criar objeto usando a estrutura do CreateNewContractDto\n            const requestData = {\n                role: (()=>{\n                    // Mapear os roles corretamente\n                    if (userProfile.name === \"superadmin\" || userProfile.name === \"admin\") {\n                        return \"broker\";\n                    }\n                    if (userProfile.name === \"advisor\") {\n                        return \"advisor\";\n                    }\n                    if (userProfile.name === \"broker\") {\n                        return \"broker\";\n                    }\n                    // Fallback para broker\n                    return \"broker\";\n                })(),\n                personType: isPJ ? \"PJ\" : \"PF\",\n                contractType: data.modalidade,\n                advisors: (()=>{\n                    // Se o usuário for advisor, adicionar ele mesmo como advisor\n                    if (userProfile.name === \"advisor\") {\n                        return [\n                            {\n                                advisorId: userProfile.roleId,\n                                rate: 100 // 100% para o próprio advisor\n                            }\n                        ];\n                    }\n                    // Se for broker ou superadmin, deixar vazio (será preenchido pelo backend)\n                    return [];\n                })(),\n                investment: {\n                    amount: parseInt(data.valorInvestimento.replace(/\\D/g, \"\")) || 0,\n                    monthlyRate: parseFloat(data.taxaRemuneracao) || 0,\n                    durationInMonths: 12,\n                    paymentMethod: \"pix\",\n                    endDate: (()=>{\n                        // Calcular data de fim baseada na data atual + 12 meses\n                        const now = new Date();\n                        const endDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());\n                        return endDate.toISOString().split(\"T\")[0]; // Formato YYYY-MM-DD\n                    })(),\n                    profile: \"moderate\",\n                    quotaQuantity: data.modalidade === \"SCP\" ? Math.max(1, parseInt(data.quotaQuantity || \"1\") || 1) : undefined,\n                    isDebenture: false\n                },\n                bankAccount: {\n                    bank: data.banco || \"\",\n                    agency: data.agencia || \"\",\n                    account: data.conta || \"\",\n                    pix: data.chavePix || \"\"\n                },\n                individual: isPJ ? undefined : {\n                    fullName: data.nomeCompleto || \"\",\n                    cpf: documento,\n                    rg: (()=>{\n                        const rg = data.identidade;\n                        if (!rg) return \"*********\";\n                        const rgLimpo = rg.toString().replace(/\\D/g, \"\");\n                        return rgLimpo || \"*********\";\n                    })(),\n                    issuingAgency: \"SSP\",\n                    nationality: \"Brasileira\",\n                    occupation: \"Investidor\",\n                    birthDate: (()=>{\n                        const birthDate = data.dataNascimento;\n                        if (!birthDate) return new Date().toISOString().split(\"T\")[0];\n                        if (birthDate.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                            return birthDate;\n                        }\n                        const date = new Date(birthDate);\n                        return date.toISOString().split(\"T\")[0];\n                    })(),\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    motherName: data.nomeMae || \"\",\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    }\n                },\n                company: isPJ ? {\n                    companyName: data.nomeCompleto || \"\",\n                    cnpj: documento,\n                    legalType: \"LTDA\",\n                    email: data.email || \"<EMAIL>\",\n                    phone: (()=>{\n                        const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                        if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                            return \"55\" + cleanPhone;\n                        }\n                        return cleanPhone;\n                    })(),\n                    address: {\n                        street: data.rua || \"Rua Principal\",\n                        neighborhood: data.bairro || \"Centro\",\n                        city: data.cidade || \"Cidade\",\n                        state: data.estado || \"BA\",\n                        postalCode: data.cep ? data.cep.replace(/\\D/g, \"\") : \"00000000\",\n                        number: data.numero || \"1\",\n                        complement: data.complemento || \"\"\n                    },\n                    representative: {\n                        fullName: data.nomeCompleto || \"\",\n                        cpf: \"00000000000\",\n                        rg: \"*********\",\n                        issuingAgency: \"SSP\",\n                        nationality: \"Brasileira\",\n                        occupation: \"Representante\",\n                        birthDate: \"1990-01-01\",\n                        email: data.email || \"<EMAIL>\",\n                        phone: (()=>{\n                            const cleanPhone = data.celular.replace(/\\D/g, \"\");\n                            if (cleanPhone.length === 11 && !cleanPhone.startsWith(\"55\")) {\n                                return \"55\" + cleanPhone;\n                            }\n                            return cleanPhone;\n                        })(),\n                        motherName: data.nomeMae || \"\"\n                    }\n                } : undefined\n            };\n            console.log(\"=== PAYLOAD COMPLETO ===\");\n            console.log(\"UserProfile:\", userProfile);\n            console.log(\"Role enviado:\", requestData.role);\n            console.log(\"PersonType enviado:\", requestData.personType);\n            console.log(\"ContractType enviado:\", requestData.contractType);\n            console.log(\"Advisors enviado:\", requestData.advisors);\n            console.log(\"QuotaQuantity enviado:\", requestData.investment.quotaQuantity);\n            console.log(\"Enviando dados para API...\", JSON.stringify(requestData, null, 2));\n            createContractMutation.mutate(requestData);\n        } catch (error) {\n            console.error(\"Erro ao processar dados:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Erro ao processar dados do contrato\");\n        }\n    };\n    // Validação e avanço para parte 2\n    // Lista dos campos do step 1\n    const camposStep1 = [\n        \"tipoContrato\",\n        \"nomeCompleto\",\n        \"identidade\",\n        \"celular\",\n        \"cpf\",\n        // \"dataNascimento\", // Opcional - não vem da API\n        // \"nomeMae\", // Opcional - não vem da API\n        \"email\",\n        \"cep\",\n        \"cidade\",\n        \"endereco\",\n        \"numero\",\n        // \"complemento\", // Opcional\n        \"estado\",\n        \"banco\",\n        \"conta\",\n        \"agencia\",\n        \"chavePix\"\n    ];\n    const handleNext = async ()=>{\n        console.log(\"Validando campos da p\\xe1gina 1:\", camposStep1);\n        const valid = await trigger(camposStep1);\n        console.log(\"Resultado da valida\\xe7\\xe3o:\", valid);\n        console.log(\"Erros atuais:\", errors);\n        if (valid) {\n            setStep(2);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Dados da p\\xe1gina 1 validados com sucesso!\");\n        } else {\n            // Contar quantos campos obrigatórios estão com erro\n            const camposComErro = camposStep1.filter((campo)=>errors[campo]);\n            const quantidadeErros = camposComErro.length;\n            if (quantidadeErros > 0) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha \".concat(quantidadeErros, \" campo(s) obrigat\\xf3rio(s) antes de continuar.\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios antes de continuar.\");\n            }\n        }\n    };\n    const createContractMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: async (data)=>{\n            console.log(\"Mutation - Criando novo contrato\");\n            console.log(\"Dados enviados:\", data);\n            const response = await _core_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].post(\"/contract/manual\", data);\n            console.log(\"Resposta da API:\", response.data);\n            return response.data;\n        },\n        onSuccess: (response)=>{\n            console.log(\"Contrato criado com sucesso:\", response);\n            // Mostrar toast de sucesso com informações do novo contrato\n            const newContractId = response === null || response === void 0 ? void 0 : response.id;\n            if (newContractId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! ID: \".concat(newContractId.substring(0, 8), \"... Redirecionando para a home...\"));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Contrato criado com sucesso! Redirecionando para a home...\");\n            }\n            // Aguardar um pouco para o usuário ver a mensagem e depois redirecionar\n            setIsRedirecting(true);\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 3000); // 3 segundos para dar tempo de ler a mensagem\n        },\n        onError: (error)=>{\n            (0,_functions_returnError__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(error, \"Erro ao atualizar o contrato\");\n        }\n    });\n    const term = watch(\"prazoInvestimento\");\n    const initDate = watch(\"inicioContrato\");\n    const endDate = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (initDate && term) {\n            const endData = (0,_functions_getDataFilter__WEBPACK_IMPORTED_MODULE_13__.getFinalDataWithMount)({\n                investDate: String(term),\n                startDate: initDate\n            });\n            const formattedEndDate = moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"DD/MM/YYYY\");\n            // Atualiza o campo fimContrato automaticamente\n            setValue(\"fimContrato\", moment__WEBPACK_IMPORTED_MODULE_7___default()(endData, \"DD-MM-YYYY\").format(\"YYYY-MM-DD\"), {\n                shouldValidate: true\n            });\n            return formattedEndDate;\n        }\n        return \"\";\n    }, [\n        initDate,\n        term,\n        setValue\n    ]);\n    // Calcular dados do contrato atual e adendos para o detalhamento\n    const contractDetails = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _contractData_contracts, _contract_contractStatus;\n        if (!contractData || !((_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : _contractData_contracts[0])) return null;\n        const contract = contractData.contracts[0];\n        const details = [];\n        // Função para calcular IR baseado no prazo\n        const calculateIR = (amount, rate, days)=>{\n            const totalReturn = amount * rate * (days / 30) / 100;\n            let irRate = 0;\n            if (days <= 180) irRate = 22.5;\n            else if (days <= 360) irRate = 20;\n            else if (days <= 720) irRate = 17.5;\n            else irRate = 15;\n            const irValue = totalReturn * irRate / 100;\n            return {\n                totalReturn,\n                irRate,\n                irValue\n            };\n        };\n        // Contrato principal - apenas se estiver ATIVO\n        const contractStatus = (_contract_contractStatus = contract.contractStatus) === null || _contract_contractStatus === void 0 ? void 0 : _contract_contractStatus.toUpperCase();\n        if (contractStatus === \"ACTIVE\" || contractStatus === \"ATIVO\") {\n            const mainAmount = parseInt(contract.investmentValue) || 0;\n            const mainRate = parseFloat(contract.investmentYield) || 0;\n            const mainStartDate = contract.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainEndDate = contract.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(contract.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n            const mainDays = mainEndDate.diff(mainStartDate, \"days\");\n            const mainIR = calculateIR(mainAmount, mainRate, mainDays);\n            details.push({\n                type: \"Contrato Inicial\",\n                amount: mainAmount,\n                daysRentabilized: mainDays,\n                monthlyRate: mainRate,\n                irRate: mainIR.irRate,\n                irValue: mainIR.irValue,\n                totalReturn: mainIR.totalReturn,\n                status: contract.contractStatus\n            });\n        }\n        // Adendos - apenas os que estão ATIVOS\n        if (contract.addendum && contract.addendum.length > 0) {\n            let activeAddendumIndex = 1; // Contador para aditivos ativos\n            contract.addendum.forEach((addendum)=>{\n                var _addendum_contractStatus;\n                const addendumStatus = (_addendum_contractStatus = addendum.contractStatus) === null || _addendum_contractStatus === void 0 ? void 0 : _addendum_contractStatus.toUpperCase();\n                // Incluir apenas aditivos com status ACTIVE ou ATIVO\n                if (addendumStatus === \"ACTIVE\" || addendumStatus === \"ATIVO\") {\n                    const addAmount = parseFloat(addendum.investmentValue) || 0;\n                    const addRate = parseFloat(addendum.investmentYield) || 0;\n                    const addStartDate = addendum.contractStart ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractStart) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addEndDate = addendum.contractEnd ? moment__WEBPACK_IMPORTED_MODULE_7___default()(addendum.contractEnd) : moment__WEBPACK_IMPORTED_MODULE_7___default()();\n                    const addDays = addEndDate.diff(addStartDate, \"days\");\n                    const addIR = calculateIR(addAmount, addRate, addDays);\n                    details.push({\n                        type: \"Aditivo \".concat(activeAddendumIndex),\n                        amount: addAmount,\n                        daysRentabilized: addDays,\n                        monthlyRate: addRate,\n                        irRate: addIR.irRate,\n                        irValue: addIR.irValue,\n                        totalReturn: addIR.totalReturn,\n                        status: addendum.contractStatus\n                    });\n                    activeAddendumIndex++; // Incrementar apenas para aditivos ativos\n                }\n            });\n        }\n        // Calcular total do IR\n        const totalIR = details.reduce((sum, detail)=>sum + detail.irValue, 0);\n        return {\n            details,\n            totalIR,\n            contractType: contract.tags || \"MUTUO\"\n        };\n    }, [\n        contractData\n    ]);\n    // Calcular valor após desconto de IR e validar para SCP\n    const irDesconto = watch(\"irDesconto\");\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D UseEffect SCP - Condi\\xe7\\xf5es:\");\n        console.log(\"  modalidade === 'SCP':\", modalidade === \"SCP\");\n        console.log(\"  valorInvestimento:\", valorInvestimento);\n        console.log(\"  valorInvestimento.trim() !== '':\", valorInvestimento && valorInvestimento.trim() !== \"\");\n        console.log(\"  irDesconto:\", irDesconto);\n        console.log(\"  contractDetails?.totalIR:\", contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR);\n        if (modalidade === \"SCP\" && valorInvestimento && valorInvestimento.trim() !== \"\" && irDesconto && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR)) {\n            const valorNumerico = (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valorInvestimento) || 0;\n            const valorAposDesconto = valorNumerico - contractDetails.totalIR;\n            console.log(\"=== C\\xc1LCULO SCP COM DESCONTO IR ===\");\n            console.log(\"Valor original:\", valorNumerico);\n            console.log(\"IR a descontar:\", contractDetails.totalIR);\n            console.log(\"Valor ap\\xf3s desconto:\", valorAposDesconto);\n            // Verificar se o valor após desconto é divisível por 5000\n            const resto = valorAposDesconto % 5000;\n            if (resto !== 0) {\n                // Arredondar para BAIXO (menos cotas) - pegar apenas cotas completas\n                const cotasCompletas = Math.floor(valorAposDesconto / 5000);\n                const valorCotasCompletas = cotasCompletas * 5000;\n                const valorComplementarNecessario = valorAposDesconto - valorCotasCompletas;\n                console.log(\"Resto:\", resto);\n                console.log(\"Cotas completas:\", cotasCompletas);\n                console.log(\"Valor das cotas completas:\", valorCotasCompletas);\n                console.log(\"Valor complementar necess\\xe1rio:\", valorComplementarNecessario);\n                // Atualizar o valor do investimento para o valor das cotas completas + IR\n                const novoValorInvestimento = valorCotasCompletas + contractDetails.totalIR;\n                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)((novoValorInvestimento * 100).toString());\n                setValue(\"valorInvestimento\", valorFormatado);\n                // Atualizar quantidade de cotas\n                setValue(\"quotaQuantity\", cotasCompletas.toString());\n                setValorComplementar(valorComplementarNecessario);\n                setShowComplementMessage(true);\n                console.log(\"✅ showComplementMessage ativado - valor parcial permitido\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.warning(\"⚠️ Valor ajustado para \".concat(cotasCompletas, \" cotas completas. Valor complementar de \").concat(new Intl.NumberFormat(\"pt-BR\", {\n                    style: \"currency\",\n                    currency: \"BRL\"\n                }).format(valorComplementarNecessario), \" ser\\xe1 necess\\xe1rio.\"));\n            } else {\n                setShowComplementMessage(false);\n                setValorComplementar(0);\n            }\n        } else {\n            setShowComplementMessage(false);\n            setValorComplementar(0);\n        }\n    }, [\n        modalidade,\n        valorInvestimento,\n        irDesconto,\n        contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR,\n        setValue\n    ]);\n    // Validação em tempo real para SCP\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (valorInvestimento && modalidade) {\n            validateScpValue(valorInvestimento, modalidade);\n        }\n    }, [\n        valorInvestimento,\n        modalidade,\n        validateScpValue,\n        showComplementMessage\n    ]);\n    // Reset IR states quando modalidade mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        if (modalidade === \"SCP\") {\n            // Para SCP, não precisa de IR\n            setIrCalculado(true);\n            setIrOpcaoSelecionada(true);\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        } else if (modalidade === \"MUTUO\") {\n            // Para MUTUO, resetar estados\n            setIrCalculado(false);\n            setIrOpcaoSelecionada(false);\n            setAliquotaIR(\"\");\n            setValue(\"irDeposito\", false);\n            setValue(\"irDesconto\", false);\n            setValorOriginalInvestimento(\"\"); // Reset valor original\n        }\n    }, [\n        modalidade,\n        setValue\n    ]);\n    // Função auxiliar para converter string de valor para número\n    function parseValor(valor) {\n        if (!valor) return 0;\n        // Usar a função formatNumberValue que já trata corretamente valores brasileiros\n        return (0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(valor) || 0;\n    }\n    // Valor investido preenchido\n    const valorInvestido = parseValor(watch(\"valorInvestimento\"));\n    // Alíquota em número\n    const aliquotaNumber = aliquotaIR ? parseFloat(aliquotaIR.replace(\"%\", \"\").replace(\",\", \".\")) : 0;\n    // Valor total de IR\n    const valorTotalIR = valorInvestido && aliquotaNumber ? valorInvestido * (aliquotaNumber / 100) : 0;\n    // PARTE 1: Dados pessoais, bancários, observações\n    const renderStep1 = ()=>{\n        var _errors_nomeCompleto, _errors_identidade, _errors_celular, _errors_cpf, _errors_dataNascimento, _errors_nomeMae, _errors_email, _errors_cep, _errors_cidade, _errors_estado, _errors_endereco, _errors_numero, _errors_complemento, _errors_banco, _errors_conta, _errors_agencia, _errors_chavePix;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Pessoais\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 969,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-1/2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white mb-1\",\n                                        children: \"Tipo de Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        value: watch(\"tipoContrato\"),\n                                        onChange: (e)=>setValue(\"tipoContrato\", e.target.value, {\n                                                shouldValidate: true\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pf\",\n                                                children: \"Pessoa F\\xedsica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 978,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"pj\",\n                                                children: \"Pessoa Jur\\xeddica\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 979,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 972,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeCompleto\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeCompleto,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeCompleto = errors.nomeCompleto) === null || _errors_nomeCompleto === void 0 ? void 0 : _errors_nomeCompleto.message,\n                                    label: \"Nome Completo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 982,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"identidade\",\n                                            width: \"100%\",\n                                            error: !!errors.identidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_identidade = errors.identidade) === null || _errors_identidade === void 0 ? void 0 : _errors_identidade.message,\n                                            label: \"Identidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 994,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"celular\",\n                                            width: \"100%\",\n                                            maxLength: 18,\n                                            error: !!errors.celular,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_celular = errors.celular) === null || _errors_celular === void 0 ? void 0 : _errors_celular.message,\n                                            label: \"Celular\",\n                                            onChange: (e)=>{\n                                                setValue(\"celular\", phoneWithCountryMask(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 992,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cpf\",\n                                            width: \"100%\",\n                                            error: !!errors.cpf,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cpf = errors.cpf) === null || _errors_cpf === void 0 ? void 0 : _errors_cpf.message,\n                                            label: \"CPF/CNPJ\",\n                                            onChange: (e)=>{\n                                                const value = watch(\"tipoContrato\") === \"pj\" ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cnpjMask)(e.target.value) : (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cpfMask)(e.target.value);\n                                                setValue(\"cpf\", value, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1020,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            type: \"date\",\n                                            register: register,\n                                            name: \"dataNascimento\",\n                                            width: \"100%\",\n                                            error: !!errors.dataNascimento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_dataNascimento = errors.dataNascimento) === null || _errors_dataNascimento === void 0 ? void 0 : _errors_dataNascimento.message,\n                                            label: \"Data de Nascimento (opcional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1033,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1018,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"nomeMae\",\n                                    width: \"100%\",\n                                    error: !!errors.nomeMae,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_nomeMae = errors.nomeMae) === null || _errors_nomeMae === void 0 ? void 0 : _errors_nomeMae.message,\n                                    label: \"Nome da M\\xe3e (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1046,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1045,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"email\",\n                                    width: \"100%\",\n                                    error: !!errors.email,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_email = errors.email) === null || _errors_email === void 0 ? void 0 : _errors_email.message,\n                                    label: \"E-mail\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1056,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1055,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cep\",\n                                            width: \"100%\",\n                                            error: !!errors.cep,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cep = errors.cep) === null || _errors_cep === void 0 ? void 0 : _errors_cep.message,\n                                            label: \"CEP\",\n                                            onChange: (e)=>{\n                                                setValue(\"cep\", (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.cepMask)(e.target.value), {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"cidade\",\n                                            width: \"100%\",\n                                            error: !!errors.cidade,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_cidade = errors.cidade) === null || _errors_cidade === void 0 ? void 0 : _errors_cidade.message,\n                                            label: \"Cidade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1079,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1065,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"estado\",\n                                        width: \"100%\",\n                                        error: !!errors.estado,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_estado = errors.estado) === null || _errors_estado === void 0 ? void 0 : _errors_estado.message,\n                                        label: \"Estado\",\n                                        placeholder: \"ex: SC\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1092,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1090,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    register: register,\n                                    name: \"endereco\",\n                                    width: \"100%\",\n                                    error: !!errors.endereco,\n                                    errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_endereco = errors.endereco) === null || _errors_endereco === void 0 ? void 0 : _errors_endereco.message,\n                                    label: \"Endere\\xe7o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1104,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"numero\",\n                                            width: \"100%\",\n                                            error: !!errors.numero,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_numero = errors.numero) === null || _errors_numero === void 0 ? void 0 : _errors_numero.message,\n                                            label: \"N\\xfamero\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1115,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full md:w-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"complemento\",\n                                            width: \"100%\",\n                                            error: !!errors.complemento,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_complemento = errors.complemento) === null || _errors_complemento === void 0 ? void 0 : _errors_complemento.message,\n                                            label: \"Complemento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1124,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 971,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 970,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Dados Banc\\xe1rios\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1138,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"banco\",\n                                        width: \"100%\",\n                                        error: !!errors.banco,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_banco = errors.banco) === null || _errors_banco === void 0 ? void 0 : _errors_banco.message,\n                                        label: \"Nome do Banco\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1142,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1141,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"conta\",\n                                        width: \"100%\",\n                                        error: !!errors.conta,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_conta = errors.conta) === null || _errors_conta === void 0 ? void 0 : _errors_conta.message,\n                                        label: \"Conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1152,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1140,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"agencia\",\n                                        width: \"100%\",\n                                        error: !!errors.agencia,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_agencia = errors.agencia) === null || _errors_agencia === void 0 ? void 0 : _errors_agencia.message,\n                                        label: \"Ag\\xeancia\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1164,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full md:w-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"chavePix\",\n                                        width: \"100%\",\n                                        error: !!errors.chavePix,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_chavePix = errors.chavePix) === null || _errors_chavePix === void 0 ? void 0 : _errors_chavePix.message,\n                                        label: \"Chave PIX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1174,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1173,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1162,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1139,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white mb-4\",\n                    children: \"Observa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1186,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 relative overflow-visible border border-[#FF9900] mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            ...register(\"observacoes\"),\n                            className: \"w-full h-32 rounded-lg bg-black border border-[#FF9900] text-white p-4 resize-none focus:outline-none focus:ring-2 focus:ring-[#FF9900]\",\n                            placeholder: \"Observa\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1188,\n                            columnNumber: 9\n                        }, this),\n                        errors.observacoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-500 text-xs\",\n                            children: errors.observacoes.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1194,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1187,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        size: \"lg\",\n                        type: \"button\",\n                        className: \"bg-[#FF9900] text-white px-8 py-2 rounded-lg hover:bg-[#e68a00]\",\n                        onClick: handleNext,\n                        children: \"Pr\\xf3ximo\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1200,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1198,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 968,\n            columnNumber: 5\n        }, this);\n    };\n    // PARTE 2: Dados de investimento, resumo/calculadora, detalhamento\n    const renderStep2 = ()=>{\n        var _errors_valorInvestimento, _errors_valorInvestimento1, _errors_quotaQuantity, _errors_taxaRemuneracao, _errors_inicioContrato, _errors_prazoInvestimento;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit(onSubmit, (errors)=>{\n                console.log(\"Erros de valida\\xe7\\xe3o:\", errors);\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Por favor, preencha todos os campos obrigat\\xf3rios\");\n            }),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-8 flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-white mb-1 block\",\n                                    children: \"Modalidade de Investimento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            value: watch(\"modalidade\"),\n                                            onChange: (e)=>{\n                                                const newModality = e.target.value;\n                                                // Validar se está tentando mudar de SCP para MUTUO\n                                                if (currentContractModality === \"SCP\" && newModality === \"MUTUO\") {\n                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"N\\xe3o \\xe9 poss\\xedvel alterar contratos SCP para modalidade M\\xfatuo\");\n                                                    return;\n                                                }\n                                                setValue(\"modalidade\", newModality, {\n                                                    shouldValidate: true\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MUTUO\",\n                                                    children: \"M\\xfatuo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1229,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SCP\",\n                                                    children: \"SCP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1230,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1217,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-[#FF9900] text-xs mt-1\",\n                                            children: \"*Ao alterar modalidade, pressione o bot\\xe3o calcular IR\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1232,\n                                            columnNumber: 13\n                                        }, this),\n                                        currentContractModality === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-3 mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-yellow-200 text-xs\",\n                                                children: [\n                                                    \"⚠️ \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Contrato atual \\xe9 SCP:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 22\n                                                    }, this),\n                                                    \" N\\xe3o \\xe9 poss\\xedvel alterar para modalidade M\\xfatuo. Apenas upgrades dentro da mesma modalidade ou de M\\xfatuo para SCP s\\xe3o permitidos.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1236,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1235,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1216,\n                                    columnNumber: 11\n                                }, this),\n                                watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:w-1/3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"valorInvestimento\",\n                                            width: \"100%\",\n                                            error: !!errors.valorInvestimento || !!scpValidationError,\n                                            errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento = errors.valorInvestimento) === null || _errors_valorInvestimento === void 0 ? void 0 : _errors_valorInvestimento.message) || scpValidationError,\n                                            label: \"Valor do Investimento\",\n                                            placeholder: \"ex: R$ 50.000,00\",\n                                            setValue: (e)=>{\n                                                console.log(\"Input SCP - Valor recebido:\", e);\n                                                const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                                console.log(\"Input SCP - Valor com m\\xe1scara:\", maskedValue);\n                                                setValue(\"valorInvestimento\", maskedValue, {\n                                                    shouldValidate: true\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1246,\n                                            columnNumber: 15\n                                        }, this),\n                                        showComplementMessage && scpValidationError === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-400 text-xs mt-1\",\n                                            children: \"✅ Valor parcial permitido (dep\\xf3sito adicional ser\\xe1 necess\\xe1rio)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1263,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 13\n                                }, this),\n                                watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 md:w-1/3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        type: \"button\",\n                                        className: \"bg-[#FF9900] text-white px-6 py-2 rounded-lg hover:bg-[#e68a00] w-full\",\n                                        onClick: ()=>calcularAliquotaIR(),\n                                        children: \"Calcular IR\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1272,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 9\n                        }, this),\n                        watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-black border border-[#FF9900] rounded-lg p-4 mb-4 md:w-1/3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-xs mb-1\",\n                                    children: [\n                                        \"Valor investido: \",\n                                        valorInvestido ? valorInvestido.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white font-bold\",\n                                    children: [\n                                        \"Valor total de IR: \",\n                                        valorTotalIR ? valorTotalIR.toLocaleString(\"pt-BR\", {\n                                            style: \"currency\",\n                                            currency: \"BRL\"\n                                        }) : \"R$ 0,00\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1280,\n                                    columnNumber: 13\n                                }, this),\n                                aliquotaIR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#FF9900] font-bold mt-2\",\n                                    children: [\n                                        \"Al\\xedquota do IR calculada: \",\n                                        aliquotaIR\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1282,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1278,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-white\",\n                                    children: \"Detalhamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1289,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full text-white text-xs border border-[#FF9900] rounded-xl bg-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-[#232323]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Tipo de Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1294,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do Contrato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Dias Rentabilizados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1296,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Rentabilidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Taxa de IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 border-r border-[#FF9900] font-semibold\",\n                                                            children: \"Valor do IR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1299,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"px-2 py-2 font-semibold\",\n                                                            children: \"Anexos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1300,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1293,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1292,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: isLoadingContract ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4\",\n                                                        children: \"Carregando dados do contrato...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1305,\n                                                    columnNumber: 19\n                                                }, this) : contractDetails ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        contractDetails.details.length > 0 ? contractDetails.details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: detail.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1314,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.amount)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1317,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.daysRentabilized,\n                                                                            \" Dias\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.monthlyRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1324,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: [\n                                                                            detail.irRate,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1325,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2 border-r border-[#FF9900]\",\n                                                                        children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                            style: \"currency\",\n                                                                            currency: \"BRL\"\n                                                                        }).format(detail.irValue)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1326,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"px-2 py-2\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                        lineNumber: 1332,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1313,\n                                                                columnNumber: 23\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                colSpan: 7,\n                                                                className: \"text-center px-2 py-4 text-gray-400\",\n                                                                children: \"Nenhum contrato ativo encontrado para c\\xe1lculo de IR\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                lineNumber: 1337,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1336,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    colSpan: 6,\n                                                                    className: \"text-right px-2 py-2 font-semibold text-white border-t border-[#FF9900]\",\n                                                                    children: \"Valor total do IR\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1343,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-2 py-2 font-bold text-white border-t border-[#FF9900]\",\n                                                                    children: new Intl.NumberFormat(\"pt-BR\", {\n                                                                        style: \"currency\",\n                                                                        currency: \"BRL\"\n                                                                    }).format(contractDetails.totalIR)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                                    lineNumber: 1344,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                            lineNumber: 1342,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: 7,\n                                                        className: \"text-center px-2 py-4 text-gray-400\",\n                                                        children: \"Dados do contrato n\\xe3o encontrados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1354,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1353,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1303,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1291,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1290,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1288,\n                            columnNumber: 11\n                        }, this),\n                        !isLoadingContract && !hasActiveContracts && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-white font-bold mb-2\",\n                                    children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1368,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white text-sm\",\n                                    children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1369,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1367,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) !== \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: [\n                                    \"ℹ️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Modalidade SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1380,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Contratos SCP n\\xe3o possuem desconto de Imposto de Renda. A tabela de IR n\\xe3o ser\\xe1 exibida para esta modalidade.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1379,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1378,\n                            columnNumber: 11\n                        }, this),\n                        watch(\"modalidade\") === \"SCP\" && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-orange-900 border border-orange-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-orange-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Altera\\xe7\\xe3o M\\xdaTUO → SCP:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1390,\n                                        columnNumber: 18\n                                    }, this),\n                                    \" Como o contrato anterior era M\\xdaTUO, \\xe9 necess\\xe1rio tratar o IR acumulado. A tabela de IR ser\\xe1 exibida para que voc\\xea possa escolher como proceder com o desconto.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1389,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1388,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-900 border border-red-500 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-200 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"A\\xe7\\xe3o Obrigat\\xf3ria:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1400,\n                                        columnNumber: 18\n                                    }, this),\n                                    ' Clique no bot\\xe3o \"\"Calcular IR\"\" acima antes de prosseguir.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1399,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1398,\n                            columnNumber: 11\n                        }, this),\n                        (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && irCalculado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-900 border border-green-500 rounded-lg p-3 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-200 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"IR Calculado:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1410,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Agora selecione uma das op\\xe7\\xf5es abaixo (obrigat\\xf3rio):\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1409,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row gap-4 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDeposito\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDeposito\", true);\n                                                            setValue(\"irDesconto\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Restaurar valor original se existir (caso estava com desconto)\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado. O IR ser\\xe1 depositado separadamente.\");\n                                                            }\n                                                        } else {\n                                                            setValue(\"irDeposito\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDesconto\")); // Verifica se a outra está marcada\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1415,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor ir\\xe1 depositar valor referente ao IR\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1414,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center text-white text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    className: \"mr-2\",\n                                                    checked: watch(\"irDesconto\"),\n                                                    onChange: (e)=>{\n                                                        if (e.target.checked) {\n                                                            setValue(\"irDesconto\", true);\n                                                            setValue(\"irDeposito\", false); // Desmarca a outra opção\n                                                            setIrOpcaoSelecionada(true);\n                                                            // Aplicar desconto do IR automaticamente usando o valor total da tabela\n                                                            const valorAtual = watch(\"valorInvestimento\");\n                                                            if (valorAtual && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && contractDetails.totalIR > 0) {\n                                                                // Salvar valor original se ainda não foi salvo\n                                                                if (!valorOriginalInvestimento) {\n                                                                    setValorOriginalInvestimento(valorAtual);\n                                                                }\n                                                                const valorNumerico = parseValor(valorAtual);\n                                                                const valorIRTabela = contractDetails.totalIR; // Usar valor da tabela de detalhamento\n                                                                // Debug logs\n                                                                console.log(\"=== DEBUG DESCONTO IR ===\");\n                                                                console.log(\"Valor atual (string):\", valorAtual);\n                                                                console.log(\"Valor num\\xe9rico convertido:\", valorNumerico);\n                                                                console.log(\"Valor IR da tabela:\", valorIRTabela);\n                                                                const valorComDesconto = valorNumerico - valorIRTabela;\n                                                                console.log(\"Valor com desconto:\", valorComDesconto);\n                                                                // Verificar se o valor com desconto é positivo\n                                                                if (valorComDesconto <= 0) {\n                                                                    react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ Erro: O valor do IR \\xe9 maior que o valor do investimento. Verifique os valores.\");\n                                                                    setValue(\"irDesconto\", false);\n                                                                    return;\n                                                                }\n                                                                // Aplicar o valor com desconto\n                                                                // Converter para centavos (multiplicar por 100) e depois aplicar máscara\n                                                                const valorEmCentavos = Math.round(valorComDesconto * 100);\n                                                                const valorFormatado = (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(valorEmCentavos.toString());\n                                                                console.log(\"Valor em centavos:\", valorEmCentavos);\n                                                                console.log(\"Valor formatado:\", valorFormatado);\n                                                                setValue(\"valorInvestimento\", valorFormatado);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"✅ Desconto aplicado! IR de \".concat(new Intl.NumberFormat(\"pt-BR\", {\n                                                                    style: \"currency\",\n                                                                    currency: \"BRL\"\n                                                                }).format(valorIRTabela), \" foi descontado do valor do investimento.\"));\n                                                            } else {\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"⚠️ N\\xe3o foi poss\\xedvel aplicar o desconto. Verifique se h\\xe1 contratos ativos na tabela de IR.\");\n                                                                setValue(\"irDesconto\", false);\n                                                            }\n                                                            // Mostrar aviso de adendo\n                                                            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"⚠️ Aten\\xe7\\xe3o: Ao selecionar desconto do IR sobre o valor do contrato, pode ser necess\\xe1rio criar um adendo para ajuste de valor.\");\n                                                        } else {\n                                                            setValue(\"irDesconto\", false);\n                                                            setIrOpcaoSelecionada(!!watch(\"irDeposito\")); // Verifica se a outra está marcada\n                                                            // Restaurar valor original se existir\n                                                            if (valorOriginalInvestimento) {\n                                                                setValue(\"valorInvestimento\", valorOriginalInvestimento);\n                                                                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Valor original do investimento restaurado.\");\n                                                            }\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1439,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Investidor decidiu desconto do IR sobre o valor do contrato\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1438,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1413,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        watch(\"irDesconto\") && valorOriginalInvestimento && (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-900 border border-blue-500 rounded-lg p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-blue-200 font-bold mb-2\",\n                                    children: \"\\uD83D\\uDCB0 Desconto de IR Aplicado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1513,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-blue-200 text-sm space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor original:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1515,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                valorOriginalInvestimento\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1515,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor total do IR (da tabela):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1516,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                new Intl.NumberFormat(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                }).format(contractDetails.totalIR)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1516,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Valor final (com desconto):\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                    lineNumber: 1517,\n                                                    columnNumber: 18\n                                                }, this),\n                                                \" \",\n                                                watch(\"valorInvestimento\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1517,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-200 text-xs mt-2\",\n                                            children: \"ℹ️ O valor descontado \\xe9 baseado no c\\xe1lculo detalhado da tabela acima, considerando todos os contratos ativos.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1518,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1514,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1512,\n                            columnNumber: 11\n                        }, this),\n                        showComplementMessage && watch(\"modalidade\") === \"SCP\" && watch(\"irDesconto\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-900 border border-yellow-500 rounded-lg p-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-yellow-200 font-bold mb-2\",\n                                    children: \"⚠️ Ajuste de Valor Necess\\xe1rio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1528,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Conforme informado no formul\\xe1rio:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1530,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Informamos que o valor atual, ap\\xf3s a aplica\\xe7\\xe3o do desconto do Imposto de Renda sobre o valor do contrato, n\\xe3o est\\xe1 em conformidade com nossas regras de neg\\xf3cio, pois n\\xe3o \\xe9 divis\\xedvel por R$ 5.000,00.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-yellow-200 text-sm font-semibold mb-3\",\n                                    children: [\n                                        \"Para que as cotas sejam ajustadas corretamente, ser\\xe1 necess\\xe1rio o pagamento complementar no valor de\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-100 font-bold\",\n                                            children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                style: \"currency\",\n                                                currency: \"BRL\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1535,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1533,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-800 border border-yellow-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-100 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Exemplo:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Contrato de R$ 10.000 com IR de R$ 2.000 = R$ 8.000 restantes.\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 105\n                                            }, this),\n                                            \"• Sistema cadastra: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: [\n                                                    Math.floor(((0,_utils_formatNumberValue__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(watch(\"valorInvestimento\")) - ((contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.totalIR) || 0)) / 5000),\n                                                    \" cota(s)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1542,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1542,\n                                                columnNumber: 164\n                                            }, this),\n                                            \"• Valor complementar: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: valorComplementar.toLocaleString(\"pt-BR\", {\n                                                    style: \"currency\",\n                                                    currency: \"BRL\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1543,\n                                                columnNumber: 39\n                                            }, this),\n                                            \" dever\\xe1 ser pago separadamente.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1540,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-800 border border-green-600 rounded p-3 mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-100 text-sm\",\n                                        children: [\n                                            \"✅ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Valor Parcial Permitido:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1548,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Neste caso, o contrato pode ser enviado com o valor atual (n\\xe3o m\\xfaltiplo de R$ 5.000), pois o sistema reconhece que haver\\xe1 um dep\\xf3sito adicional posterior para completar as cotas.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1547,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1527,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1213,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold text-white my-8\",\n                    children: \"Dados do Investimento\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1555,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#1C1C1C] rounded-lg p-8 border border-[#FF9900] mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    watch(\"modalidade\") === \"MUTUO\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        name: \"valorInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.valorInvestimento || !!scpValidationError,\n                                        errorMessage: (errors === null || errors === void 0 ? void 0 : (_errors_valorInvestimento1 = errors.valorInvestimento) === null || _errors_valorInvestimento1 === void 0 ? void 0 : _errors_valorInvestimento1.message) || scpValidationError,\n                                        label: \"Valor do Investimento\",\n                                        setValue: (e)=>{\n                                            const maskedValue = e ? (0,_utils_masks__WEBPACK_IMPORTED_MODULE_6__.valueMask)(e) : \"\";\n                                            setValue(\"valorInvestimento\", maskedValue, {\n                                                shouldValidate: true\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1562,\n                                        columnNumber: 15\n                                    }, this),\n                                    watch(\"modalidade\") === \"SCP\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            register: register,\n                                            name: \"quotaQuantity\",\n                                            width: \"100%\",\n                                            error: !!errors.quotaQuantity,\n                                            errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_quotaQuantity = errors.quotaQuantity) === null || _errors_quotaQuantity === void 0 ? void 0 : _errors_quotaQuantity.message,\n                                            label: \"Quantidade de cotas (calculado automaticamente)\",\n                                            placeholder: \"Calculado automaticamente\",\n                                            disabled: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1578,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1577,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"text\",\n                                        name: \"taxaRemuneracao\",\n                                        width: \"100%\",\n                                        error: !!errors.taxaRemuneracao,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_taxaRemuneracao = errors.taxaRemuneracao) === null || _errors_taxaRemuneracao === void 0 ? void 0 : _errors_taxaRemuneracao.message,\n                                        label: \"Taxa de Remunera\\xe7\\xe3o Mensal %\",\n                                        placeholder: \"ex: 2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1590,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        maxDate: moment__WEBPACK_IMPORTED_MODULE_7___default()().format(\"YYYY-MM-DD\"),\n                                        name: \"inicioContrato\",\n                                        width: \"100%\",\n                                        setValue: (e)=>setValue(\"inicioContrato\", e, {\n                                                shouldValidate: true\n                                            }),\n                                        error: !!errors.inicioContrato,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_inicioContrato = errors.inicioContrato) === null || _errors_inicioContrato === void 0 ? void 0 : _errors_inicioContrato.message,\n                                        label: \"In\\xedcio do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1600,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1612,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"perfil\"),\n                                                onChange: (e)=>setValue(\"perfil\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"conservative\",\n                                                        children: \"Conservador\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1617,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"moderate\",\n                                                        children: \"Moderado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1618,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"aggressive\",\n                                                        children: \"Agressivo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1619,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1613,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1611,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1559,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4 w-full md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        register: register,\n                                        type: \"number\",\n                                        name: \"prazoInvestimento\",\n                                        width: \"100%\",\n                                        error: !!errors.prazoInvestimento,\n                                        errorMessage: errors === null || errors === void 0 ? void 0 : (_errors_prazoInvestimento = errors.prazoInvestimento) === null || _errors_prazoInvestimento === void 0 ? void 0 : _errors_prazoInvestimento.message,\n                                        label: \"Prazo do Investimento - em meses\",\n                                        placeholder: \"ex: 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1625,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Comprar com\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1636,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"comprarCom\"),\n                                                onChange: (e)=>setValue(\"comprarCom\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"pix\",\n                                                        children: \"PIX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1641,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"boleto\",\n                                                        children: \"Boleto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1642,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"bank_transfer\",\n                                                        children: \"Transfer\\xeancia Banc\\xe1ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1643,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1637,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1635,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Inputs_InputText__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        type: \"date\",\n                                        register: register,\n                                        name: \"fimContrato\",\n                                        value: endDate ? moment__WEBPACK_IMPORTED_MODULE_7___default()(endDate, \"DD/MM/YYYY\").format(\"YYYY-MM-DD\") : \"\",\n                                        width: \"100%\",\n                                        disabled: true,\n                                        label: \"Fim do Contrato\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1646,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-white mb-1 block\",\n                                                children: \"Deb\\xeanture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1656,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SelectCustom__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                value: watch(\"debenture\"),\n                                                onChange: (e)=>setValue(\"debenture\", e.target.value, {\n                                                        shouldValidate: true\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"s\",\n                                                        children: \"Sim\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1661,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"n\",\n                                                        children: \"N\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                        lineNumber: 1662,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                                lineNumber: 1657,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1655,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1624,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1557,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1556,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>setStep(1),\n                            className: \"border-[#FF9900] text-[#FF9900] px-8 py-2 rounded-lg hover:bg-[#FF9900] hover:text-white\",\n                            children: \"← Voltar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1669,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            size: \"lg\",\n                            type: \"submit\",\n                            className: \"px-8 py-2 rounded-lg \".concat(hasActiveContracts ? \"bg-[#FF9900] text-white hover:bg-[#e68a00]\" : \"bg-gray-500 text-gray-300 cursor-not-allowed\"),\n                            onClick: ()=>{\n                                console.log(\"Bot\\xe3o Concluir clicado\");\n                                console.log(\"Estado do formul\\xe1rio:\", {\n                                    isValid,\n                                    errors\n                                });\n                                console.log(\"Dados atuais:\", watch());\n                                console.log(\"Contratos ativos:\", hasActiveContracts);\n                            },\n                            disabled: !hasActiveContracts || isSubmitting || createContractMutation.isPending || isRedirecting || (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && (!irCalculado || !watch(\"irDeposito\") && !watch(\"irDesconto\")),\n                            children: !hasActiveContracts ? \"Nenhum contrato ativo encontrado\" : isRedirecting ? \"Redirecionando...\" : isSubmitting || createContractMutation.isPending ? \"Enviando...\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !irCalculado ? \"Calcule o IR primeiro\" : (watch(\"modalidade\") === \"MUTUO\" || (contractDetails === null || contractDetails === void 0 ? void 0 : contractDetails.contractType) === \"MUTUO\") && !watch(\"irDeposito\") && !watch(\"irDesconto\") ? \"Selecione uma op\\xe7\\xe3o de IR\" : \"Alterar Contrato\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1678,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1668,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n            lineNumber: 1209,\n            columnNumber: 5\n        }, this);\n    };\n    // Mostrar loading enquanto carrega os dados\n    if (isLoadingContract) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1715,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8 flex items-center justify-center min-h-[400px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF9900] mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1720,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white\",\n                                        children: \"Carregando dados do contrato...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1721,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1719,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                            lineNumber: 1718,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1717,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1716,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1732,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-6 w-full justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl text-center w-full font-bold text-white\",\n                                    children: [\n                                        \"Contratos\",\n                                        contractData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 block mt-1\",\n                                            children: (_contractData_contracts = contractData.contracts) === null || _contractData_contracts === void 0 ? void 0 : (_contractData_contracts_ = _contractData_contracts[0]) === null || _contractData_contracts_ === void 0 ? void 0 : _contractData_contracts_.investorName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                            lineNumber: 1740,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                    lineNumber: 1737,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1736,\n                                columnNumber: 13\n                            }, this),\n                            !tipo && hasActiveContracts ? upgradeContractOptions() : !tipo && !hasActiveContracts ? /* Se não há contratos ativos, mostrar mensagem */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900 border border-red-500 rounded-lg p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-white font-bold mb-2\",\n                                        children: \"⚠️ Nenhum Contrato Ativo Encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1752,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-sm\",\n                                        children: \"N\\xe3o \\xe9 poss\\xedvel realizar upgrade pois n\\xe3o h\\xe1 contratos ativos para este investidor. Apenas contratos com status ATIVO podem ser alterados.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                        lineNumber: 1753,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                                lineNumber: 1751,\n                                columnNumber: 15\n                            }, this) : /* Se há tipo definido, mostrar formulário normal */ step === 1 ? renderStep1() : renderStep2()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                        lineNumber: 1735,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                    lineNumber: 1734,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\projetos\\\\3\\\\ica-invest-contracts\\\\src\\\\app\\\\contratos\\\\alterar\\\\page.tsx\",\n                lineNumber: 1733,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AlterarContrato, \"S2p+YJ6E2UICSmXYuSm0HDLU9Bk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_18__.useQuery,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_19__.useForm,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = AlterarContrato;\nvar _c;\n$RefreshReg$(_c, \"AlterarContrato\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29udHJhdG9zL2FsdGVyYXIvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdEO0FBQ1Q7QUFDc0I7QUFDcEI7QUFDRTtBQVNwQjtBQUNLO0FBQzBCO0FBQ0Q7QUFPYTtBQUN4QjtBQUNZO0FBQzNCO0FBQzNCLHdEQUF3RDtBQUNVO0FBQ0o7QUFDWjtBQUNyQjtBQUM0QjtBQUNDO0FBRTFELGdFQUFnRTtBQUNoRSxNQUFNNEIsdUJBQXVCLENBQUNDO0lBQzVCLE1BQU1DLFVBQVVELE1BQU1FLE9BQU8sQ0FBQyxPQUFPO0lBRXJDLDhEQUE4RDtJQUM5RCxJQUFJRCxRQUFRRSxVQUFVLENBQUMsU0FBU0YsUUFBUUcsTUFBTSxLQUFLLElBQUk7UUFDckQsT0FBT0gsUUFDSkMsT0FBTyxDQUFDLGtDQUFrQztJQUMvQztJQUVBLDJFQUEyRTtJQUMzRSxJQUFJLENBQUNELFFBQVFFLFVBQVUsQ0FBQyxTQUFTRixRQUFRRyxNQUFNLEtBQUssSUFBSTtRQUN0RCxNQUFNQyxrQkFBa0IsT0FBT0o7UUFDL0IsT0FBT0ksZ0JBQ0pILE9BQU8sQ0FBQyxrQ0FBa0M7SUFDL0M7SUFFQSwwREFBMEQ7SUFDMUQsSUFBSUQsUUFBUUcsTUFBTSxJQUFJLElBQUk7UUFDeEIsSUFBSUgsUUFBUUUsVUFBVSxDQUFDLE9BQU87WUFDNUIsd0JBQXdCO1lBQ3hCLElBQUlGLFFBQVFHLE1BQU0sSUFBSSxHQUFHO2dCQUN2QixPQUFPSCxRQUFRQyxPQUFPLENBQUMsc0JBQXNCO1lBQy9DLE9BQU8sSUFBSUQsUUFBUUcsTUFBTSxJQUFJLEdBQUc7Z0JBQzlCLE9BQU9ILFFBQVFDLE9BQU8sQ0FBQyw2QkFBNkI7WUFDdEQsT0FBTztnQkFDTCxPQUFPRCxRQUFRQyxPQUFPLENBQUMsb0NBQW9DO1lBQzdEO1FBQ0YsT0FBTztZQUNMLDhDQUE4QztZQUM5QyxPQUFPdkIsdURBQVNBLENBQUNxQjtRQUNuQjtJQUNGO0lBRUEsT0FBT0E7QUFDVDtBQUVBLHNCQUFzQjtBQUN0QixNQUFNTSxtQkFBbUJmLHdDQUFVLEdBQUdpQixLQUFLLENBQUM7SUFDMUNDLGNBQWNsQix3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQ3BDQyxjQUFjckIsd0NBQVUsR0FBR29CLFFBQVEsQ0FBQztJQUNwQ0UsWUFBWXRCLHdDQUFVLEdBQUdvQixRQUFRLENBQUM7SUFDbENHLFNBQVN2Qix3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQy9CSSxLQUFLeEIsd0NBQVUsR0FBR29CLFFBQVEsQ0FBQztJQUMzQkssZ0JBQWdCekIsd0NBQVUsR0FBRzBCLFFBQVE7SUFDckNDLFNBQVMzQix3Q0FBVSxHQUFHMEIsUUFBUTtJQUM5QkUsT0FBTzVCLHdDQUFVLEdBQUc0QixLQUFLLENBQUMsc0JBQW1CUixRQUFRLENBQUM7SUFDdERTLEtBQUs3Qix3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQzNCVSxRQUFROUIsd0NBQVUsR0FBR29CLFFBQVEsQ0FBQztJQUM5QlcsVUFBVS9CLHdDQUFVLEdBQUdvQixRQUFRLENBQUM7SUFDaENZLFFBQVFoQyx3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQzlCYSxhQUFhakMsd0NBQVUsR0FBRzBCLFFBQVE7SUFDbENRLFFBQVFsQyx3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQzlCZSxPQUFPbkMsd0NBQVUsR0FBR29CLFFBQVEsQ0FBQztJQUM3QmdCLE9BQU9wQyx3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQzdCaUIsU0FBU3JDLHdDQUFVLEdBQUdvQixRQUFRLENBQUM7SUFDL0JrQixVQUFVdEMsd0NBQVUsR0FBR29CLFFBQVEsQ0FBQztJQUNoQ21CLFlBQVl2Qyx3Q0FBVSxHQUNuQm9CLFFBQVEsQ0FBQyw2QkFDVG9CLElBQUksQ0FBQywyQkFBMkIsMEVBQThELFNBQVMvQixLQUFLO1FBQzNHLHVFQUF1RTtRQUN2RSxPQUFPLE1BQU0sa0RBQWtEO0lBQ2pFO0lBQ0ZnQyxtQkFBbUJ6Qyx3Q0FBVSxHQUMxQm9CLFFBQVEsQ0FBQyx3Q0FDVG9CLElBQUksQ0FBQyxlQUFlLDJEQUFxRCxTQUFTL0IsS0FBSztRQUN0RixNQUFNOEIsYUFBYSxJQUFJLENBQUNHLE1BQU0sQ0FBQ0gsVUFBVTtRQUN6QyxJQUFJQSxlQUFlLFNBQVM5QixPQUFPO1lBQ2pDLG9GQUFvRjtZQUNwRixNQUFNa0MsZUFBZUMsT0FBT25DLE1BQU1vQyxVQUFVLENBQUMsS0FBSyxJQUFJQSxVQUFVLENBQUMsS0FBSyxTQUFTO1lBQy9FLE9BQU9GLGdCQUFnQjtRQUN6QjtRQUNBLE9BQU87SUFDVDtJQUNGRyxtQkFBbUI5Qyx3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQ3pDMkIsaUJBQWlCL0Msd0NBQVUsR0FBR29CLFFBQVEsQ0FBQztJQUN2QzRCLFlBQVloRCx3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQ2xDNkIsZ0JBQWdCakQsd0NBQVUsR0FBR29CLFFBQVEsQ0FBQztJQUN0QzhCLGFBQWFsRCx3Q0FBVTtJQUN2Qm1ELFFBQVFuRCx3Q0FBVSxHQUFHb0IsUUFBUSxDQUFDO0lBQzlCZ0MsV0FBV3BELHdDQUFVLEdBQUdvQixRQUFRLENBQUM7SUFDakNpQyxhQUFhckQsd0NBQVUsR0FBRzBCLFFBQVE7SUFDbEM0QixlQUFldEQsd0NBQVUsR0FBRzBCLFFBQVE7SUFDcEMseUNBQXlDO0lBQ3pDNkIsWUFBWXZELHlDQUFXLEdBQUcwQixRQUFRO0lBQ2xDK0IsWUFBWXpELHlDQUFXLEdBQUcwQixRQUFRO0FBQ3BDO0FBRWUsU0FBU2dDO1FBK2tESEMsMEJBQUFBOztJQTlrRG5CLE1BQU1DLFNBQVM5RSwwREFBU0E7SUFDeEIsTUFBTStFLGVBQWU5RSxnRUFBZUE7SUFDcEMsTUFBTStFLE9BQU9ELGFBQWFFLEdBQUcsQ0FBQyxTQUFTLGtDQUFrQztJQUN6RSxNQUFNQyxhQUFhSCxhQUFhRSxHQUFHLENBQUM7SUFDcEMsTUFBTUUsY0FBYzNELHVFQUFjQTtJQUVsQyxpQ0FBaUM7SUFDakMsTUFBTSxFQUFFNEQsTUFBTVAsWUFBWSxFQUFFUSxXQUFXQyxpQkFBaUIsRUFBRSxHQUFHakUsZ0VBQVFBLENBQUM7UUFDcEVrRSxVQUFVO1lBQUM7WUFBWUw7U0FBVztRQUNsQ00sU0FBUztZQUNQLElBQUksQ0FBQ04sWUFBWSxPQUFPO1lBQ3hCLE1BQU1PLFdBQVcsTUFBTWxFLGtEQUFHQSxDQUFDMEQsR0FBRyxDQUFDLGFBQXdCLE9BQVhDO1lBQzVDLE9BQU9PLFNBQVNMLElBQUk7UUFDdEI7UUFDQU0sU0FBUyxDQUFDLENBQUNSO0lBQ2I7SUFJQSx1REFBdUQ7SUFFdkQsTUFBTSxFQUNKUyxRQUFRLEVBQ1JDLFlBQVksRUFDWkMsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLE9BQU8sRUFDUEMsV0FBVyxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsWUFBWSxFQUFFLEVBQzdDLEdBQUduRix5REFBT0EsQ0FBQztRQUNWb0YsVUFBVW5GLHFFQUFXQSxDQUFDZ0I7UUFDdEJvRSxNQUFNO1FBQ05DLGVBQWU7WUFDYixVQUFVO1lBQ1ZsRSxjQUFjO1lBQ2RHLGNBQWM7WUFDZEMsWUFBWTtZQUNaQyxTQUFTO1lBQ1RDLEtBQUs7WUFDTEMsZ0JBQWdCO1lBQ2hCRSxTQUFTO1lBQ1RDLE9BQU87WUFDUEMsS0FBSztZQUNMQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsUUFBUTtZQUNSQyxhQUFhO1lBQ2JDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsVUFBVTtZQUNWZSxhQUFhO1lBQ2IsVUFBVTtZQUNWZCxZQUFZO1lBQ1pFLG1CQUFtQjtZQUNuQkssbUJBQW1CO1lBQ25CQyxpQkFBaUI7WUFDakJDLFlBQVk7WUFDWkMsZ0JBQWdCO1lBQ2hCQyxhQUFhO1lBQ2JDLFFBQVE7WUFDUkMsV0FBVztZQUNYRSxlQUFlO1lBQ2YsdUNBQXVDO1lBQ3ZDQyxZQUFZO1lBQ1pFLFlBQVk7UUFDZDtJQUNGO0lBSUEsTUFBTSxDQUFDNEIsTUFBTUMsUUFBUSxHQUFHM0YsZ0RBQVFBLENBQUMsSUFBSSwyQkFBMkI7SUFDaEUsTUFBTSxDQUFDNEYsWUFBWUMsY0FBYyxHQUFHN0YsZ0RBQVFBLENBQVM7SUFDckQsTUFBTSxDQUFDOEYsZUFBZUMsaUJBQWlCLEdBQUcvRixnREFBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUNnRyxtQkFBbUJDLHFCQUFxQixHQUFHakcsZ0RBQVFBLENBQVM7SUFDbkUsTUFBTSxDQUFDa0csdUJBQXVCQyx5QkFBeUIsR0FBR25HLGdEQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQ29HLGFBQWFDLGVBQWUsR0FBR3JHLGdEQUFRQSxDQUFDLFFBQVEsaUNBQWlDO0lBQ3hGLE1BQU0sQ0FBQ3NHLG9CQUFvQkMsc0JBQXNCLEdBQUd2RyxnREFBUUEsQ0FBQyxRQUFRLDhDQUE4QztJQUNuSCxNQUFNLENBQUN3RywyQkFBMkJDLDZCQUE2QixHQUFHekcsZ0RBQVFBLENBQVMsS0FBSyw0Q0FBNEM7SUFDcEksTUFBTSxDQUFDMEcsb0JBQW9CQyxzQkFBc0IsR0FBRzNHLGdEQUFRQSxDQUFTLEtBQUssb0NBQW9DO0lBRTlHLGdDQUFnQztJQUNoQyxNQUFNNEcsbUJBQW1CMUcsbURBQVdBLENBQUMsQ0FBQzJHLE9BQWVqRTtRQUNuRCxJQUFJQSxlQUFlLE9BQU87WUFDeEIrRCxzQkFBc0I7WUFDdEIsT0FBTztRQUNUO1FBRUEsaUZBQWlGO1FBQ2pGLElBQUksQ0FBQ0UsU0FBU0EsTUFBTUMsSUFBSSxPQUFPLElBQUk7WUFDakNILHNCQUFzQjtZQUN0QixPQUFPO1FBQ1Q7UUFFQSxNQUFNSSxnQkFBZ0I5RCxPQUFPNEQsTUFBTTNELFVBQVUsQ0FBQyxLQUFLLElBQUlBLFVBQVUsQ0FBQyxLQUFLLFNBQVM7UUFFaEYsMkRBQTJEO1FBQzNEOEQsUUFBUUMsR0FBRyxDQUFDLDZEQUE2Q2Y7UUFDekRjLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBNkJKO1FBQ3pDRyxRQUFRQyxHQUFHLENBQUMscURBQXFDRjtRQUVqRCxJQUFJYix1QkFBdUI7WUFDekJjLFFBQVFDLEdBQUcsQ0FBQztZQUNaTixzQkFBc0I7WUFDdEIsT0FBTztRQUNUO1FBRUEseURBQXlEO1FBQ3pELElBQUlJLGdCQUFnQixPQUFPO1lBQ3pCQyxRQUFRQyxHQUFHLENBQUM7WUFDWk4sc0JBQXNCO1lBQ3RCLE9BQU87UUFDVDtRQUVBLElBQUlJLGdCQUFnQixTQUFTLEdBQUc7WUFDOUJDLFFBQVFDLEdBQUcsQ0FBQztZQUNaTixzQkFBc0I7WUFDdEIsT0FBTztRQUNUO1FBRUFLLFFBQVFDLEdBQUcsQ0FBQztRQUNaTixzQkFBc0I7UUFDdEIsT0FBTztJQUNULEdBQUc7UUFBQ1Q7S0FBc0I7SUFFMUIsMkNBQTJDO0lBQzNDLE1BQU1nQiwwQkFBMEJuSCwrQ0FBT0EsQ0FBQztZQUNoQmlFO1FBQXRCLElBQUksQ0FBQ0EsZ0JBQWdCLEdBQUNBLDBCQUFBQSxhQUFhbUQsU0FBUyxjQUF0Qm5ELDhDQUFBQSx1QkFBd0IsQ0FBQyxFQUFFLEdBQUUsT0FBTztRQUMxRCxNQUFNb0QsV0FBV3BELGFBQWFtRCxTQUFTLENBQUMsRUFBRTtRQUMxQyxPQUFPQyxTQUFTQyxJQUFJLEtBQUssUUFBUSxVQUFVO0lBQzdDLEdBQUc7UUFBQ3JEO0tBQWE7SUFFakIsb0RBQW9EO0lBQ3BEakUsK0NBQU9BLENBQUM7UUFDTixJQUFJaUUsZ0JBQWdCLENBQUNTLG1CQUFtQjtnQkFDckJUO1lBQWpCLE1BQU1vRCxZQUFXcEQsMEJBQUFBLGFBQWFtRCxTQUFTLGNBQXRCbkQsOENBQUFBLHVCQUF3QixDQUFDLEVBQUUsRUFBRSwyQkFBMkI7WUFFekUsSUFBSW9ELFVBQVU7b0JBK0NSQSx5QkFBcURBLDBCQUU5Q0EsMEJBRUFBO2dCQWxEWCxpQkFBaUI7Z0JBQ2pCbkMsU0FBUyxnQkFBZ0JtQyxTQUFTRSxZQUFZLElBQUk7Z0JBRWxELDBDQUEwQztnQkFDMUMsTUFBTUMsWUFBWXZELGFBQWF3RCxRQUFRLElBQUk7Z0JBQzNDLElBQUlELFdBQVc7b0JBQ2IsTUFBTUUsc0JBQXNCRixVQUFVckcsTUFBTSxJQUFJLEtBQUsxQixxREFBT0EsQ0FBQytILGFBQWE1SCxzREFBUUEsQ0FBQzRIO29CQUNuRnRDLFNBQVMsT0FBT3dDO2dCQUNsQixPQUFPO29CQUNMeEMsU0FBUyxPQUFPO2dCQUNsQjtnQkFFQUEsU0FBUyxjQUFjakIsYUFBYTBELEVBQUUsSUFBSTtnQkFDMUN6QyxTQUFTLFNBQVNqQixhQUFhL0IsS0FBSyxJQUFJO2dCQUV4Qyw4RUFBOEU7Z0JBQzlFLE1BQU0wRixXQUFXM0QsYUFBYTRELEtBQUssSUFBSTtnQkFDdkMzQyxTQUFTLFdBQVcwQyxXQUFXOUcscUJBQXFCOEcsWUFBWTtnQkFFaEUseUVBQXlFO2dCQUN6RTFDLFNBQVMsV0FBV2pCLGFBQWE2RCxVQUFVLElBQUk7Z0JBQy9DNUMsU0FBUyxrQkFBa0JqQixhQUFhOEQsU0FBUyxJQUFJO2dCQUVyRCxXQUFXO2dCQUNYLE1BQU01RixNQUFNOEIsYUFBYStELE9BQU8sSUFBSTtnQkFDcEM5QyxTQUFTLE9BQU8vQyxNQUFNM0MscURBQU9BLENBQUMyQyxPQUFPO2dCQUNyQytDLFNBQVMsVUFBVWpCLGFBQWFnRSxJQUFJLElBQUk7Z0JBQ3hDL0MsU0FBUyxZQUFZakIsYUFBYWlFLE9BQU8sSUFBSTtnQkFDN0NoRCxTQUFTLFVBQVVqQixhQUFha0UsYUFBYSxJQUFJO2dCQUNqRGpELFNBQVMsZUFBZWpCLGFBQWFtRSxVQUFVLElBQUk7Z0JBQ25EbEQsU0FBUyxVQUFVakIsYUFBYW9FLEtBQUssSUFBSTtnQkFFekMsa0JBQWtCO2dCQUNsQm5ELFNBQVMsU0FBU2pCLGFBQWFxRSxJQUFJLElBQUk7Z0JBQ3ZDcEQsU0FBUyxXQUFXakIsYUFBYXNFLE1BQU0sSUFBSTtnQkFDM0NyRCxTQUFTLFNBQVNqQixhQUFhdUUsYUFBYSxJQUFJO2dCQUNoRHRELFNBQVMsWUFBWWpCLGFBQWE0RCxLQUFLLElBQUk1RCxhQUFhL0IsS0FBSyxJQUFJLEtBQUsseUNBQXlDO2dCQUUvRyw0Q0FBNEM7Z0JBQzVDLE1BQU1hLG9CQUFvQnNFLFNBQVNvQixlQUFlLElBQUk7Z0JBQ3RELDJEQUEyRDtnQkFDM0R2RCxTQUFTLHFCQUFxQm5DLG9CQUFvQnBELHVEQUFTQSxDQUFDb0QscUJBQXFCO2dCQUNqRm1DLFNBQVMsbUJBQW1CbUMsU0FBU3FCLGVBQWUsSUFBSTtnQkFDeER4RCxTQUFTLHFCQUFxQm1DLFNBQVNzQixjQUFjLElBQUk7Z0JBQ3pELDRCQUE0QjtnQkFDNUIsSUFBSUMsZ0JBQWdCLE9BQU8sU0FBUztnQkFDcEMsSUFBSXZCLEVBQUFBLDBCQUFBQSxTQUFTd0IsYUFBYSxjQUF0QnhCLDhDQUFBQSx3QkFBd0J5QixRQUFRLENBQUMsMEJBQW9CekIsMkJBQUFBLFNBQVN3QixhQUFhLGNBQXRCeEIsK0NBQUFBLHlCQUF3QnlCLFFBQVEsQ0FBQyxpQkFBYTtvQkFDckdGLGdCQUFnQjtnQkFDbEIsT0FBTyxLQUFJdkIsMkJBQUFBLFNBQVN3QixhQUFhLGNBQXRCeEIsK0NBQUFBLHlCQUF3QnlCLFFBQVEsQ0FBQyxRQUFRO29CQUNsREYsZ0JBQWdCO2dCQUNsQixPQUFPLEtBQUl2QiwyQkFBQUEsU0FBU3dCLGFBQWEsY0FBdEJ4QiwrQ0FBQUEseUJBQXdCeUIsUUFBUSxDQUFDLFdBQVc7b0JBQ3JERixnQkFBZ0I7Z0JBQ2xCO2dCQUNBMUQsU0FBUyxjQUFjMEQ7Z0JBQ3ZCMUQsU0FBUyxrQkFBa0JtQyxTQUFTMEIsYUFBYSxHQUFHbEosNkNBQU1BLENBQUN3SCxTQUFTMEIsYUFBYSxFQUFFQyxNQUFNLENBQUMsZ0JBQWdCO2dCQUUxRyw4QkFBOEI7Z0JBQzlCOUQsU0FBUyxjQUFjbUMsU0FBU0MsSUFBSSxLQUFLLFFBQVEsVUFBVTtnQkFFM0RMLFFBQVFDLEdBQUcsQ0FBQywwREFBb0RqRDtZQUNsRTtRQUNGO0lBQ0YsR0FBRztRQUFDQTtRQUFjUztRQUFtQlE7S0FBUztJQUU5Qyx5RkFBeUY7SUFDekYsTUFBTXJDLGFBQWFvQyxNQUFNO0lBQ3pCLE1BQU1sQyxvQkFBb0JrQyxNQUFNO0lBRWhDL0UsaURBQVNBLENBQUM7UUFDUixJQUFJMkMsZUFBZSxTQUFTRSxxQkFBcUJBLGtCQUFrQmdFLElBQUksT0FBTyxJQUFJO1lBQ2hGLG9GQUFvRjtZQUNwRixNQUFNQyxnQkFBZ0JuRyxxRUFBaUJBLENBQUNrQyxzQkFBc0I7WUFDOUQsSUFBSWlFLGdCQUFnQixHQUFHO2dCQUNyQixNQUFNaUMsUUFBUUMsS0FBS0MsS0FBSyxDQUFDbkMsZ0JBQWdCO2dCQUN6QzlCLFNBQVMsaUJBQWlCK0QsTUFBTUcsUUFBUTtZQUMxQyxPQUFPO2dCQUNMbEUsU0FBUyxpQkFBaUI7WUFDNUI7UUFDRixPQUFPLElBQUlyQyxlQUFlLE9BQU87WUFDL0Isb0RBQW9EO1lBQ3BEcUMsU0FBUyxpQkFBaUI7UUFDNUI7SUFDRixHQUFHO1FBQUNyQztRQUFZRTtRQUFtQm1DO0tBQVM7SUFFNUMsb0VBQW9FO0lBQ3BFLE1BQU1tRSxxQkFBcUJySiwrQ0FBT0EsQ0FBQztZQUNYaUUseUJBR0NvRDtRQUh2QixJQUFJLENBQUNwRCxnQkFBZ0IsR0FBQ0EsMEJBQUFBLGFBQWFtRCxTQUFTLGNBQXRCbkQsOENBQUFBLHVCQUF3QixDQUFDLEVBQUUsR0FBRSxPQUFPO1FBRTFELE1BQU1vRCxXQUFXcEQsYUFBYW1ELFNBQVMsQ0FBQyxFQUFFO1FBQzFDLE1BQU1rQyxrQkFBaUJqQywyQkFBQUEsU0FBU2lDLGNBQWMsY0FBdkJqQywrQ0FBQUEseUJBQXlCa0MsV0FBVztRQUUzRCwrQ0FBK0M7UUFDL0MsTUFBTUMscUJBQXFCRixtQkFBbUIsWUFBWUEsbUJBQW1CO1FBRTdFLDhDQUE4QztRQUM5QyxJQUFJRyxvQkFBb0I7UUFDeEIsSUFBSXBDLFNBQVNxQyxRQUFRLElBQUlyQyxTQUFTcUMsUUFBUSxDQUFDdkksTUFBTSxHQUFHLEdBQUc7WUFDckRzSSxvQkFBb0JwQyxTQUFTcUMsUUFBUSxDQUFDQyxJQUFJLENBQUMsQ0FBQ0Q7b0JBQ25CQTtnQkFBdkIsTUFBTUUsa0JBQWlCRiwyQkFBQUEsU0FBU0osY0FBYyxjQUF2QkksK0NBQUFBLHlCQUF5QkgsV0FBVztnQkFDM0QsT0FBT0ssbUJBQW1CLFlBQVlBLG1CQUFtQjtZQUMzRDtRQUNGO1FBRUEsT0FBT0osc0JBQXNCQztJQUMvQixHQUFHO1FBQUN4RjtLQUFhO0lBRWpCLCtDQUErQztJQUMvQyxNQUFNNEYseUJBQXlCO1FBQzdCM0YsT0FBTzRGLElBQUksQ0FBQyxvREFBK0QsT0FBWHhGO0lBQ2xFO0lBRUEsTUFBTXlGLHNCQUFzQjtRQUMxQjdGLE9BQU80RixJQUFJLENBQUMsaURBQTRELE9BQVh4RjtJQUMvRDtJQUVBLHVDQUF1QztJQUN2QyxNQUFNMEYseUJBQXlCO1FBQzdCLHFCQUNFLDhEQUFDQztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUdELFdBQVU7OEJBQW9DOzs7Ozs7OEJBR2xELDhEQUFDRDtvQkFDQ0MsV0FBVTtvQkFDVkUsU0FBU1A7OEJBRVQsNEVBQUNJO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0c7NENBQUVILFdBQVU7c0RBQXlGOzs7Ozs7c0RBR3RHLDhEQUFDRzs0Q0FBRUgsV0FBVTtzREFBbUU7Ozs7Ozs7Ozs7Ozs4Q0FJbEYsOERBQUNEO29DQUFJQyxXQUFVOzs7Ozs7OENBQ2YsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0c7NENBQUVILFdBQVU7c0RBQStCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFVdEQsOERBQUNEO29CQUNDQyxXQUFVO29CQUNWRSxTQUFTTDs4QkFFVCw0RUFBQ0U7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRzs0Q0FBRUgsV0FBVTtzREFBeUY7Ozs7OztzREFHdEcsOERBQUNHOzRDQUFFSCxXQUFVO3NEQUFtRTs7Ozs7Ozs7Ozs7OzhDQUlsRiw4REFBQ0Q7b0NBQUlDLFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRzs0Q0FBRUgsV0FBVTtzREFBK0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBVTVEO0lBRUEsU0FBU0k7WUFBbUJDLFlBQUFBLGlFQUFZO1FBQ3RDLDZEQUE2RDtRQUM3RCxNQUFNQyxhQUFhdEgsT0FBTytCLE1BQU07UUFDaEMsSUFBSSxDQUFDdUYsY0FBY0MsTUFBTUQsYUFBYTtZQUNwQzFFLGNBQWM7WUFDZFEsZUFBZTtZQUNmLElBQUlpRSxXQUFXO2dCQUNicEwsaURBQUtBLENBQUN1TCxJQUFJLENBQUM7WUFDYjtZQUNBO1FBQ0Y7UUFDQSxNQUFNQyxZQUFZSCxhQUFhO1FBQy9CLElBQUlJLFdBQVc7UUFDZixJQUFJRCxhQUFhLEtBQUs7WUFDcEJDLFdBQVc7UUFDYixPQUFPLElBQUlELGFBQWEsS0FBSztZQUMzQkMsV0FBVztRQUNiLE9BQU8sSUFBSUQsYUFBYSxLQUFLO1lBQzNCQyxXQUFXO1FBQ2IsT0FBTztZQUNMQSxXQUFXO1FBQ2I7UUFDQTlFLGNBQWM4RTtRQUNkdEUsZUFBZTtRQUNmLElBQUlpRSxXQUFXO1lBQ2JwTCxpREFBS0EsQ0FBQzBMLE9BQU8sQ0FBQztRQUNoQjtJQUNGO0lBRUEsTUFBTUMsV0FBVyxPQUFPdEc7UUFDdEJ5QyxRQUFRQyxHQUFHLENBQUMsOENBQXdDMUM7UUFFcEQsMEVBQTBFO1FBQzFFLElBQUlBLEtBQUszQixVQUFVLEtBQUssV0FBV2tJLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCQyxZQUFZLE1BQUssU0FBUztZQUM1RSxJQUFJLENBQUMzRSxhQUFhO2dCQUNoQmxILGlEQUFLQSxDQUFDOEwsS0FBSyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxJQUFJLENBQUN6RyxLQUFLWCxVQUFVLElBQUksQ0FBQ1csS0FBS1QsVUFBVSxFQUFFO2dCQUN4QzVFLGlEQUFLQSxDQUFDOEwsS0FBSyxDQUFDO2dCQUNaO1lBQ0Y7UUFDRjtRQUVBLDBEQUEwRDtRQUMxRCxJQUFJLENBQUNwRSxpQkFBaUJyQyxLQUFLekIsaUJBQWlCLEVBQUV5QixLQUFLM0IsVUFBVSxHQUFHO1lBQzlEMUQsaURBQUtBLENBQUM4TCxLQUFLLENBQUN0RTtZQUNaO1FBQ0Y7UUFFQSxtREFBbUQ7UUFDbkQsSUFBSVEsNEJBQTRCLFNBQVMzQyxLQUFLM0IsVUFBVSxLQUFLLFNBQVM7WUFDcEUxRCxpREFBS0EsQ0FBQzhMLEtBQUssQ0FBQztZQUNaO1FBQ0Y7UUFFQWhFLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0I1QztRQUU1QixJQUFJO1lBQ0YsTUFBTTRHLE9BQU8xRyxLQUFLaEQsWUFBWSxLQUFLO1lBQ25DLGdDQUFnQztZQUNoQyxNQUFNZ0csWUFBWTBELE9BQ2QxRyxLQUFLMUMsR0FBRyxDQUFDYixPQUFPLENBQUMsT0FBTyxJQUFJLE9BQU87ZUFDbkN1RCxLQUFLMUMsR0FBRyxDQUFDYixPQUFPLENBQUMsT0FBTyxLQUFLLE1BQU07WUFFdkNnRyxRQUFRQyxHQUFHLENBQUMsc0JBQXNCO2dCQUFFZ0U7Z0JBQU0xRDtnQkFBV2pEO1lBQVk7WUFFakUsMERBQTBEO1lBQzFELE1BQU00RyxjQUFtQjtnQkFDdkJDLE1BQU0sQ0FBQztvQkFDTCwrQkFBK0I7b0JBQy9CLElBQUk3RyxZQUFZOEcsSUFBSSxLQUFLLGdCQUFnQjlHLFlBQVk4RyxJQUFJLEtBQUssU0FBUzt3QkFDckUsT0FBTztvQkFDVDtvQkFDQSxJQUFJOUcsWUFBWThHLElBQUksS0FBSyxXQUFXO3dCQUNsQyxPQUFPO29CQUNUO29CQUNBLElBQUk5RyxZQUFZOEcsSUFBSSxLQUFLLFVBQVU7d0JBQ2pDLE9BQU87b0JBQ1Q7b0JBQ0EsdUJBQXVCO29CQUN2QixPQUFPO2dCQUNUO2dCQUNBQyxZQUFZSixPQUFPLE9BQU87Z0JBQzFCRixjQUFjeEcsS0FBSzNCLFVBQVU7Z0JBQzdCMEksVUFBVSxDQUFDO29CQUNULDZEQUE2RDtvQkFDN0QsSUFBSWhILFlBQVk4RyxJQUFJLEtBQUssV0FBVzt3QkFDbEMsT0FBTzs0QkFBQztnQ0FDTkcsV0FBV2pILFlBQVlrSCxNQUFNO2dDQUM3QkMsTUFBTSxJQUFJLDhCQUE4Qjs0QkFDMUM7eUJBQUU7b0JBQ0o7b0JBQ0EsMkVBQTJFO29CQUMzRSxPQUFPLEVBQUU7Z0JBQ1g7Z0JBQ0FDLFlBQVk7b0JBQ1ZDLFFBQVFDLFNBQVNySCxLQUFLekIsaUJBQWlCLENBQUM5QixPQUFPLENBQUMsT0FBTyxRQUFRO29CQUMvRDZLLGFBQWFDLFdBQVd2SCxLQUFLbkIsZUFBZSxLQUFLO29CQUNqRDJJLGtCQUFrQjtvQkFDbEJwRCxlQUFlO29CQUNmcUQsU0FBUyxDQUFDO3dCQUNSLHdEQUF3RDt3QkFDeEQsTUFBTUMsTUFBTSxJQUFJQzt3QkFDaEIsTUFBTUYsVUFBVSxJQUFJRSxLQUFLRCxJQUFJRSxXQUFXLEtBQUssR0FBR0YsSUFBSUcsUUFBUSxJQUFJSCxJQUFJSSxPQUFPO3dCQUMzRSxPQUFPTCxRQUFRTSxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLHFCQUFxQjtvQkFDbkU7b0JBQ0FDLFNBQVM7b0JBQ1Q3SSxlQUFlWSxLQUFLM0IsVUFBVSxLQUFLLFFBQVFxRyxLQUFLd0QsR0FBRyxDQUFDLEdBQUdiLFNBQVNySCxLQUFLWixhQUFhLElBQUksUUFBUSxLQUFLK0k7b0JBQ25HQyxhQUFhO2dCQUNmO2dCQUNBQyxhQUFhO29CQUNYdkUsTUFBTTlELEtBQUsvQixLQUFLLElBQUk7b0JBQ3BCcUssUUFBUXRJLEtBQUs3QixPQUFPLElBQUk7b0JBQ3hCb0ssU0FBU3ZJLEtBQUs5QixLQUFLLElBQUk7b0JBQ3ZCc0ssS0FBS3hJLEtBQUs1QixRQUFRLElBQUk7Z0JBQ3hCO2dCQUNBcUssWUFBWS9CLE9BQU95QixZQUFZO29CQUM3Qk8sVUFBVTFJLEtBQUs3QyxZQUFZLElBQUk7b0JBQy9CRyxLQUFLMEY7b0JBQ0xHLElBQUksQ0FBQzt3QkFDSCxNQUFNQSxLQUFLbkQsS0FBSzVDLFVBQVU7d0JBQzFCLElBQUksQ0FBQytGLElBQUksT0FBTzt3QkFDaEIsTUFBTXdGLFVBQVV4RixHQUFHeUIsUUFBUSxHQUFHbkksT0FBTyxDQUFDLE9BQU87d0JBQzdDLE9BQU9rTSxXQUFXO29CQUNwQjtvQkFDQUMsZUFBZTtvQkFDZkMsYUFBYTtvQkFDYkMsWUFBWTtvQkFDWnZGLFdBQVcsQ0FBQzt3QkFDVixNQUFNQSxZQUFZdkQsS0FBS3pDLGNBQWM7d0JBQ3JDLElBQUksQ0FBQ2dHLFdBQVcsT0FBTyxJQUFJb0UsT0FBT0ksV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7d0JBQzdELElBQUl6RSxVQUFVd0YsS0FBSyxDQUFDLHdCQUF3Qjs0QkFDMUMsT0FBT3hGO3dCQUNUO3dCQUNBLE1BQU15RixPQUFPLElBQUlyQixLQUFLcEU7d0JBQ3RCLE9BQU95RixLQUFLakIsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ3pDO29CQUNBdEssT0FBT3NDLEtBQUt0QyxLQUFLLElBQUk7b0JBQ3JCMkYsT0FBTyxDQUFDO3dCQUNOLE1BQU00RixhQUFhakosS0FBSzNDLE9BQU8sQ0FBQ1osT0FBTyxDQUFDLE9BQU87d0JBQy9DLElBQUl3TSxXQUFXdE0sTUFBTSxLQUFLLE1BQU0sQ0FBQ3NNLFdBQVd2TSxVQUFVLENBQUMsT0FBTzs0QkFDNUQsT0FBTyxPQUFPdU07d0JBQ2hCO3dCQUNBLE9BQU9BO29CQUNUO29CQUNBM0YsWUFBWXRELEtBQUt2QyxPQUFPLElBQUk7b0JBQzVCaUcsU0FBUzt3QkFDUHdGLFFBQVFsSixLQUFLbUosR0FBRyxJQUFJO3dCQUNwQkMsY0FBY3BKLEtBQUtxSixNQUFNLElBQUk7d0JBQzdCNUYsTUFBTXpELEtBQUtwQyxNQUFNLElBQUk7d0JBQ3JCaUcsT0FBTzdELEtBQUtoQyxNQUFNLElBQUk7d0JBQ3RCc0wsWUFBWXRKLEtBQUtyQyxHQUFHLEdBQUdxQyxLQUFLckMsR0FBRyxDQUFDbEIsT0FBTyxDQUFDLE9BQU8sTUFBTTt3QkFDckQ4TSxRQUFRdkosS0FBS2xDLE1BQU0sSUFBSTt3QkFDdkI4RixZQUFZNUQsS0FBS2pDLFdBQVcsSUFBSTtvQkFDbEM7Z0JBQ0Y7Z0JBQ0F5TCxTQUFTOUMsT0FBTztvQkFDZCtDLGFBQWF6SixLQUFLN0MsWUFBWSxJQUFJO29CQUNsQ3VNLE1BQU0xRztvQkFDTjJHLFdBQVc7b0JBQ1hqTSxPQUFPc0MsS0FBS3RDLEtBQUssSUFBSTtvQkFDckIyRixPQUFPLENBQUM7d0JBQ04sTUFBTTRGLGFBQWFqSixLQUFLM0MsT0FBTyxDQUFDWixPQUFPLENBQUMsT0FBTzt3QkFDL0MsSUFBSXdNLFdBQVd0TSxNQUFNLEtBQUssTUFBTSxDQUFDc00sV0FBV3ZNLFVBQVUsQ0FBQyxPQUFPOzRCQUM1RCxPQUFPLE9BQU91TTt3QkFDaEI7d0JBQ0EsT0FBT0E7b0JBQ1Q7b0JBQ0F2RixTQUFTO3dCQUNQd0YsUUFBUWxKLEtBQUttSixHQUFHLElBQUk7d0JBQ3BCQyxjQUFjcEosS0FBS3FKLE1BQU0sSUFBSTt3QkFDN0I1RixNQUFNekQsS0FBS3BDLE1BQU0sSUFBSTt3QkFDckJpRyxPQUFPN0QsS0FBS2hDLE1BQU0sSUFBSTt3QkFDdEJzTCxZQUFZdEosS0FBS3JDLEdBQUcsR0FBR3FDLEtBQUtyQyxHQUFHLENBQUNsQixPQUFPLENBQUMsT0FBTyxNQUFNO3dCQUNyRDhNLFFBQVF2SixLQUFLbEMsTUFBTSxJQUFJO3dCQUN2QjhGLFlBQVk1RCxLQUFLakMsV0FBVyxJQUFJO29CQUNsQztvQkFDQTZMLGdCQUFnQjt3QkFDZGxCLFVBQVUxSSxLQUFLN0MsWUFBWSxJQUFJO3dCQUMvQkcsS0FBSzt3QkFDTDZGLElBQUk7d0JBQ0p5RixlQUFlO3dCQUNmQyxhQUFhO3dCQUNiQyxZQUFZO3dCQUNadkYsV0FBVzt3QkFDWDdGLE9BQU9zQyxLQUFLdEMsS0FBSyxJQUFJO3dCQUNyQjJGLE9BQU8sQ0FBQzs0QkFDTixNQUFNNEYsYUFBYWpKLEtBQUszQyxPQUFPLENBQUNaLE9BQU8sQ0FBQyxPQUFPOzRCQUMvQyxJQUFJd00sV0FBV3RNLE1BQU0sS0FBSyxNQUFNLENBQUNzTSxXQUFXdk0sVUFBVSxDQUFDLE9BQU87Z0NBQzVELE9BQU8sT0FBT3VNOzRCQUNoQjs0QkFDQSxPQUFPQTt3QkFDVDt3QkFDQTNGLFlBQVl0RCxLQUFLdkMsT0FBTyxJQUFJO29CQUM5QjtnQkFDRixJQUFJMEs7WUFDTjtZQUVBMUYsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0IzQztZQUM1QjBDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUJpRSxZQUFZQyxJQUFJO1lBQzdDbkUsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QmlFLFlBQVlHLFVBQVU7WUFDekRyRSxRQUFRQyxHQUFHLENBQUMseUJBQXlCaUUsWUFBWUgsWUFBWTtZQUM3RC9ELFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJpRSxZQUFZSSxRQUFRO1lBQ3JEdEUsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQmlFLFlBQVlRLFVBQVUsQ0FBQy9ILGFBQWE7WUFDMUVxRCxRQUFRQyxHQUFHLENBQUMsOEJBQThCbUgsS0FBS0MsU0FBUyxDQUFDbkQsYUFBYSxNQUFNO1lBRTVFb0QsdUJBQXVCQyxNQUFNLENBQUNyRDtRQUNoQyxFQUFFLE9BQU9GLE9BQU87WUFDZGhFLFFBQVFnRSxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQzlMLGlEQUFLQSxDQUFDOEwsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLGtDQUFrQztJQUNsQyw2QkFBNkI7SUFDN0IsTUFBTXdELGNBOEJBO1FBQ0Y7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBLGlEQUFpRDtRQUNqRCwwQ0FBMEM7UUFDMUM7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBLDZCQUE2QjtRQUM3QjtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBRUQ7SUFFSCxNQUFNQyxhQUFhO1FBQ2pCekgsUUFBUUMsR0FBRyxDQUFDLG9DQUFpQ3VIO1FBQzdDLE1BQU1FLFFBQVEsTUFBTXhKLFFBQVFzSjtRQUM1QnhILFFBQVFDLEdBQUcsQ0FBQyxpQ0FBMkJ5SDtRQUN2QzFILFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUI3QjtRQUU3QixJQUFJc0osT0FBTztZQUNUL0ksUUFBUTtZQUNSekcsaURBQUtBLENBQUMwTCxPQUFPLENBQUM7UUFDaEIsT0FBTztZQUNMLG9EQUFvRDtZQUNwRCxNQUFNK0QsZ0JBQWdCSCxZQUFZSSxNQUFNLENBQUNDLENBQUFBLFFBQVN6SixNQUFNLENBQUN5SixNQUFNO1lBQy9ELE1BQU1DLGtCQUFrQkgsY0FBY3pOLE1BQU07WUFFNUMsSUFBSTROLGtCQUFrQixHQUFHO2dCQUN2QjVQLGlEQUFLQSxDQUFDOEwsS0FBSyxDQUFDLHVCQUF1QyxPQUFoQjhELGlCQUFnQjtZQUNyRCxPQUFPO2dCQUNMNVAsaURBQUtBLENBQUM4TCxLQUFLLENBQUM7WUFDZDtRQUNGO0lBQ0Y7SUFHQSxNQUFNc0QseUJBQXlCL04sbUVBQVdBLENBQUM7UUFDekN3TyxZQUFZLE9BQU94SztZQUNqQnlDLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsbUJBQW1CMUM7WUFFL0IsTUFBTUssV0FBVyxNQUFNbEUsa0RBQUdBLENBQUNzTyxJQUFJLENBQUUsb0JBQW1Ceks7WUFDcER5QyxRQUFRQyxHQUFHLENBQUMsb0JBQW9CckMsU0FBU0wsSUFBSTtZQUM3QyxPQUFPSyxTQUFTTCxJQUFJO1FBQ3RCO1FBQ0EwSyxXQUFXLENBQUNySztZQUNWb0MsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ3JDO1lBRTVDLDREQUE0RDtZQUM1RCxNQUFNc0ssZ0JBQWdCdEsscUJBQUFBLCtCQUFBQSxTQUFVdUssRUFBRTtZQUNsQyxJQUFJRCxlQUFlO2dCQUNqQmhRLGlEQUFLQSxDQUFDMEwsT0FBTyxDQUFDLG9DQUFrRSxPQUE5QnNFLGNBQWNFLFNBQVMsQ0FBQyxHQUFHLElBQUc7WUFDbEYsT0FBTztnQkFDTGxRLGlEQUFLQSxDQUFDMEwsT0FBTyxDQUFDO1lBQ2hCO1lBRUEsd0VBQXdFO1lBQ3hFN0UsaUJBQWlCO1lBQ2pCc0osV0FBVztnQkFDVHBMLE9BQU80RixJQUFJLENBQUM7WUFDZCxHQUFHLE9BQU8sOENBQThDO1FBQzFEO1FBQ0F5RixTQUFTLENBQUN0RTtZQUNSdkssbUVBQVdBLENBQUN1SyxPQUFPO1FBQ3JCO0lBQ0Y7SUFFQSxNQUFNdUUsT0FBT3ZLLE1BQU07SUFDbkIsTUFBTXdLLFdBQVd4SyxNQUFNO0lBRXZCLE1BQU1nSCxVQUFVak0sK0NBQU9BLENBQUM7UUFDdEIsSUFBSXlQLFlBQVlELE1BQU07WUFDcEIsTUFBTUUsVUFBVW5QLGdGQUFxQkEsQ0FBQztnQkFDcENvUCxZQUFZQyxPQUFPSjtnQkFDbkJLLFdBQVdKO1lBQ2I7WUFDQSxNQUFNSyxtQkFBbUJqUSw2Q0FBTUEsQ0FBQzZQLFNBQVMsY0FBYzFHLE1BQU0sQ0FBQztZQUM5RCwrQ0FBK0M7WUFDL0M5RCxTQUFTLGVBQWVyRiw2Q0FBTUEsQ0FBQzZQLFNBQVMsY0FBYzFHLE1BQU0sQ0FBQyxlQUFlO2dCQUFFK0csZ0JBQWdCO1lBQUs7WUFDbkcsT0FBT0Q7UUFDVDtRQUNBLE9BQU87SUFDVCxHQUFHO1FBQUNMO1FBQVVEO1FBQU10SztLQUFTO0lBRTdCLGlFQUFpRTtJQUNqRSxNQUFNNkYsa0JBQWtCL0ssK0NBQU9BLENBQUM7WUFDUmlFLHlCQW1CQ29EO1FBbkJ2QixJQUFJLENBQUNwRCxnQkFBZ0IsR0FBQ0EsMEJBQUFBLGFBQWFtRCxTQUFTLGNBQXRCbkQsOENBQUFBLHVCQUF3QixDQUFDLEVBQUUsR0FBRSxPQUFPO1FBRTFELE1BQU1vRCxXQUFXcEQsYUFBYW1ELFNBQVMsQ0FBQyxFQUFFO1FBQzFDLE1BQU00SSxVQUFVLEVBQUU7UUFFbEIsMkNBQTJDO1FBQzNDLE1BQU1DLGNBQWMsQ0FBQ3JFLFFBQWdCRixNQUFjd0U7WUFDakQsTUFBTUMsY0FBYyxTQUFVekUsT0FBUXdFLENBQUFBLE9BQU8sRUFBQyxJQUFNO1lBQ3BELElBQUlFLFNBQVM7WUFDYixJQUFJRixRQUFRLEtBQUtFLFNBQVM7aUJBQ3JCLElBQUlGLFFBQVEsS0FBS0UsU0FBUztpQkFDMUIsSUFBSUYsUUFBUSxLQUFLRSxTQUFTO2lCQUMxQkEsU0FBUztZQUVkLE1BQU1DLFVBQVUsY0FBZUQsU0FBVTtZQUN6QyxPQUFPO2dCQUFFRDtnQkFBYUM7Z0JBQVFDO1lBQVE7UUFDeEM7UUFFQSwrQ0FBK0M7UUFDL0MsTUFBTS9HLGtCQUFpQmpDLDJCQUFBQSxTQUFTaUMsY0FBYyxjQUF2QmpDLCtDQUFBQSx5QkFBeUJrQyxXQUFXO1FBQzNELElBQUlELG1CQUFtQixZQUFZQSxtQkFBbUIsU0FBUztZQUM3RCxNQUFNZ0gsYUFBYXpFLFNBQVN4RSxTQUFTb0IsZUFBZSxLQUFLO1lBQ3pELE1BQU04SCxXQUFXeEUsV0FBVzFFLFNBQVNxQixlQUFlLEtBQUs7WUFDekQsTUFBTThILGdCQUFnQm5KLFNBQVMwQixhQUFhLEdBQUdsSiw2Q0FBTUEsQ0FBQ3dILFNBQVMwQixhQUFhLElBQUlsSiw2Q0FBTUE7WUFDdEYsTUFBTTRRLGNBQWNwSixTQUFTcUosV0FBVyxHQUFHN1EsNkNBQU1BLENBQUN3SCxTQUFTcUosV0FBVyxJQUFJN1EsNkNBQU1BO1lBQ2hGLE1BQU04USxXQUFXRixZQUFZRyxJQUFJLENBQUNKLGVBQWU7WUFDakQsTUFBTUssU0FBU1osWUFBWUssWUFBWUMsVUFBVUk7WUFFakRYLFFBQVFsRyxJQUFJLENBQUM7Z0JBQ1hnSCxNQUFNO2dCQUNObEYsUUFBUTBFO2dCQUNSUyxrQkFBa0JKO2dCQUNsQjdFLGFBQWF5RTtnQkFDYkgsUUFBUVMsT0FBT1QsTUFBTTtnQkFDckJDLFNBQVNRLE9BQU9SLE9BQU87Z0JBQ3ZCRixhQUFhVSxPQUFPVixXQUFXO2dCQUMvQmEsUUFBUTNKLFNBQVNpQyxjQUFjO1lBQ2pDO1FBQ0Y7UUFFQSx1Q0FBdUM7UUFDdkMsSUFBSWpDLFNBQVNxQyxRQUFRLElBQUlyQyxTQUFTcUMsUUFBUSxDQUFDdkksTUFBTSxHQUFHLEdBQUc7WUFDckQsSUFBSThQLHNCQUFzQixHQUFHLGdDQUFnQztZQUM3RDVKLFNBQVNxQyxRQUFRLENBQUN3SCxPQUFPLENBQUMsQ0FBQ3hIO29CQUNGQTtnQkFBdkIsTUFBTUUsa0JBQWlCRiwyQkFBQUEsU0FBU0osY0FBYyxjQUF2QkksK0NBQUFBLHlCQUF5QkgsV0FBVztnQkFFM0QscURBQXFEO2dCQUNyRCxJQUFJSyxtQkFBbUIsWUFBWUEsbUJBQW1CLFNBQVM7b0JBQzdELE1BQU11SCxZQUFZcEYsV0FBV3JDLFNBQVNqQixlQUFlLEtBQUs7b0JBQzFELE1BQU0ySSxVQUFVckYsV0FBV3JDLFNBQVNoQixlQUFlLEtBQUs7b0JBQ3hELE1BQU0ySSxlQUFlM0gsU0FBU1gsYUFBYSxHQUFHbEosNkNBQU1BLENBQUM2SixTQUFTWCxhQUFhLElBQUlsSiw2Q0FBTUE7b0JBQ3JGLE1BQU15UixhQUFhNUgsU0FBU2dILFdBQVcsR0FBRzdRLDZDQUFNQSxDQUFDNkosU0FBU2dILFdBQVcsSUFBSTdRLDZDQUFNQTtvQkFDL0UsTUFBTTBSLFVBQVVELFdBQVdWLElBQUksQ0FBQ1MsY0FBYztvQkFDOUMsTUFBTUcsUUFBUXZCLFlBQVlrQixXQUFXQyxTQUFTRztvQkFFOUN2QixRQUFRbEcsSUFBSSxDQUFDO3dCQUNYZ0gsTUFBTSxXQUErQixPQUFwQkc7d0JBQ2pCckYsUUFBUXVGO3dCQUNSSixrQkFBa0JRO3dCQUNsQnpGLGFBQWFzRjt3QkFDYmhCLFFBQVFvQixNQUFNcEIsTUFBTTt3QkFDcEJDLFNBQVNtQixNQUFNbkIsT0FBTzt3QkFDdEJGLGFBQWFxQixNQUFNckIsV0FBVzt3QkFDOUJhLFFBQVF0SCxTQUFTSixjQUFjO29CQUNqQztvQkFFQTJILHVCQUF1QiwwQ0FBMEM7Z0JBQ25FO1lBQ0Y7UUFDRjtRQUVBLHVCQUF1QjtRQUN2QixNQUFNUSxVQUFVekIsUUFBUTBCLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxTQUFXRCxNQUFNQyxPQUFPdkIsT0FBTyxFQUFFO1FBRXRFLE9BQU87WUFDTEw7WUFDQXlCO1lBQ0F6RyxjQUFjM0QsU0FBU0MsSUFBSSxJQUFJO1FBQ2pDO0lBQ0YsR0FBRztRQUFDckQ7S0FBYTtJQUVqQix3REFBd0Q7SUFDeEQsTUFBTUYsYUFBYWtCLE1BQU07SUFFekIvRSxpREFBU0EsQ0FBQztRQUNSK0csUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQywyQkFBMkJyRSxlQUFlO1FBQ3REb0UsUUFBUUMsR0FBRyxDQUFDLHdCQUF3Qm5FO1FBQ3BDa0UsUUFBUUMsR0FBRyxDQUFDLHNDQUFzQ25FLHFCQUFxQkEsa0JBQWtCZ0UsSUFBSSxPQUFPO1FBQ3BHRSxRQUFRQyxHQUFHLENBQUMsaUJBQWlCbkQ7UUFDN0JrRCxRQUFRQyxHQUFHLENBQUMsK0JBQStCNkQsNEJBQUFBLHNDQUFBQSxnQkFBaUIwRyxPQUFPO1FBRW5FLElBQUk1TyxlQUFlLFNBQVNFLHFCQUFxQkEsa0JBQWtCZ0UsSUFBSSxPQUFPLE1BQU1oRCxlQUFjZ0gsNEJBQUFBLHNDQUFBQSxnQkFBaUIwRyxPQUFPLEdBQUU7WUFDMUgsTUFBTXpLLGdCQUFnQm5HLHFFQUFpQkEsQ0FBQ2tDLHNCQUFzQjtZQUM5RCxNQUFNOE8sb0JBQW9CN0ssZ0JBQWdCK0QsZ0JBQWdCMEcsT0FBTztZQUVqRXhLLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsbUJBQW1CRjtZQUMvQkMsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQjZELGdCQUFnQjBHLE9BQU87WUFDdER4SyxRQUFRQyxHQUFHLENBQUMsMkJBQXdCMks7WUFFcEMsMERBQTBEO1lBQzFELE1BQU1DLFFBQVFELG9CQUFvQjtZQUNsQyxJQUFJQyxVQUFVLEdBQUc7Z0JBQ2YscUVBQXFFO2dCQUNyRSxNQUFNQyxpQkFBaUI3SSxLQUFLQyxLQUFLLENBQUMwSSxvQkFBb0I7Z0JBQ3RELE1BQU1HLHNCQUFzQkQsaUJBQWlCO2dCQUM3QyxNQUFNRSw4QkFBOEJKLG9CQUFvQkc7Z0JBRXhEL0ssUUFBUUMsR0FBRyxDQUFDLFVBQVU0SztnQkFDdEI3SyxRQUFRQyxHQUFHLENBQUMsb0JBQW9CNks7Z0JBQ2hDOUssUUFBUUMsR0FBRyxDQUFDLDhCQUE4QjhLO2dCQUMxQy9LLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBa0MrSztnQkFFOUMsMEVBQTBFO2dCQUMxRSxNQUFNQyx3QkFBd0JGLHNCQUFzQmpILGdCQUFnQjBHLE9BQU87Z0JBQzNFLE1BQU1VLGlCQUFpQnhTLHVEQUFTQSxDQUFDLENBQUN1Uyx3QkFBd0IsR0FBRSxFQUFHOUksUUFBUTtnQkFDdkVsRSxTQUFTLHFCQUFxQmlOO2dCQUU5QixnQ0FBZ0M7Z0JBQ2hDak4sU0FBUyxpQkFBaUI2TSxlQUFlM0ksUUFBUTtnQkFFakRsRCxxQkFBcUIrTDtnQkFDckI3TCx5QkFBeUI7Z0JBQ3pCYSxRQUFRQyxHQUFHLENBQUM7Z0JBRVovSCxpREFBS0EsQ0FBQ2lULE9BQU8sQ0FBQywwQkFBbUYsT0FBekRMLGdCQUFlLDRDQUFxSixPQUEzRyxJQUFJTSxLQUFLQyxZQUFZLENBQUMsU0FBUztvQkFBRUMsT0FBTztvQkFBWUMsVUFBVTtnQkFBTSxHQUFHeEosTUFBTSxDQUFDaUosOEJBQTZCO1lBQzlNLE9BQU87Z0JBQ0w3TCx5QkFBeUI7Z0JBQ3pCRixxQkFBcUI7WUFDdkI7UUFDRixPQUFPO1lBQ0xFLHlCQUF5QjtZQUN6QkYscUJBQXFCO1FBQ3ZCO0lBQ0YsR0FBRztRQUFDckQ7UUFBWUU7UUFBbUJnQjtRQUFZZ0gsNEJBQUFBLHNDQUFBQSxnQkFBaUIwRyxPQUFPO1FBQUV2TTtLQUFTO0lBRWxGLG1DQUFtQztJQUNuQ2hGLGlEQUFTQSxDQUFDO1FBQ1IsSUFBSTZDLHFCQUFxQkYsWUFBWTtZQUNuQ2dFLGlCQUFpQjlELG1CQUFtQkY7UUFDdEM7SUFDRixHQUFHO1FBQUNFO1FBQW1CRjtRQUFZZ0U7UUFBa0JWO0tBQXNCO0lBRTNFLDBDQUEwQztJQUMxQ2pHLGlEQUFTQSxDQUFDO1FBQ1IsSUFBSTJDLGVBQWUsT0FBTztZQUN4Qiw4QkFBOEI7WUFDOUJ5RCxlQUFlO1lBQ2ZFLHNCQUFzQjtZQUN0QnRCLFNBQVMsY0FBYztZQUN2QkEsU0FBUyxjQUFjO1lBQ3ZCd0IsNkJBQTZCLEtBQUssdUJBQXVCO1FBQzNELE9BQU8sSUFBSTdELGVBQWUsU0FBUztZQUNqQyw4QkFBOEI7WUFDOUJ5RCxlQUFlO1lBQ2ZFLHNCQUFzQjtZQUN0QlYsY0FBYztZQUNkWixTQUFTLGNBQWM7WUFDdkJBLFNBQVMsY0FBYztZQUN2QndCLDZCQUE2QixLQUFLLHVCQUF1QjtRQUMzRDtJQUNGLEdBQUc7UUFBQzdEO1FBQVlxQztLQUFTO0lBRXpCLDZEQUE2RDtJQUM3RCxTQUFTdU4sV0FBVzNMLEtBQWE7UUFDL0IsSUFBSSxDQUFDQSxPQUFPLE9BQU87UUFDbkIsZ0ZBQWdGO1FBQ2hGLE9BQU9qRyxxRUFBaUJBLENBQUNpRyxVQUFVO0lBQ3JDO0lBSUEsNkJBQTZCO0lBQzdCLE1BQU00TCxpQkFBaUJELFdBQVd4TixNQUFNO0lBQ3hDLHFCQUFxQjtJQUNyQixNQUFNME4saUJBQWlCOU0sYUFBYWtHLFdBQVdsRyxXQUFXNUUsT0FBTyxDQUFDLEtBQUssSUFBSUEsT0FBTyxDQUFDLEtBQUssUUFBUTtJQUNoRyxvQkFBb0I7SUFDcEIsTUFBTTJSLGVBQWVGLGtCQUFrQkMsaUJBQWtCRCxpQkFBa0JDLENBQUFBLGlCQUFpQixHQUFFLElBQU07SUFFcEcsa0RBQWtEO0lBQ2xELE1BQU1FLGNBQWM7WUFxQk14TixzQkFXRUEsb0JBV0FBLGlCQWVBQSxhQWVBQSx3QkFXRkEsaUJBVUFBLGVBV0VBLGFBYUFBLGdCQVlBQSxnQkFZRkEsa0JBV0VBLGdCQVVBQSxxQkFpQkZBLGVBVUFBLGVBWUFBLGlCQVVBQTs2QkFuTnhCLDhEQUFDNEU7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFHRCxXQUFVOzhCQUFvQzs7Ozs7OzhCQUNsRCw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRzt3Q0FBRUgsV0FBVTtrREFBa0I7Ozs7OztrREFDL0IsOERBQUNuSyxnRUFBWUE7d0NBQ1hnQixPQUFPa0UsTUFBTTt3Q0FDYjZOLFVBQVUsQ0FBQ0MsSUFBTTdOLFNBQVMsZ0JBQWdCNk4sRUFBRUMsTUFBTSxDQUFDalMsS0FBSyxFQUFFO2dEQUFFZ1AsZ0JBQWdCOzRDQUFLOzswREFFakYsOERBQUNrRDtnREFBT2xTLE9BQU87MERBQU07Ozs7OzswREFDckIsOERBQUNrUztnREFBT2xTLE9BQU87MERBQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHekIsOERBQUNrSjtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ3BLLG9FQUFTQTtvQ0FDUmlGLFVBQVVBO29DQUNWc0csTUFBSztvQ0FDTDZILE9BQU07b0NBQ05qSSxPQUFPLENBQUMsQ0FBQzVGLE9BQU8xRCxZQUFZO29DQUM1QndSLFlBQVksRUFBRTlOLG1CQUFBQSw4QkFBQUEsdUJBQUFBLE9BQVExRCxZQUFZLGNBQXBCMEQsMkNBQUFBLHFCQUFzQitOLE9BQU87b0NBQzNDQyxPQUFNOzs7Ozs7Ozs7OzswQ0FHViw4REFBQ3BKO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNwSyxvRUFBU0E7NENBQ1JpRixVQUFVQTs0Q0FDVnNHLE1BQUs7NENBQ0w2SCxPQUFNOzRDQUNOakksT0FBTyxDQUFDLENBQUM1RixPQUFPekQsVUFBVTs0Q0FDMUJ1UixZQUFZLEVBQUU5TixtQkFBQUEsOEJBQUFBLHFCQUFBQSxPQUFRekQsVUFBVSxjQUFsQnlELHlDQUFBQSxtQkFBb0IrTixPQUFPOzRDQUN6Q0MsT0FBTTs7Ozs7Ozs7Ozs7a0RBR1YsOERBQUNwSjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3BLLG9FQUFTQTs0Q0FDUmlGLFVBQVVBOzRDQUNWc0csTUFBSzs0Q0FDTDZILE9BQU07NENBQ05JLFdBQVc7NENBQ1hySSxPQUFPLENBQUMsQ0FBQzVGLE9BQU94RCxPQUFPOzRDQUN2QnNSLFlBQVksRUFBRTlOLG1CQUFBQSw4QkFBQUEsa0JBQUFBLE9BQVF4RCxPQUFPLGNBQWZ3RCxzQ0FBQUEsZ0JBQWlCK04sT0FBTzs0Q0FDdENDLE9BQU07NENBQ05QLFVBQVVDLENBQUFBO2dEQUNSN04sU0FBUyxXQUFXcEUscUJBQXFCaVMsRUFBRUMsTUFBTSxDQUFDalMsS0FBSyxHQUFHO29EQUFFZ1AsZ0JBQWdCO2dEQUFLOzRDQUNuRjs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSU4sOERBQUM5RjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDcEssb0VBQVNBOzRDQUNSaUYsVUFBVUE7NENBQ1ZzRyxNQUFLOzRDQUNMNkgsT0FBTTs0Q0FDTmpJLE9BQU8sQ0FBQyxDQUFDNUYsT0FBT3ZELEdBQUc7NENBQ25CcVIsWUFBWSxFQUFFOU4sbUJBQUFBLDhCQUFBQSxjQUFBQSxPQUFRdkQsR0FBRyxjQUFYdUQsa0NBQUFBLFlBQWErTixPQUFPOzRDQUNsQ0MsT0FBTTs0Q0FDTlAsVUFBVUMsQ0FBQUE7Z0RBQ1IsTUFBTWhTLFFBQVFrRSxNQUFNLG9CQUFvQixPQUFPckYsc0RBQVFBLENBQUNtVCxFQUFFQyxNQUFNLENBQUNqUyxLQUFLLElBQUl0QixxREFBT0EsQ0FBQ3NULEVBQUVDLE1BQU0sQ0FBQ2pTLEtBQUs7Z0RBQ2hHbUUsU0FBUyxPQUFPbkUsT0FBTztvREFBRWdQLGdCQUFnQjtnREFBSzs0Q0FDaEQ7Ozs7Ozs7Ozs7O2tEQUdKLDhEQUFDOUY7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNwSyxvRUFBU0E7NENBQ1JnUixNQUFLOzRDQUNML0wsVUFBVUE7NENBQ1ZzRyxNQUFLOzRDQUNMNkgsT0FBTTs0Q0FDTmpJLE9BQU8sQ0FBQyxDQUFDNUYsT0FBT3RELGNBQWM7NENBQzlCb1IsWUFBWSxFQUFFOU4sbUJBQUFBLDhCQUFBQSx5QkFBQUEsT0FBUXRELGNBQWMsY0FBdEJzRCw2Q0FBQUEsdUJBQXdCK04sT0FBTzs0Q0FDN0NDLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUlaLDhEQUFDcEo7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNwSyxvRUFBU0E7b0NBQ1JpRixVQUFVQTtvQ0FDVnNHLE1BQUs7b0NBQ0w2SCxPQUFNO29DQUNOakksT0FBTyxDQUFDLENBQUM1RixPQUFPcEQsT0FBTztvQ0FDdkJrUixZQUFZLEVBQUU5TixtQkFBQUEsOEJBQUFBLGtCQUFBQSxPQUFRcEQsT0FBTyxjQUFmb0Qsc0NBQUFBLGdCQUFpQitOLE9BQU87b0NBQ3RDQyxPQUFNOzs7Ozs7Ozs7OzswQ0FHViw4REFBQ3BKO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDcEssb0VBQVNBO29DQUNSaUYsVUFBVUE7b0NBQ1ZzRyxNQUFLO29DQUNMNkgsT0FBTTtvQ0FDTmpJLE9BQU8sQ0FBQyxDQUFDNUYsT0FBT25ELEtBQUs7b0NBQ3JCaVIsWUFBWSxFQUFFOU4sbUJBQUFBLDhCQUFBQSxnQkFBQUEsT0FBUW5ELEtBQUssY0FBYm1ELG9DQUFBQSxjQUFlK04sT0FBTztvQ0FDcENDLE9BQU07Ozs7Ozs7Ozs7OzBDQUdWLDhEQUFDcEo7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ3BLLG9FQUFTQTs0Q0FDUmlGLFVBQVVBOzRDQUNWc0csTUFBSzs0Q0FDTDZILE9BQU07NENBQ05qSSxPQUFPLENBQUMsQ0FBQzVGLE9BQU9sRCxHQUFHOzRDQUNuQmdSLFlBQVksRUFBRTlOLG1CQUFBQSw4QkFBQUEsY0FBQUEsT0FBUWxELEdBQUcsY0FBWGtELGtDQUFBQSxZQUFhK04sT0FBTzs0Q0FDbENDLE9BQU07NENBQ05QLFVBQVVDLENBQUFBO2dEQUNSN04sU0FBUyxPQUFPMUYscURBQU9BLENBQUN1VCxFQUFFQyxNQUFNLENBQUNqUyxLQUFLLEdBQUc7b0RBQUVnUCxnQkFBZ0I7Z0RBQUs7NENBQ2xFOzs7Ozs7Ozs7OztrREFHSiw4REFBQzlGO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDcEssb0VBQVNBOzRDQUNSaUYsVUFBVUE7NENBQ1ZzRyxNQUFLOzRDQUNMNkgsT0FBTTs0Q0FDTmpJLE9BQU8sQ0FBQyxDQUFDNUYsT0FBT2pELE1BQU07NENBQ3RCK1EsWUFBWSxFQUFFOU4sbUJBQUFBLDhCQUFBQSxpQkFBQUEsT0FBUWpELE1BQU0sY0FBZGlELHFDQUFBQSxlQUFnQitOLE9BQU87NENBQ3JDQyxPQUFNOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJWiw4REFBQ3BKO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ3BLLG9FQUFTQTt3Q0FDUmlGLFVBQVVBO3dDQUNWc0csTUFBSzt3Q0FDTDZILE9BQU07d0NBQ05qSSxPQUFPLENBQUMsQ0FBQzVGLE9BQU83QyxNQUFNO3dDQUN0QjJRLFlBQVksRUFBRTlOLG1CQUFBQSw4QkFBQUEsaUJBQUFBLE9BQVE3QyxNQUFNLGNBQWQ2QyxxQ0FBQUEsZUFBZ0IrTixPQUFPO3dDQUNyQ0MsT0FBTTt3Q0FDTkUsYUFBWTs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJbEIsOERBQUN0SjtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ3BLLG9FQUFTQTtvQ0FDUmlGLFVBQVVBO29DQUNWc0csTUFBSztvQ0FDTDZILE9BQU07b0NBQ05qSSxPQUFPLENBQUMsQ0FBQzVGLE9BQU9oRCxRQUFRO29DQUN4QjhRLFlBQVksRUFBRTlOLG1CQUFBQSw4QkFBQUEsbUJBQUFBLE9BQVFoRCxRQUFRLGNBQWhCZ0QsdUNBQUFBLGlCQUFrQitOLE9BQU87b0NBQ3ZDQyxPQUFNOzs7Ozs7Ozs7OzswQ0FHViw4REFBQ3BKO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNwSyxvRUFBU0E7NENBQ1JpRixVQUFVQTs0Q0FDVnNHLE1BQUs7NENBQ0w2SCxPQUFNOzRDQUNOakksT0FBTyxDQUFDLENBQUM1RixPQUFPL0MsTUFBTTs0Q0FDdEI2USxZQUFZLEVBQUU5TixtQkFBQUEsOEJBQUFBLGlCQUFBQSxPQUFRL0MsTUFBTSxjQUFkK0MscUNBQUFBLGVBQWdCK04sT0FBTzs0Q0FDckNDLE9BQU07Ozs7Ozs7Ozs7O2tEQUdWLDhEQUFDcEo7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNwSyxvRUFBU0E7NENBQ1JpRixVQUFVQTs0Q0FDVnNHLE1BQUs7NENBQ0w2SCxPQUFNOzRDQUNOakksT0FBTyxDQUFDLENBQUM1RixPQUFPOUMsV0FBVzs0Q0FDM0I0USxZQUFZLEVBQUU5TixtQkFBQUEsOEJBQUFBLHNCQUFBQSxPQUFROUMsV0FBVyxjQUFuQjhDLDBDQUFBQSxvQkFBcUIrTixPQUFPOzRDQUMxQ0MsT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFPaEIsOERBQUNsSjtvQkFBR0QsV0FBVTs4QkFBb0M7Ozs7Ozs4QkFDbEQsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ3BLLG9FQUFTQTt3Q0FDUmlGLFVBQVVBO3dDQUNWc0csTUFBSzt3Q0FDTDZILE9BQU07d0NBQ05qSSxPQUFPLENBQUMsQ0FBQzVGLE9BQU81QyxLQUFLO3dDQUNyQjBRLFlBQVksRUFBRTlOLG1CQUFBQSw4QkFBQUEsZ0JBQUFBLE9BQVE1QyxLQUFLLGNBQWI0QyxvQ0FBQUEsY0FBZStOLE9BQU87d0NBQ3BDQyxPQUFNOzs7Ozs7Ozs7Ozs4Q0FHViw4REFBQ3BKO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDcEssb0VBQVNBO3dDQUNSaUYsVUFBVUE7d0NBQ1ZzRyxNQUFLO3dDQUNMNkgsT0FBTTt3Q0FDTmpJLE9BQU8sQ0FBQyxDQUFDNUYsT0FBTzNDLEtBQUs7d0NBQ3JCeVEsWUFBWSxFQUFFOU4sbUJBQUFBLDhCQUFBQSxnQkFBQUEsT0FBUTNDLEtBQUssY0FBYjJDLG9DQUFBQSxjQUFlK04sT0FBTzt3Q0FDcENDLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlaLDhEQUFDcEo7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ3BLLG9FQUFTQTt3Q0FDUmlGLFVBQVVBO3dDQUNWc0csTUFBSzt3Q0FDTDZILE9BQU07d0NBQ05qSSxPQUFPLENBQUMsQ0FBQzVGLE9BQU8xQyxPQUFPO3dDQUN2QndRLFlBQVksRUFBRTlOLG1CQUFBQSw4QkFBQUEsa0JBQUFBLE9BQVExQyxPQUFPLGNBQWYwQyxzQ0FBQUEsZ0JBQWlCK04sT0FBTzt3Q0FDdENDLE9BQU07Ozs7Ozs7Ozs7OzhDQUdWLDhEQUFDcEo7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNwSyxvRUFBU0E7d0NBQ1JpRixVQUFVQTt3Q0FDVnNHLE1BQUs7d0NBQ0w2SCxPQUFNO3dDQUNOakksT0FBTyxDQUFDLENBQUM1RixPQUFPekMsUUFBUTt3Q0FDeEJ1USxZQUFZLEVBQUU5TixtQkFBQUEsOEJBQUFBLG1CQUFBQSxPQUFRekMsUUFBUSxjQUFoQnlDLHVDQUFBQSxpQkFBa0IrTixPQUFPO3dDQUN2Q0MsT0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTWQsOERBQUNsSjtvQkFBR0QsV0FBVTs4QkFBb0M7Ozs7Ozs4QkFDbEQsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3NKOzRCQUNFLEdBQUd6TyxTQUFTLGNBQWM7NEJBQzNCbUYsV0FBVTs0QkFDVnFKLGFBQVk7Ozs7Ozt3QkFFYmxPLE9BQU8xQixXQUFXLGtCQUNqQiw4REFBQzhQOzRCQUFLdkosV0FBVTtzQ0FBd0I3RSxPQUFPMUIsV0FBVyxDQUFDeVAsT0FBTzs7Ozs7Ozs7Ozs7OzhCQUl0RSw4REFBQ25KO29CQUFJQyxXQUFVOzhCQUViLDRFQUFDaEwseURBQU1BO3dCQUFDd1UsTUFBSzt3QkFBSzVDLE1BQUs7d0JBQVM1RyxXQUFVO3dCQUFrRUUsU0FBU3NFO2tDQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFPdkksbUVBQW1FO0lBQ25FLE1BQU1pRixjQUFjO1lBMkNRdE8sMkJBNFRBQSw0QkFnQkVBLHVCQWFKQSx5QkFZQUEsd0JBdUJBQTs2QkF0YXhCLDhEQUFDdU87WUFBSzlJLFVBQVU5RixhQUFhOEYsVUFBVSxDQUFDekY7Z0JBQ3RDNEIsUUFBUUMsR0FBRyxDQUFDLDZCQUF1QjdCO2dCQUNuQ2xHLGlEQUFLQSxDQUFDOEwsS0FBSyxDQUFDO1lBQ2Q7OzhCQUNFLDhEQUFDaEI7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNtSjtvQ0FBTW5KLFdBQVU7OENBQXdCOzs7Ozs7OENBQ3pDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNuSyxnRUFBWUE7NENBQ1hnQixPQUFPa0UsTUFBTTs0Q0FDYjZOLFVBQVVDLENBQUFBO2dEQUNSLE1BQU1jLGNBQWNkLEVBQUVDLE1BQU0sQ0FBQ2pTLEtBQUs7Z0RBQ2xDLG1EQUFtRDtnREFDbkQsSUFBSW9HLDRCQUE0QixTQUFTME0sZ0JBQWdCLFNBQVM7b0RBQ2hFMVUsaURBQUtBLENBQUM4TCxLQUFLLENBQUM7b0RBQ1o7Z0RBQ0Y7Z0RBQ0EvRixTQUFTLGNBQWMyTyxhQUFhO29EQUFFOUQsZ0JBQWdCO2dEQUFLOzRDQUM3RDs7OERBRUEsOERBQUNrRDtvREFBT2xTLE9BQU07OERBQVE7Ozs7Ozs4REFDdEIsOERBQUNrUztvREFBT2xTLE9BQU07OERBQU07Ozs7Ozs7Ozs7OztzREFFdEIsOERBQUMwUzs0Q0FBS3ZKLFdBQVU7c0RBQThCOzs7Ozs7d0NBRTdDL0MsNEJBQTRCLHVCQUMzQiw4REFBQzhDOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRztnREFBRUgsV0FBVTs7b0RBQTBCO2tFQUNsQyw4REFBQzRKO2tFQUFPOzs7Ozs7b0RBQThCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBT2hEN08sTUFBTSxrQkFBa0IsdUJBQ3ZCLDhEQUFDZ0Y7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDcEssb0VBQVNBOzRDQUNSaUYsVUFBVUE7NENBQ1ZzRyxNQUFLOzRDQUNMNkgsT0FBTTs0Q0FDTmpJLE9BQU8sQ0FBQyxDQUFDNUYsT0FBT3RDLGlCQUFpQixJQUFJLENBQUMsQ0FBQzREOzRDQUN2Q3dNLGNBQWM5TixDQUFBQSxtQkFBQUEsOEJBQUFBLDRCQUFBQSxPQUFRdEMsaUJBQWlCLGNBQXpCc0MsZ0RBQUFBLDBCQUEyQitOLE9BQU8sS0FBSXpNOzRDQUNwRDBNLE9BQU07NENBQ05FLGFBQVk7NENBQ1pyTyxVQUFVNk4sQ0FBQUE7Z0RBQ1I5TCxRQUFRQyxHQUFHLENBQUMsK0JBQStCNkw7Z0RBQzNDLE1BQU1nQixjQUFjaEIsSUFBSXBULHVEQUFTQSxDQUFDb1QsS0FBSztnREFDdkM5TCxRQUFRQyxHQUFHLENBQUMscUNBQWtDNk07Z0RBQzlDN08sU0FBUyxxQkFBcUI2TyxhQUFhO29EQUFFaEUsZ0JBQWdCO2dEQUFLOzRDQUNwRTs7Ozs7O3dDQUdENUoseUJBQXlCUSx1QkFBdUIsb0JBQy9DLDhEQUFDc0Q7NENBQUlDLFdBQVU7c0RBQThCOzs7Ozs7Ozs7Ozs7Z0NBT2xEakYsTUFBTSxrQkFBa0IseUJBQ3ZCLDhEQUFDZ0Y7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNoTCx5REFBTUE7d0NBQUM0UixNQUFLO3dDQUFTNUcsV0FBVTt3Q0FBeUVFLFNBQVMsSUFBTUU7a0RBQXNCOzs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFLbkpyRixNQUFNLGtCQUFrQix5QkFDdkIsOERBQUNnRjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNHO29DQUFFSCxXQUFVOzt3Q0FBMEI7d0NBQWtCd0ksaUJBQWlCQSxlQUFlc0IsY0FBYyxDQUFDLFNBQVM7NENBQUV6QixPQUFPOzRDQUFZQyxVQUFVO3dDQUFNLEtBQUs7Ozs7Ozs7OENBQzNKLDhEQUFDbkk7b0NBQUVILFdBQVU7O3dDQUF1Qjt3Q0FBb0IwSSxlQUFlQSxhQUFhb0IsY0FBYyxDQUFDLFNBQVM7NENBQUV6QixPQUFPOzRDQUFZQyxVQUFVO3dDQUFNLEtBQUs7Ozs7Ozs7Z0NBQ3JKM00sNEJBQ0MsOERBQUN3RTtvQ0FBRUgsV0FBVTs7d0NBQWdDO3dDQUEyQnJFOzs7Ozs7Ozs7Ozs7O3dCQUs1RVosQ0FBQUEsTUFBTSxrQkFBa0IsV0FBVzhGLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCQyxZQUFZLE1BQUssT0FBTSxtQkFDM0UsOERBQUNmOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQytKO29DQUFHL0osV0FBVTs4Q0FBK0I7Ozs7Ozs4Q0FDL0MsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDZ0s7d0NBQU1oSyxXQUFVOzswREFDZiw4REFBQ2lLOzBEQUNDLDRFQUFDQztvREFBR2xLLFdBQVU7O3NFQUNaLDhEQUFDbUs7NERBQUduSyxXQUFVO3NFQUFvRDs7Ozs7O3NFQUNsRSw4REFBQ21LOzREQUFHbkssV0FBVTtzRUFBb0Q7Ozs7OztzRUFDbEUsOERBQUNtSzs0REFBR25LLFdBQVU7c0VBQW9EOzs7Ozs7c0VBQ2xFLDhEQUFDbUs7NERBQUduSyxXQUFVO3NFQUFvRDs7Ozs7O3NFQUNsRSw4REFBQ21LOzREQUFHbkssV0FBVTtzRUFBb0Q7Ozs7OztzRUFDbEUsOERBQUNtSzs0REFBR25LLFdBQVU7c0VBQW9EOzs7Ozs7c0VBQ2xFLDhEQUFDbUs7NERBQUduSyxXQUFVO3NFQUEwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBRzVDLDhEQUFDb0s7MERBQ0U1UCxrQ0FDQyw4REFBQzBQOzhEQUNDLDRFQUFDRzt3REFBR0MsU0FBUzt3REFBR3RLLFdBQVU7a0VBQXdCOzs7Ozs7Ozs7OzJEQUVsRGEsZ0NBQ0Y7O3dEQUVHQSxnQkFBZ0JpRixPQUFPLENBQUM3TyxNQUFNLEdBQUcsSUFDaEM0SixnQkFBZ0JpRixPQUFPLENBQUN5RSxHQUFHLENBQUMsQ0FBQzdDLFFBQWE4QyxzQkFDMUMsOERBQUNOOztrRkFDQyw4REFBQ0c7d0VBQUdySyxXQUFVO2tGQUNYMEgsT0FBT2QsSUFBSTs7Ozs7O2tGQUVkLDhEQUFDeUQ7d0VBQUdySyxXQUFVO2tGQUNYLElBQUltSSxLQUFLQyxZQUFZLENBQUMsU0FBUzs0RUFDOUJDLE9BQU87NEVBQ1BDLFVBQVU7d0VBQ1osR0FBR3hKLE1BQU0sQ0FBQzRJLE9BQU9oRyxNQUFNOzs7Ozs7a0ZBRXpCLDhEQUFDMkk7d0VBQUdySyxXQUFVOzs0RUFBdUMwSCxPQUFPYixnQkFBZ0I7NEVBQUM7Ozs7Ozs7a0ZBQzdFLDhEQUFDd0Q7d0VBQUdySyxXQUFVOzs0RUFBdUMwSCxPQUFPOUYsV0FBVzs0RUFBQzs7Ozs7OztrRkFDeEUsOERBQUN5STt3RUFBR3JLLFdBQVU7OzRFQUF1QzBILE9BQU94QixNQUFNOzRFQUFDOzs7Ozs7O2tGQUNuRSw4REFBQ21FO3dFQUFHckssV0FBVTtrRkFDWCxJQUFJbUksS0FBS0MsWUFBWSxDQUFDLFNBQVM7NEVBQzlCQyxPQUFPOzRFQUNQQyxVQUFVO3dFQUNaLEdBQUd4SixNQUFNLENBQUM0SSxPQUFPdkIsT0FBTzs7Ozs7O2tGQUUxQiw4REFBQ2tFO3dFQUFHckssV0FBVTtrRkFBWTs7Ozs7OzsrREFuQm5Cd0s7Ozs7c0ZBdUJULDhEQUFDTjtzRUFDQyw0RUFBQ0c7Z0VBQUdDLFNBQVM7Z0VBQUd0SyxXQUFVOzBFQUFzQzs7Ozs7Ozs7Ozs7c0VBS3BFLDhEQUFDa0s7OzhFQUNDLDhEQUFDRztvRUFBR0MsU0FBUztvRUFBR3RLLFdBQVU7OEVBQTBFOzs7Ozs7OEVBQ3BHLDhEQUFDcUs7b0VBQUdySyxXQUFVOzhFQUNYLElBQUltSSxLQUFLQyxZQUFZLENBQUMsU0FBUzt3RUFDOUJDLE9BQU87d0VBQ1BDLFVBQVU7b0VBQ1osR0FBR3hKLE1BQU0sQ0FBQytCLGdCQUFnQjBHLE9BQU87Ozs7Ozs7Ozs7Ozs7aUZBS3ZDLDhEQUFDMkM7OERBQ0MsNEVBQUNHO3dEQUFHQyxTQUFTO3dEQUFHdEssV0FBVTtrRUFBc0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFZM0UsQ0FBQ3hGLHFCQUFxQixDQUFDMkUsb0NBQ3RCLDhEQUFDWTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMrSjtvQ0FBRy9KLFdBQVU7OENBQTRCOzs7Ozs7OENBQzFDLDhEQUFDRztvQ0FBRUgsV0FBVTs4Q0FBcUI7Ozs7Ozs7Ozs7Ozt3QkFRckNqRixNQUFNLGtCQUFrQixTQUFTOEYsQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJDLFlBQVksTUFBSyx5QkFDbEUsOERBQUNmOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRztnQ0FBRUgsV0FBVTs7b0NBQXdCO2tEQUNoQyw4REFBQzRKO2tEQUFPOzs7Ozs7b0NBQXdCOzs7Ozs7Ozs7Ozs7d0JBT3hDN08sTUFBTSxrQkFBa0IsU0FBUzhGLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCQyxZQUFZLE1BQUsseUJBQ2xFLDhEQUFDZjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0c7Z0NBQUVILFdBQVU7O29DQUEwQjtrREFDbEMsOERBQUM0SjtrREFBTzs7Ozs7O29DQUErQjs7Ozs7Ozs7Ozs7O3dCQU85QzdPLENBQUFBLE1BQU0sa0JBQWtCLFdBQVc4RixDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQkMsWUFBWSxNQUFLLE9BQU0sS0FBTSxDQUFDM0UsNkJBQ2xGLDhEQUFDNEQ7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNHO2dDQUFFSCxXQUFVOztvQ0FBdUI7a0RBQy9CLDhEQUFDNEo7a0RBQU87Ozs7OztvQ0FBMEI7Ozs7Ozs7Ozs7Ozt3QkFNekM3TyxDQUFBQSxNQUFNLGtCQUFrQixXQUFXOEYsQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJDLFlBQVksTUFBSyxPQUFNLEtBQU0zRSw2QkFDakY7OzhDQUNFLDhEQUFDNEQ7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNHO3dDQUFFSCxXQUFVOzs0Q0FBeUI7MERBQ2xDLDhEQUFDNEo7MERBQU87Ozs7Ozs0Q0FBc0I7Ozs7Ozs7Ozs7Ozs4Q0FHcEMsOERBQUM3SjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNtSjs0Q0FBTW5KLFdBQVU7OzhEQUNmLDhEQUFDeUs7b0RBQ0M3RCxNQUFLO29EQUNMNUcsV0FBVTtvREFDVjBLLFNBQVMzUCxNQUFNO29EQUNmNk4sVUFBVSxDQUFDQzt3REFDVCxJQUFJQSxFQUFFQyxNQUFNLENBQUM0QixPQUFPLEVBQUU7NERBQ3BCMVAsU0FBUyxjQUFjOzREQUN2QkEsU0FBUyxjQUFjLFFBQVEseUJBQXlCOzREQUN4RHNCLHNCQUFzQjs0REFFdEIsaUVBQWlFOzREQUNqRSxJQUFJQywyQkFBMkI7Z0VBQzdCdkIsU0FBUyxxQkFBcUJ1QjtnRUFDOUJ0SCxpREFBS0EsQ0FBQzBWLElBQUksQ0FBQzs0REFDYjt3REFDRixPQUFPOzREQUNMM1AsU0FBUyxjQUFjOzREQUN2QnNCLHNCQUFzQixDQUFDLENBQUN2QixNQUFNLGdCQUFnQixtQ0FBbUM7d0RBQ25GO29EQUNGOzs7Ozs7Z0RBQ0E7Ozs7Ozs7c0RBR0osOERBQUNvTzs0Q0FBTW5KLFdBQVU7OzhEQUNmLDhEQUFDeUs7b0RBQ0M3RCxNQUFLO29EQUNMNUcsV0FBVTtvREFDVjBLLFNBQVMzUCxNQUFNO29EQUNmNk4sVUFBVSxDQUFDQzt3REFDVCxJQUFJQSxFQUFFQyxNQUFNLENBQUM0QixPQUFPLEVBQUU7NERBQ3BCMVAsU0FBUyxjQUFjOzREQUN2QkEsU0FBUyxjQUFjLFFBQVEseUJBQXlCOzREQUN4RHNCLHNCQUFzQjs0REFFdEIsd0VBQXdFOzREQUN4RSxNQUFNc08sYUFBYTdQLE1BQU07NERBQ3pCLElBQUk2UCxlQUFjL0osNEJBQUFBLHNDQUFBQSxnQkFBaUIwRyxPQUFPLEtBQUkxRyxnQkFBZ0IwRyxPQUFPLEdBQUcsR0FBRztnRUFDekUsK0NBQStDO2dFQUMvQyxJQUFJLENBQUNoTCwyQkFBMkI7b0VBQzlCQyw2QkFBNkJvTztnRUFDL0I7Z0VBRUEsTUFBTTlOLGdCQUFnQnlMLFdBQVdxQztnRUFDakMsTUFBTUMsZ0JBQWdCaEssZ0JBQWdCMEcsT0FBTyxFQUFFLHVDQUF1QztnRUFFdEYsYUFBYTtnRUFDYnhLLFFBQVFDLEdBQUcsQ0FBQztnRUFDWkQsUUFBUUMsR0FBRyxDQUFDLHlCQUF5QjROO2dFQUNyQzdOLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBOEJGO2dFQUMxQ0MsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QjZOO2dFQUVuQyxNQUFNQyxtQkFBbUJoTyxnQkFBZ0IrTjtnRUFDekM5TixRQUFRQyxHQUFHLENBQUMsdUJBQXVCOE47Z0VBRW5DLCtDQUErQztnRUFDL0MsSUFBSUEsb0JBQW9CLEdBQUc7b0VBQ3pCN1YsaURBQUtBLENBQUM4TCxLQUFLLENBQUM7b0VBQ1ovRixTQUFTLGNBQWM7b0VBQ3ZCO2dFQUNGO2dFQUVBLCtCQUErQjtnRUFDL0IseUVBQXlFO2dFQUN6RSxNQUFNK1Asa0JBQWtCL0wsS0FBS2dNLEtBQUssQ0FBQ0YsbUJBQW1CO2dFQUN0RCxNQUFNN0MsaUJBQWlCeFMsdURBQVNBLENBQUNzVixnQkFBZ0I3TCxRQUFRO2dFQUN6RG5DLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0IrTjtnRUFDbENoTyxRQUFRQyxHQUFHLENBQUMsb0JBQW9CaUw7Z0VBQ2hDak4sU0FBUyxxQkFBcUJpTjtnRUFFOUJoVCxpREFBS0EsQ0FBQzBMLE9BQU8sQ0FBQyw4QkFBMkgsT0FBN0YsSUFBSXdILEtBQUtDLFlBQVksQ0FBQyxTQUFTO29FQUFFQyxPQUFPO29FQUFZQyxVQUFVO2dFQUFNLEdBQUd4SixNQUFNLENBQUMrTCxnQkFBZTs0REFDM0ksT0FBTztnRUFDTDVWLGlEQUFLQSxDQUFDOEwsS0FBSyxDQUFDO2dFQUNaL0YsU0FBUyxjQUFjOzREQUN6Qjs0REFFQSwwQkFBMEI7NERBQzFCL0YsaURBQUtBLENBQUMwVixJQUFJLENBQUM7d0RBQ2IsT0FBTzs0REFDTDNQLFNBQVMsY0FBYzs0REFDdkJzQixzQkFBc0IsQ0FBQyxDQUFDdkIsTUFBTSxnQkFBZ0IsbUNBQW1DOzREQUVqRixzQ0FBc0M7NERBQ3RDLElBQUl3QiwyQkFBMkI7Z0VBQzdCdkIsU0FBUyxxQkFBcUJ1QjtnRUFDOUJ0SCxpREFBS0EsQ0FBQzBWLElBQUksQ0FBQzs0REFDYjt3REFDRjtvREFDRjs7Ozs7O2dEQUNBOzs7Ozs7Ozs7Ozs7Ozs7d0JBUVQ1UCxNQUFNLGlCQUFpQndCLDhCQUE2QnNFLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCMEcsT0FBTyxtQkFDM0UsOERBQUN4SDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMrSjtvQ0FBRy9KLFdBQVU7OENBQStCOzs7Ozs7OENBQzdDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNHOzs4REFBRSw4REFBQ3lKOzhEQUFPOzs7Ozs7Z0RBQXdCO2dEQUFFck47Ozs7Ozs7c0RBQ3JDLDhEQUFDNEQ7OzhEQUFFLDhEQUFDeUo7OERBQU87Ozs7OztnREFBdUM7Z0RBQUUsSUFBSXpCLEtBQUtDLFlBQVksQ0FBQyxTQUFTO29EQUFFQyxPQUFPO29EQUFZQyxVQUFVO2dEQUFNLEdBQUd4SixNQUFNLENBQUMrQixnQkFBZ0IwRyxPQUFPOzs7Ozs7O3NEQUN6Siw4REFBQ3BIOzs4REFBRSw4REFBQ3lKOzhEQUFPOzs7Ozs7Z0RBQW9DO2dEQUFFN08sTUFBTTs7Ozs7OztzREFDdkQsOERBQUNvRjs0Q0FBRUgsV0FBVTtzREFBK0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFRakQvRCx5QkFBeUJsQixNQUFNLGtCQUFrQixTQUFTQSxNQUFNLCtCQUMvRCw4REFBQ2dGOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQytKO29DQUFHL0osV0FBVTs4Q0FBaUM7Ozs7Ozs4Q0FDL0MsOERBQUNHO29DQUFFSCxXQUFVOztzREFDWCw4REFBQzRKO3NEQUFPOzs7Ozs7d0NBQTBDOzs7Ozs7OzhDQUdwRCw4REFBQ3pKO29DQUFFSCxXQUFVOzt3Q0FBNkM7d0NBQzZDO3NEQUNyRyw4REFBQ3VKOzRDQUFLdkosV0FBVTtzREFDYmpFLGtCQUFrQitOLGNBQWMsQ0FBQyxTQUFTO2dEQUFFekIsT0FBTztnREFBWUMsVUFBVTs0Q0FBTTs7Ozs7O3dDQUMzRTs7Ozs7Ozs4Q0FFVCw4REFBQ3ZJO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRzt3Q0FBRUgsV0FBVTs7MERBQ1gsOERBQUM0SjswREFBTzs7Ozs7OzRDQUFpQjswREFBK0QsOERBQUNxQjs7Ozs7NENBQUk7MERBQ3pFLDhEQUFDckI7O29EQUFRNUssS0FBS0MsS0FBSyxDQUFDLENBQUN0SSxxRUFBaUJBLENBQUNvRSxNQUFNLHdCQUF5QjhGLENBQUFBLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCMEcsT0FBTyxLQUFJLEVBQUMsSUFBSztvREFBTTs7Ozs7OzswREFBaUIsOERBQUMwRDs7Ozs7NENBQUk7MERBQ2xJLDhEQUFDckI7MERBQVE3TixrQkFBa0IrTixjQUFjLENBQUMsU0FBUztvREFBRXpCLE9BQU87b0RBQVlDLFVBQVU7Z0RBQU07Ozs7Ozs0Q0FBWTs7Ozs7Ozs7Ozs7OzhDQUc5SCw4REFBQ3ZJO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRzt3Q0FBRUgsV0FBVTs7NENBQXlCOzBEQUNsQyw4REFBQzRKOzBEQUFPOzs7Ozs7NENBQWlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT3JELDhEQUFDM0o7b0JBQUdELFdBQVU7OEJBQW9DOzs7Ozs7OEJBQ2xELDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7b0NBRVpqRixNQUFNLGtCQUFrQix5QkFDdkIsOERBQUNuRixvRUFBU0E7d0NBQ1JpRixVQUFVQTt3Q0FDVnNHLE1BQUs7d0NBQ0w2SCxPQUFNO3dDQUNOakksT0FBTyxDQUFDLENBQUM1RixPQUFPdEMsaUJBQWlCLElBQUksQ0FBQyxDQUFDNEQ7d0NBQ3ZDd00sY0FBYzlOLENBQUFBLG1CQUFBQSw4QkFBQUEsNkJBQUFBLE9BQVF0QyxpQkFBaUIsY0FBekJzQyxpREFBQUEsMkJBQTJCK04sT0FBTyxLQUFJek07d0NBQ3BEME0sT0FBTTt3Q0FDTm5PLFVBQVU2TixDQUFBQTs0Q0FDUixNQUFNZ0IsY0FBY2hCLElBQUlwVCx1REFBU0EsQ0FBQ29ULEtBQUs7NENBQ3ZDN04sU0FBUyxxQkFBcUI2TyxhQUFhO2dEQUFFaEUsZ0JBQWdCOzRDQUFLO3dDQUNwRTs7Ozs7O29DQUlIOUssTUFBTSxrQkFBa0IsdUJBQ3ZCLDhEQUFDZ0Y7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNwSyxvRUFBU0E7NENBQ1JpRixVQUFVQTs0Q0FDVnNHLE1BQUs7NENBQ0w2SCxPQUFNOzRDQUNOakksT0FBTyxDQUFDLENBQUM1RixPQUFPekIsYUFBYTs0Q0FDN0J1UCxZQUFZLEVBQUU5TixtQkFBQUEsOEJBQUFBLHdCQUFBQSxPQUFRekIsYUFBYSxjQUFyQnlCLDRDQUFBQSxzQkFBdUIrTixPQUFPOzRDQUM1Q0MsT0FBTTs0Q0FDTkUsYUFBWTs0Q0FDWjZCLFVBQVU7Ozs7Ozs7Ozs7O2tEQUloQiw4REFBQ3RWLG9FQUFTQTt3Q0FDUmlGLFVBQVVBO3dDQUNWK0wsTUFBSzt3Q0FDTHpGLE1BQUs7d0NBQ0w2SCxPQUFNO3dDQUNOakksT0FBTyxDQUFDLENBQUM1RixPQUFPaEMsZUFBZTt3Q0FDL0I4UCxZQUFZLEVBQUU5TixtQkFBQUEsOEJBQUFBLDBCQUFBQSxPQUFRaEMsZUFBZSxjQUF2QmdDLDhDQUFBQSx3QkFBeUIrTixPQUFPO3dDQUM5Q0MsT0FBTTt3Q0FDTkUsYUFBWTs7Ozs7O2tEQUVkLDhEQUFDelQsb0VBQVNBO3dDQUNSZ1IsTUFBSzt3Q0FDTC9MLFVBQVVBO3dDQUNWc1EsU0FBU3hWLDZDQUFNQSxHQUFHbUosTUFBTSxDQUFDO3dDQUN6QnFDLE1BQUs7d0NBQ0w2SCxPQUFNO3dDQUNOaE8sVUFBVTZOLENBQUFBLElBQUs3TixTQUFTLGtCQUFrQjZOLEdBQUc7Z0RBQUVoRCxnQkFBZ0I7NENBQUs7d0NBQ3BFOUUsT0FBTyxDQUFDLENBQUM1RixPQUFPOUIsY0FBYzt3Q0FDOUI0UCxZQUFZLEVBQUU5TixtQkFBQUEsOEJBQUFBLHlCQUFBQSxPQUFROUIsY0FBYyxjQUF0QjhCLDZDQUFBQSx1QkFBd0IrTixPQUFPO3dDQUM3Q0MsT0FBTTs7Ozs7O2tEQUVSLDhEQUFDcEo7OzBEQUNDLDhEQUFDb0o7Z0RBQU1uSixXQUFVOzBEQUF3Qjs7Ozs7OzBEQUN6Qyw4REFBQ25LLGdFQUFZQTtnREFDWGdCLE9BQU9rRSxNQUFNO2dEQUNiNk4sVUFBVUMsQ0FBQUEsSUFBSzdOLFNBQVMsVUFBVTZOLEVBQUVDLE1BQU0sQ0FBQ2pTLEtBQUssRUFBRTt3REFBRWdQLGdCQUFnQjtvREFBSzs7a0VBRXpFLDhEQUFDa0Q7d0RBQU9sUyxPQUFNO2tFQUFlOzs7Ozs7a0VBQzdCLDhEQUFDa1M7d0RBQU9sUyxPQUFNO2tFQUFXOzs7Ozs7a0VBQ3pCLDhEQUFDa1M7d0RBQU9sUyxPQUFNO2tFQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS2pDLDhEQUFDa0o7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDcEssb0VBQVNBO3dDQUNSaUYsVUFBVUE7d0NBQ1YrTCxNQUFLO3dDQUNMekYsTUFBSzt3Q0FDTDZILE9BQU07d0NBQ05qSSxPQUFPLENBQUMsQ0FBQzVGLE9BQU9qQyxpQkFBaUI7d0NBQ2pDK1AsWUFBWSxFQUFFOU4sbUJBQUFBLDhCQUFBQSw0QkFBQUEsT0FBUWpDLGlCQUFpQixjQUF6QmlDLGdEQUFBQSwwQkFBMkIrTixPQUFPO3dDQUNoREMsT0FBTTt3Q0FDTkUsYUFBWTs7Ozs7O2tEQUVkLDhEQUFDdEo7OzBEQUNDLDhEQUFDb0o7Z0RBQU1uSixXQUFVOzBEQUF3Qjs7Ozs7OzBEQUN6Qyw4REFBQ25LLGdFQUFZQTtnREFDWGdCLE9BQU9rRSxNQUFNO2dEQUNiNk4sVUFBVUMsQ0FBQUEsSUFBSzdOLFNBQVMsY0FBYzZOLEVBQUVDLE1BQU0sQ0FBQ2pTLEtBQUssRUFBRTt3REFBRWdQLGdCQUFnQjtvREFBSzs7a0VBRTdFLDhEQUFDa0Q7d0RBQU9sUyxPQUFNO2tFQUFNOzs7Ozs7a0VBQ3BCLDhEQUFDa1M7d0RBQU9sUyxPQUFNO2tFQUFTOzs7Ozs7a0VBQ3ZCLDhEQUFDa1M7d0RBQU9sUyxPQUFNO2tFQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUdsQyw4REFBQ2pCLG9FQUFTQTt3Q0FDUmdSLE1BQUs7d0NBQ0wvTCxVQUFVQTt3Q0FDVnNHLE1BQUs7d0NBQ0x0SyxPQUFPa0wsVUFBVXBNLDZDQUFNQSxDQUFDb00sU0FBUyxjQUFjakQsTUFBTSxDQUFDLGdCQUFnQjt3Q0FDdEVrSyxPQUFNO3dDQUNOa0MsVUFBVTt3Q0FDVi9CLE9BQU07Ozs7OztrREFFUiw4REFBQ3BKOzswREFDQyw4REFBQ29KO2dEQUFNbkosV0FBVTswREFBd0I7Ozs7OzswREFDekMsOERBQUNuSyxnRUFBWUE7Z0RBQ1hnQixPQUFPa0UsTUFBTTtnREFDYjZOLFVBQVVDLENBQUFBLElBQUs3TixTQUFTLGFBQWE2TixFQUFFQyxNQUFNLENBQUNqUyxLQUFLLEVBQUU7d0RBQUVnUCxnQkFBZ0I7b0RBQUs7O2tFQUU1RSw4REFBQ2tEO3dEQUFPbFMsT0FBTTtrRUFBSTs7Ozs7O2tFQUNsQiw4REFBQ2tTO3dEQUFPbFMsT0FBTTtrRUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTTVCLDhEQUFDa0o7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDaEwseURBQU1BOzRCQUNMNFIsTUFBSzs0QkFDTHdFLFNBQVE7NEJBQ1I1QixNQUFLOzRCQUNMdEosU0FBUyxJQUFNeEUsUUFBUTs0QkFDdkJzRSxXQUFVO3NDQUNYOzs7Ozs7c0NBR0QsOERBQUNoTCx5REFBTUE7NEJBQ0x3VSxNQUFLOzRCQUNMNUMsTUFBSzs0QkFDTDVHLFdBQVcsd0JBSVYsT0FIQ2IscUJBQ0ksK0NBQ0E7NEJBRU5lLFNBQVM7Z0NBQ1BuRCxRQUFRQyxHQUFHLENBQUM7Z0NBQ1pELFFBQVFDLEdBQUcsQ0FBQyw0QkFBeUI7b0NBQUU1QjtvQ0FBU0Q7Z0NBQU87Z0NBQ3ZENEIsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQmpDO2dDQUM3QmdDLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUJtQzs0QkFDbkM7NEJBQ0ErTCxVQUNFLENBQUMvTCxzQkFDRDlELGdCQUNBZ0osdUJBQXVCZ0gsU0FBUyxJQUNoQ3hQLGlCQUNDLENBQUNkLE1BQU0sa0JBQWtCLFdBQVc4RixDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQkMsWUFBWSxNQUFLLE9BQU0sS0FBTyxFQUFDM0UsZUFBZ0IsQ0FBQ3BCLE1BQU0saUJBQWlCLENBQUNBLE1BQU0sYUFBYTtzQ0FHbEosQ0FBQ29FLHFCQUFxQixxQ0FDdEJ0RCxnQkFBZ0Isc0JBQ2hCUixnQkFBZ0JnSix1QkFBdUJnSCxTQUFTLEdBQUcsZ0JBQ25ELENBQUV0USxNQUFNLGtCQUFrQixXQUFXOEYsQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJDLFlBQVksTUFBSyxPQUFNLEtBQU0sQ0FBQzNFLGNBQWUsMEJBQ25HLENBQUVwQixNQUFNLGtCQUFrQixXQUFXOEYsQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUJDLFlBQVksTUFBSyxPQUFNLEtBQU8sQ0FBQy9GLE1BQU0saUJBQWlCLENBQUNBLE1BQU0sZ0JBQWtCLG9DQUNySTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU1ULDRDQUE0QztJQUM1QyxJQUFJUCxtQkFBbUI7UUFDckIscUJBQ0U7OzhCQUNFLDhEQUFDcEYsMERBQU1BOzs7Ozs4QkFDUCw4REFBQ0MsMkRBQU9BOzhCQUNOLDRFQUFDMEs7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNHO3dDQUFFSCxXQUFVO2tEQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU94QztJQUVBLHFCQUNFOzswQkFDRSw4REFBQzVLLDBEQUFNQTs7Ozs7MEJBQ1AsOERBQUNDLDJEQUFPQTswQkFDTiw0RUFBQzBLO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDc0w7b0NBQUd0TCxXQUFVOzt3Q0FBbUQ7d0NBRTlEakcsOEJBQ0MsOERBQUN3UDs0Q0FBS3ZKLFdBQVU7dURBQ2JqRywwQkFBQUEsYUFBYW1ELFNBQVMsY0FBdEJuRCwrQ0FBQUEsMkJBQUFBLHVCQUF3QixDQUFDLEVBQUUsY0FBM0JBLCtDQUFBQSx5QkFBNkJzRCxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFNakQsQ0FBQ25ELFFBQVFpRixxQkFDUlcsMkJBQ0UsQ0FBQzVGLFFBQVEsQ0FBQ2lGLHFCQUNaLGdEQUFnRCxpQkFDaEQsOERBQUNZO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQytKO3dDQUFHL0osV0FBVTtrREFBNEI7Ozs7OztrREFDMUMsOERBQUNHO3dDQUFFSCxXQUFVO2tEQUFxQjs7Ozs7Ozs7Ozs7dUNBTXBDLGtEQUFrRCxHQUNsRHZFLFNBQVMsSUFBSWtOLGdCQUFnQmM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPM0M7R0F6bUR3QjNQOztRQUNQNUUsc0RBQVNBO1FBQ0hDLDREQUFlQTtRQU15Qm9CLDREQUFRQTtRQXFCakVMLHFEQUFPQTtRQXFrQm9CSSwrREFBV0E7OztLQWxtQnBCd0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9jb250cmF0b3MvYWx0ZXJhci9wYWdlLnRzeD8zZTE1Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tIFwicmVhY3QtdG9hc3RpZnlcIjtcclxuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XHJcbmltcG9ydCBIZWFkZXIgZnJvbSBcIkAvY29tcG9uZW50cy9IZWFkZXJcIjtcclxuaW1wb3J0IFNpZGViYXIgZnJvbSBcIkAvY29tcG9uZW50cy9TaWRlYmFyXCI7XHJcbmltcG9ydCBDb3ZlckZvcm0gZnJvbSBcIkAvY29tcG9uZW50cy9Db3ZlckZvcm1cIjtcclxuaW1wb3J0IHtcclxuICBjZXBNYXNrLFxyXG4gIGNsZWFyTGV0dGVycyxcclxuICBjcGZNYXNrLFxyXG4gIHBob25lTWFzayxcclxuICB2YWx1ZU1hc2ssXHJcbiAgY25wak1hc2ssXHJcbn0gZnJvbSBcIkAvdXRpbHMvbWFza3NcIjtcclxuaW1wb3J0IG1vbWVudCBmcm9tIFwibW9tZW50XCI7XHJcbmltcG9ydCBJbnB1dFRleHQgZnJvbSBcIkAvY29tcG9uZW50cy9JbnB1dHMvSW5wdXRUZXh0XCI7XHJcbmltcG9ydCBTZWxlY3RDdXN0b20gZnJvbSBcIkAvY29tcG9uZW50cy9TZWxlY3RDdXN0b21cIjtcclxuaW1wb3J0IFNlbGVjdFNlYXJjaCBmcm9tIFwiQC9jb21wb25lbnRzL1NlbGVjdFNlYXJjaFwiO1xyXG5cclxuaW1wb3J0IHsgaXNWYWxpZCB9IGZyb20gXCJkYXRlLWZuc1wiO1xyXG5pbXBvcnQgeyBUcmFzaEljb24sIFBsdXNJY29uIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5cclxuaW1wb3J0IERyb3B6b25lIGZyb20gXCJyZWFjdC1kcm9wem9uZVwiO1xyXG5pbXBvcnQgeyB1c2VNZW1vLCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyB1c2VGb3JtIH0gZnJvbSBcInJlYWN0LWhvb2stZm9ybVwiO1xyXG5pbXBvcnQgeyB5dXBSZXNvbHZlciB9IGZyb20gJ0Bob29rZm9ybS9yZXNvbHZlcnMveXVwJztcclxuaW1wb3J0ICogYXMgeXVwIGZyb20gJ3l1cCc7XHJcbi8vIFNjaGVtYSBpbXBvcnRzIHJlbW92ZWQgc2luY2UgdXNpbmcgY3VzdG9tIGZpZWxkIG5hbWVzXHJcbmltcG9ydCB7IGdldEZpbmFsRGF0YVdpdGhNb3VudCB9IGZyb20gXCJAL2Z1bmN0aW9ucy9nZXREYXRhRmlsdGVyXCI7XHJcbmltcG9ydCB7IHVzZU11dGF0aW9uLCB1c2VRdWVyeSB9IGZyb20gXCJAdGFuc3RhY2svcmVhY3QtcXVlcnlcIjtcclxuaW1wb3J0IHJldHVybkVycm9yIGZyb20gXCJAL2Z1bmN0aW9ucy9yZXR1cm5FcnJvclwiO1xyXG5pbXBvcnQgYXBpIGZyb20gXCJAL2NvcmUvYXBpXCI7XHJcbmltcG9ydCB7IGdldFVzZXJQcm9maWxlIH0gZnJvbSBcIkAvZnVuY3Rpb25zL2dldFVzZXJEYXRhXCI7XHJcbmltcG9ydCBmb3JtYXROdW1iZXJWYWx1ZSBmcm9tIFwiQC91dGlscy9mb3JtYXROdW1iZXJWYWx1ZVwiO1xyXG5cclxuLy8gRnVuw6fDo28gcGFyYSBtw6FzY2FyYSBkZSB0ZWxlZm9uZSBxdWUgcHJlc2VydmEgbyBjw7NkaWdvIGRvIHBhw61zXHJcbmNvbnN0IHBob25lV2l0aENvdW50cnlNYXNrID0gKHZhbHVlOiBzdHJpbmcpID0+IHtcclxuICBjb25zdCBjbGVhbmVkID0gdmFsdWUucmVwbGFjZSgvXFxEL2csICcnKTtcclxuXHJcbiAgLy8gU2UgY29tZcOnYXIgY29tIDU1IGUgdGl2ZXIgMTMgZMOtZ2l0b3MgKDU1ICsgREREICsgOSBkw61naXRvcylcclxuICBpZiAoY2xlYW5lZC5zdGFydHNXaXRoKCc1NScpICYmIGNsZWFuZWQubGVuZ3RoID09PSAxMykge1xyXG4gICAgcmV0dXJuIGNsZWFuZWRcclxuICAgICAgLnJlcGxhY2UoL14oXFxkezJ9KShcXGR7Mn0pKFxcZHs1fSkoXFxkezR9KSQvLCAnJDEgKCQyKSAkMy0kNCcpO1xyXG4gIH1cclxuXHJcbiAgLy8gU2UgbsOjbyBjb21lw6dhciBjb20gNTUgZSB0aXZlciAxMSBkw61naXRvcyAoREREICsgOSBkw61naXRvcyksIGFkaWNpb25hciA1NVxyXG4gIGlmICghY2xlYW5lZC5zdGFydHNXaXRoKCc1NScpICYmIGNsZWFuZWQubGVuZ3RoID09PSAxMSkge1xyXG4gICAgY29uc3Qgd2l0aENvdW50cnlDb2RlID0gJzU1JyArIGNsZWFuZWQ7XHJcbiAgICByZXR1cm4gd2l0aENvdW50cnlDb2RlXHJcbiAgICAgIC5yZXBsYWNlKC9eKFxcZHsyfSkoXFxkezJ9KShcXGR7NX0pKFxcZHs0fSkkLywgJyQxICgkMikgJDMtJDQnKTtcclxuICB9XHJcblxyXG4gIC8vIFBhcmEgbsO6bWVyb3MgZW0gY29uc3RydcOnw6NvLCBhcGxpY2FyIG3DoXNjYXJhIHByb2dyZXNzaXZhXHJcbiAgaWYgKGNsZWFuZWQubGVuZ3RoIDw9IDEzKSB7XHJcbiAgICBpZiAoY2xlYW5lZC5zdGFydHNXaXRoKCc1NScpKSB7XHJcbiAgICAgIC8vIErDoSB0ZW0gY8OzZGlnbyBkbyBwYcOtc1xyXG4gICAgICBpZiAoY2xlYW5lZC5sZW5ndGggPD0gNCkge1xyXG4gICAgICAgIHJldHVybiBjbGVhbmVkLnJlcGxhY2UoL14oXFxkezJ9KShcXGR7MCwyfSkkLywgJyQxICgkMicpO1xyXG4gICAgICB9IGVsc2UgaWYgKGNsZWFuZWQubGVuZ3RoIDw9IDkpIHtcclxuICAgICAgICByZXR1cm4gY2xlYW5lZC5yZXBsYWNlKC9eKFxcZHsyfSkoXFxkezJ9KShcXGR7MCw1fSkkLywgJyQxICgkMikgJDMnKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICByZXR1cm4gY2xlYW5lZC5yZXBsYWNlKC9eKFxcZHsyfSkoXFxkezJ9KShcXGR7NX0pKFxcZHswLDR9KSQvLCAnJDEgKCQyKSAkMy0kNCcpO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAvLyBOw6NvIHRlbSBjw7NkaWdvIGRvIHBhw61zLCB1c2FyIG3DoXNjYXJhIG5vcm1hbFxyXG4gICAgICByZXR1cm4gcGhvbmVNYXNrKHZhbHVlKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHJldHVybiB2YWx1ZTtcclxufTtcclxuXHJcbi8vIFNjaGVtYSBkZSB2YWxpZGHDp8Ojb1xyXG5jb25zdCB2YWxpZGF0aW9uU2NoZW1hID0geXVwLm9iamVjdCgpLnNoYXBlKHtcclxuICB0aXBvQ29udHJhdG86IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnU2VsZWNpb25lIG8gdGlwbyBkZSBjb250cmF0bycpLFxyXG4gIG5vbWVDb21wbGV0bzogeXVwLnN0cmluZygpLnJlcXVpcmVkKCdOb21lIGNvbXBsZXRvIG9icmlnYXTDs3JpbycpLFxyXG4gIGlkZW50aWRhZGU6IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnSWRlbnRpZGFkZSBvYnJpZ2F0w7NyaWEnKSxcclxuICBjZWx1bGFyOiB5dXAuc3RyaW5nKCkucmVxdWlyZWQoJ0NlbHVsYXIgb2JyaWdhdMOzcmlvJyksXHJcbiAgY3BmOiB5dXAuc3RyaW5nKCkucmVxdWlyZWQoJ0NQRi9DTlBKIG9icmlnYXTDs3JpbycpLFxyXG4gIGRhdGFOYXNjaW1lbnRvOiB5dXAuc3RyaW5nKCkub3B0aW9uYWwoKSxcclxuICBub21lTWFlOiB5dXAuc3RyaW5nKCkub3B0aW9uYWwoKSxcclxuICBlbWFpbDogeXVwLnN0cmluZygpLmVtYWlsKCdFLW1haWwgaW52w6FsaWRvJykucmVxdWlyZWQoJ0UtbWFpbCBvYnJpZ2F0w7NyaW8nKSxcclxuICBjZXA6IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnQ0VQIG9icmlnYXTDs3JpbycpLFxyXG4gIGNpZGFkZTogeXVwLnN0cmluZygpLnJlcXVpcmVkKCdDaWRhZGUgb2JyaWdhdMOzcmlhJyksXHJcbiAgZW5kZXJlY286IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnRW5kZXJlw6dvIG9icmlnYXTDs3JpbycpLFxyXG4gIG51bWVybzogeXVwLnN0cmluZygpLnJlcXVpcmVkKCdOw7ptZXJvIG9icmlnYXTDs3JpbycpLFxyXG4gIGNvbXBsZW1lbnRvOiB5dXAuc3RyaW5nKCkub3B0aW9uYWwoKSxcclxuICBlc3RhZG86IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnRXN0YWRvIG9icmlnYXTDs3JpbycpLFxyXG4gIGJhbmNvOiB5dXAuc3RyaW5nKCkucmVxdWlyZWQoJ0JhbmNvIG9icmlnYXTDs3JpbycpLFxyXG4gIGNvbnRhOiB5dXAuc3RyaW5nKCkucmVxdWlyZWQoJ0NvbnRhIG9icmlnYXTDs3JpYScpLFxyXG4gIGFnZW5jaWE6IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnQWfDqm5jaWEgb2JyaWdhdMOzcmlhJyksXHJcbiAgY2hhdmVQaXg6IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnQ2hhdmUgUElYIG9icmlnYXTDs3JpYScpLFxyXG4gIG1vZGFsaWRhZGU6IHl1cC5zdHJpbmcoKVxyXG4gICAgLnJlcXVpcmVkKCdNb2RhbGlkYWRlIG9icmlnYXTDs3JpYScpXHJcbiAgICAudGVzdCgnc2NwLXRvLW11dHVvLXZhbGlkYXRpb24nLCAnTsOjbyDDqSBwb3Nzw612ZWwgYWx0ZXJhciBjb250cmF0b3MgU0NQIHBhcmEgbW9kYWxpZGFkZSBNw7p0dW8nLCBmdW5jdGlvbih2YWx1ZSkge1xyXG4gICAgICAvLyBFc3RhIHZhbGlkYcOnw6NvIHNlcsOhIGFwbGljYWRhIGRpbmFtaWNhbWVudGUgbm8gY29udGV4dG8gZG8gY29tcG9uZW50ZVxyXG4gICAgICByZXR1cm4gdHJ1ZTsgLy8gQSB2YWxpZGHDp8OjbyByZWFsIMOpIGZlaXRhIG5vIG9uQ2hhbmdlIGUgb25TdWJtaXRcclxuICAgIH0pLFxyXG4gIHZhbG9ySW52ZXN0aW1lbnRvOiB5dXAuc3RyaW5nKClcclxuICAgIC5yZXF1aXJlZCgnVmFsb3IgZG8gaW52ZXN0aW1lbnRvIG9icmlnYXTDs3JpbycpXHJcbiAgICAudGVzdCgnc2NwLW1pbmltdW0nLCAnUGFyYSBjb250cmF0b3MgU0NQLCBvIHZhbG9yIG3DrW5pbW8gw6kgZGUgUiQgMzAuMDAwJywgZnVuY3Rpb24odmFsdWUpIHtcclxuICAgICAgY29uc3QgbW9kYWxpZGFkZSA9IHRoaXMucGFyZW50Lm1vZGFsaWRhZGU7XHJcbiAgICAgIGlmIChtb2RhbGlkYWRlID09PSAnU0NQJyAmJiB2YWx1ZSkge1xyXG4gICAgICAgIC8vIFVzYXIgZm9ybWF0TnVtYmVyVmFsdWUgcGFyYSBjb252ZXJ0ZXIgY29ycmV0YW1lbnRlIHZhbG9yZXMgY29tIG3DoXNjYXJhIGJyYXNpbGVpcmFcclxuICAgICAgICBjb25zdCBudW1lcmljVmFsdWUgPSBOdW1iZXIodmFsdWUucmVwbGFjZUFsbCgnLicsICcnKS5yZXBsYWNlQWxsKCcsJywgJy4nKSkgfHwgMDtcclxuICAgICAgICByZXR1cm4gbnVtZXJpY1ZhbHVlID49IDMwMDAwO1xyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfSksXHJcbiAgcHJhem9JbnZlc3RpbWVudG86IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnUHJhem8gZG8gaW52ZXN0aW1lbnRvIG9icmlnYXTDs3JpbycpLFxyXG4gIHRheGFSZW11bmVyYWNhbzogeXVwLnN0cmluZygpLnJlcXVpcmVkKCdUYXhhIGRlIHJlbXVuZXJhw6fDo28gb2JyaWdhdMOzcmlhJyksXHJcbiAgY29tcHJhckNvbTogeXVwLnN0cmluZygpLnJlcXVpcmVkKCdGb3JtYSBkZSBjb21wcmEgb2JyaWdhdMOzcmlhJyksXHJcbiAgaW5pY2lvQ29udHJhdG86IHl1cC5zdHJpbmcoKS5yZXF1aXJlZCgnSW7DrWNpbyBkbyBjb250cmF0byBvYnJpZ2F0w7NyaW8nKSxcclxuICBmaW1Db250cmF0bzogeXVwLnN0cmluZygpLFxyXG4gIHBlcmZpbDogeXVwLnN0cmluZygpLnJlcXVpcmVkKCdQZXJmaWwgb2JyaWdhdMOzcmlvJyksXHJcbiAgZGViZW50dXJlOiB5dXAuc3RyaW5nKCkucmVxdWlyZWQoJ0RlYsOqbnR1cmUgb2JyaWdhdMOzcmlhJyksXHJcbiAgb2JzZXJ2YWNvZXM6IHl1cC5zdHJpbmcoKS5vcHRpb25hbCgpLFxyXG4gIHF1b3RhUXVhbnRpdHk6IHl1cC5zdHJpbmcoKS5vcHRpb25hbCgpLCAvLyBDYWxjdWxhZG8gYXV0b21hdGljYW1lbnRlIHBhcmEgU0NQXHJcbiAgLy8gQ2FtcG9zIGRlIElSIChvYnJpZ2F0w7NyaW9zIHBhcmEgTVVUVU8pXHJcbiAgaXJEZXBvc2l0bzogeXVwLmJvb2xlYW4oKS5vcHRpb25hbCgpLFxyXG4gIGlyRGVzY29udG86IHl1cC5ib29sZWFuKCkub3B0aW9uYWwoKSxcclxufSk7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBbHRlcmFyQ29udHJhdG8oKSB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKCk7XHJcbiAgY29uc3QgdGlwbyA9IHNlYXJjaFBhcmFtcy5nZXQoXCJ0aXBvXCIpOyAvLyBcInJlbnRhYmlsaWRhZGVcIiBvdSBcIm1vZGFsaWRhZGVcIlxyXG4gIGNvbnN0IGludmVzdG9ySWQgPSBzZWFyY2hQYXJhbXMuZ2V0KFwiaW52ZXN0b3JJZFwiKTtcclxuICBjb25zdCB1c2VyUHJvZmlsZSA9IGdldFVzZXJQcm9maWxlKCk7XHJcblxyXG4gIC8vIEJ1c2NhciBkYWRvcyBkbyBjb250cmF0byBhdHVhbFxyXG4gIGNvbnN0IHsgZGF0YTogY29udHJhY3REYXRhLCBpc0xvYWRpbmc6IGlzTG9hZGluZ0NvbnRyYWN0IH0gPSB1c2VRdWVyeSh7XHJcbiAgICBxdWVyeUtleTogW1wiY29udHJhY3RcIiwgaW52ZXN0b3JJZF0sXHJcbiAgICBxdWVyeUZuOiBhc3luYyAoKSA9PiB7XHJcbiAgICAgIGlmICghaW52ZXN0b3JJZCkgcmV0dXJuIG51bGw7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmdldChgL2NvbnRyYWN0LyR7aW52ZXN0b3JJZH1gKTtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9LFxyXG4gICAgZW5hYmxlZDogISFpbnZlc3RvcklkLFxyXG4gIH0pO1xyXG5cclxuXHJcblxyXG4gIC8vIEZvcm0gaXMgbWFuYWdlZCBieSByZWFjdC1ob29rLWZvcm0gd2l0aCB5dXAgcmVzb2x2ZXJcclxuXHJcbiAgY29uc3Qge1xyXG4gICAgcmVnaXN0ZXIsXHJcbiAgICBoYW5kbGVTdWJtaXQsXHJcbiAgICB3YXRjaCxcclxuICAgIHNldFZhbHVlLFxyXG4gICAgdHJpZ2dlcixcclxuICAgIGZvcm1TdGF0ZTogeyBlcnJvcnMsIGlzVmFsaWQsIGlzU3VibWl0dGluZyB9LFxyXG4gIH0gPSB1c2VGb3JtKHtcclxuICAgIHJlc29sdmVyOiB5dXBSZXNvbHZlcih2YWxpZGF0aW9uU2NoZW1hKSxcclxuICAgIG1vZGU6IFwiYWxsXCIsXHJcbiAgICBkZWZhdWx0VmFsdWVzOiB7XHJcbiAgICAgIC8vUMOBR0lOQSAxXHJcbiAgICAgIHRpcG9Db250cmF0bzogXCJwZlwiLFxyXG4gICAgICBub21lQ29tcGxldG86IFwiXCIsXHJcbiAgICAgIGlkZW50aWRhZGU6IFwiXCIsXHJcbiAgICAgIGNlbHVsYXI6IFwiXCIsXHJcbiAgICAgIGNwZjogXCJcIixcclxuICAgICAgZGF0YU5hc2NpbWVudG86IFwiXCIsXHJcbiAgICAgIG5vbWVNYWU6IFwiXCIsXHJcbiAgICAgIGVtYWlsOiBcIlwiLFxyXG4gICAgICBjZXA6IFwiXCIsXHJcbiAgICAgIGNpZGFkZTogXCJcIixcclxuICAgICAgZW5kZXJlY286IFwiXCIsXHJcbiAgICAgIG51bWVybzogXCJcIixcclxuICAgICAgY29tcGxlbWVudG86IFwiXCIsXHJcbiAgICAgIGVzdGFkbzogXCJcIixcclxuICAgICAgYmFuY286IFwiXCIsXHJcbiAgICAgIGNvbnRhOiBcIlwiLFxyXG4gICAgICBhZ2VuY2lhOiBcIlwiLFxyXG4gICAgICBjaGF2ZVBpeDogXCJcIixcclxuICAgICAgb2JzZXJ2YWNvZXM6IFwiXCIsXHJcbiAgICAgIC8vUMOBR0lOQSAyXHJcbiAgICAgIG1vZGFsaWRhZGU6IFwiTVVUVU9cIixcclxuICAgICAgdmFsb3JJbnZlc3RpbWVudG86IFwiXCIsXHJcbiAgICAgIHByYXpvSW52ZXN0aW1lbnRvOiBcIlwiLFxyXG4gICAgICB0YXhhUmVtdW5lcmFjYW86IFwiXCIsXHJcbiAgICAgIGNvbXByYXJDb206IFwicGl4XCIsXHJcbiAgICAgIGluaWNpb0NvbnRyYXRvOiBcIlwiLFxyXG4gICAgICBmaW1Db250cmF0bzogXCJcIixcclxuICAgICAgcGVyZmlsOiBcImNvbnNlcnZhdGl2ZVwiLFxyXG4gICAgICBkZWJlbnR1cmU6IFwiblwiLFxyXG4gICAgICBxdW90YVF1YW50aXR5OiBcIlwiLFxyXG4gICAgICAvLyBPcMOnw7VlcyBkZSBJUiAobXV0dWFtZW50ZSBleGNsdXNpdmFzKVxyXG4gICAgICBpckRlcG9zaXRvOiBmYWxzZSxcclxuICAgICAgaXJEZXNjb250bzogZmFsc2UsXHJcbiAgICB9LFxyXG4gIH0pO1xyXG5cclxuXHJcblxyXG4gIGNvbnN0IFtzdGVwLCBzZXRTdGVwXSA9IHVzZVN0YXRlKDEpOyAvLyAxID0gcGFydGUgMSwgMiA9IHBhcnRlIDJcclxuICBjb25zdCBbYWxpcXVvdGFJUiwgc2V0QWxpcXVvdGFJUl0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xyXG4gIGNvbnN0IFtpc1JlZGlyZWN0aW5nLCBzZXRJc1JlZGlyZWN0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbdmFsb3JDb21wbGVtZW50YXIsIHNldFZhbG9yQ29tcGxlbWVudGFyXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XHJcbiAgY29uc3QgW3Nob3dDb21wbGVtZW50TWVzc2FnZSwgc2V0U2hvd0NvbXBsZW1lbnRNZXNzYWdlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXJDYWxjdWxhZG8sIHNldElyQ2FsY3VsYWRvXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8gQ29udHJvbGEgc2UgbyBJUiBmb2kgY2FsY3VsYWRvXHJcbiAgY29uc3QgW2lyT3BjYW9TZWxlY2lvbmFkYSwgc2V0SXJPcGNhb1NlbGVjaW9uYWRhXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8gQ29udHJvbGEgc2UgdW1hIG9ww6fDo28gZGUgSVIgZm9pIHNlbGVjaW9uYWRhXHJcbiAgY29uc3QgW3ZhbG9yT3JpZ2luYWxJbnZlc3RpbWVudG8sIHNldFZhbG9yT3JpZ2luYWxJbnZlc3RpbWVudG9dID0gdXNlU3RhdGU8c3RyaW5nPihcIlwiKTsgLy8gQXJtYXplbmEgdmFsb3Igb3JpZ2luYWwgYW50ZXMgZG8gZGVzY29udG9cclxuICBjb25zdCBbc2NwVmFsaWRhdGlvbkVycm9yLCBzZXRTY3BWYWxpZGF0aW9uRXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nPihcIlwiKTsgLy8gRXJybyBkZSB2YWxpZGHDp8OjbyBTQ1AgY3VzdG9taXphZG9cclxuXHJcbiAgLy8gRnVuw6fDo28gcGFyYSB2YWxpZGFyIHZhbG9yIFNDUFxyXG4gIGNvbnN0IHZhbGlkYXRlU2NwVmFsdWUgPSB1c2VDYWxsYmFjaygodmFsb3I6IHN0cmluZywgbW9kYWxpZGFkZTogc3RyaW5nKSA9PiB7XHJcbiAgICBpZiAobW9kYWxpZGFkZSAhPT0gXCJTQ1BcIikge1xyXG4gICAgICBzZXRTY3BWYWxpZGF0aW9uRXJyb3IoXCJcIik7XHJcbiAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFNlIG8gdmFsb3IgZXN0aXZlciB2YXppbywgbGltcGFyIGVycm8gZSByZXRvcm5hciB0cnVlIChzZXLDoSB2YWxpZGFkbyBwZWxvIFl1cClcclxuICAgIGlmICghdmFsb3IgfHwgdmFsb3IudHJpbSgpID09PSBcIlwiKSB7XHJcbiAgICAgIHNldFNjcFZhbGlkYXRpb25FcnJvcihcIlwiKTtcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgdmFsb3JOdW1lcmljbyA9IE51bWJlcih2YWxvci5yZXBsYWNlQWxsKCcuJywgJycpLnJlcGxhY2VBbGwoJywnLCAnLicpKSB8fCAwO1xyXG5cclxuICAgIC8vIFNlIGjDoSBtZW5zYWdlbSBkZSBjb21wbGVtZW50bywgdmFsb3IgcGFyY2lhbCDDqSBwZXJtaXRpZG9cclxuICAgIGNvbnNvbGUubG9nKFwi8J+UjSBWYWxpZGHDp8OjbyBTQ1AgLSBzaG93Q29tcGxlbWVudE1lc3NhZ2U6XCIsIHNob3dDb21wbGVtZW50TWVzc2FnZSk7XHJcbiAgICBjb25zb2xlLmxvZyhcIvCflI0gVmFsaWRhw6fDo28gU0NQIC0gdmFsb3I6XCIsIHZhbG9yKTtcclxuICAgIGNvbnNvbGUubG9nKFwi8J+UjSBWYWxpZGHDp8OjbyBTQ1AgLSB2YWxvck51bWVyaWNvOlwiLCB2YWxvck51bWVyaWNvKTtcclxuXHJcbiAgICBpZiAoc2hvd0NvbXBsZW1lbnRNZXNzYWdlKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwi4pyFIFNDUDogVmFsb3IgcGFyY2lhbCBwZXJtaXRpZG8gZGV2aWRvIGFvIGRlcMOzc2l0byBhZGljaW9uYWxcIik7XHJcbiAgICAgIHNldFNjcFZhbGlkYXRpb25FcnJvcihcIlwiKTtcclxuICAgICAgcmV0dXJuIHRydWU7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVmFsaWRhw6fDo28gbm9ybWFsOiBtw7psdGlwbG8gZGUgNS4wMDAgZSBtw61uaW1vIGRlIDMwLjAwMFxyXG4gICAgaWYgKHZhbG9yTnVtZXJpY28gPCAzMDAwMCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIuKdjCBTQ1A6IFZhbG9yIG1lbm9yIHF1ZSBSJCAzMC4wMDBcIik7XHJcbiAgICAgIHNldFNjcFZhbGlkYXRpb25FcnJvcihcIlBhcmEgY29udHJhdG9zIFNDUCwgbyB2YWxvciBtw61uaW1vIMOpIGRlIFIkIDMwLjAwMFwiKTtcclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICh2YWxvck51bWVyaWNvICUgNTAwMCAhPT0gMCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIuKdjCBTQ1A6IFZhbG9yIG7Do28gw6kgbcO6bHRpcGxvIGRlIFIkIDUuMDAwXCIpO1xyXG4gICAgICBzZXRTY3BWYWxpZGF0aW9uRXJyb3IoXCJQYXJhIGNvbnRyYXRvcyBTQ1AsIG8gdmFsb3IgZGV2ZSBzZXIgbcO6bHRpcGxvIGRlIFIkIDUuMDAwXCIpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coXCLinIUgU0NQOiBWYWxvciB2w6FsaWRvXCIpO1xyXG4gICAgc2V0U2NwVmFsaWRhdGlvbkVycm9yKFwiXCIpO1xyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfSwgW3Nob3dDb21wbGVtZW50TWVzc2FnZV0pO1xyXG5cclxuICAvLyBJZGVudGlmaWNhciBtb2RhbGlkYWRlIGF0dWFsIGRvIGNvbnRyYXRvXHJcbiAgY29uc3QgY3VycmVudENvbnRyYWN0TW9kYWxpdHkgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIGlmICghY29udHJhY3REYXRhIHx8ICFjb250cmFjdERhdGEuY29udHJhY3RzPy5bMF0pIHJldHVybiBudWxsO1xyXG4gICAgY29uc3QgY29udHJhY3QgPSBjb250cmFjdERhdGEuY29udHJhY3RzWzBdO1xyXG4gICAgcmV0dXJuIGNvbnRyYWN0LnRhZ3MgPT09IFwiUDJQXCIgPyBcIk1VVFVPXCIgOiBcIlNDUFwiO1xyXG4gIH0sIFtjb250cmFjdERhdGFdKTtcclxuXHJcbiAgLy8gUHLDqS1wcmVlbmNoZXIgZm9ybXVsw6FyaW8gcXVhbmRvIG9zIGRhZG9zIGNoZWdhcmVtXHJcbiAgdXNlTWVtbygoKSA9PiB7XHJcbiAgICBpZiAoY29udHJhY3REYXRhICYmICFpc0xvYWRpbmdDb250cmFjdCkge1xyXG4gICAgICBjb25zdCBjb250cmFjdCA9IGNvbnRyYWN0RGF0YS5jb250cmFjdHM/LlswXTsgLy8gUGVnYSBvIHByaW1laXJvIGNvbnRyYXRvXHJcblxyXG4gICAgICBpZiAoY29udHJhY3QpIHtcclxuICAgICAgICAvLyBEYWRvcyBwZXNzb2Fpc1xyXG4gICAgICAgIHNldFZhbHVlKFwibm9tZUNvbXBsZXRvXCIsIGNvbnRyYWN0LmludmVzdG9yTmFtZSB8fCBcIlwiKTtcclxuXHJcbiAgICAgICAgLy8gQXBsaWNhciBtw6FzY2FyYSBubyBkb2N1bWVudG8gKENQRi9DTlBKKVxyXG4gICAgICAgIGNvbnN0IGRvY3VtZW50byA9IGNvbnRyYWN0RGF0YS5kb2N1bWVudCB8fCBcIlwiO1xyXG4gICAgICAgIGlmIChkb2N1bWVudG8pIHtcclxuICAgICAgICAgIGNvbnN0IGRvY3VtZW50b0NvbU1hc2NhcmEgPSBkb2N1bWVudG8ubGVuZ3RoIDw9IDExID8gY3BmTWFzayhkb2N1bWVudG8pIDogY25wak1hc2soZG9jdW1lbnRvKTtcclxuICAgICAgICAgIHNldFZhbHVlKFwiY3BmXCIsIGRvY3VtZW50b0NvbU1hc2NhcmEpO1xyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICBzZXRWYWx1ZShcImNwZlwiLCBcIlwiKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHNldFZhbHVlKFwiaWRlbnRpZGFkZVwiLCBjb250cmFjdERhdGEucmcgfHwgXCJcIik7XHJcbiAgICAgICAgc2V0VmFsdWUoXCJlbWFpbFwiLCBjb250cmFjdERhdGEuZW1haWwgfHwgXCJcIik7XHJcblxyXG4gICAgICAgIC8vIEFwbGljYXIgbcOhc2NhcmEgbm8gdGVsZWZvbmUgKHByZXNlcnZhbmRvIG8gZm9ybWF0byBvcmlnaW5hbCBwYXJhIG8gYmFja2VuZClcclxuICAgICAgICBjb25zdCB0ZWxlZm9uZSA9IGNvbnRyYWN0RGF0YS5waG9uZSB8fCBcIlwiO1xyXG4gICAgICAgIHNldFZhbHVlKFwiY2VsdWxhclwiLCB0ZWxlZm9uZSA/IHBob25lV2l0aENvdW50cnlNYXNrKHRlbGVmb25lKSA6IFwiXCIpO1xyXG5cclxuICAgICAgICAvLyBQcsOpLXByZWVuY2hlciBub21lIGRhIG3Do2UgZSBkYXRhIGRlIG5hc2NpbWVudG8gY29tIGRhZG9zIGRvIGludmVzdGlkb3JcclxuICAgICAgICBzZXRWYWx1ZShcIm5vbWVNYWVcIiwgY29udHJhY3REYXRhLm1vdGhlck5hbWUgfHwgXCJcIik7XHJcbiAgICAgICAgc2V0VmFsdWUoXCJkYXRhTmFzY2ltZW50b1wiLCBjb250cmFjdERhdGEuYmlydGhEYXRlIHx8IFwiXCIpO1xyXG5cclxuICAgICAgICAvLyBFbmRlcmXDp29cclxuICAgICAgICBjb25zdCBjZXAgPSBjb250cmFjdERhdGEuemlwQ29kZSB8fCBcIlwiO1xyXG4gICAgICAgIHNldFZhbHVlKFwiY2VwXCIsIGNlcCA/IGNlcE1hc2soY2VwKSA6IFwiXCIpO1xyXG4gICAgICAgIHNldFZhbHVlKFwiY2lkYWRlXCIsIGNvbnRyYWN0RGF0YS5jaXR5IHx8IFwiXCIpO1xyXG4gICAgICAgIHNldFZhbHVlKFwiZW5kZXJlY29cIiwgY29udHJhY3REYXRhLmFkZHJlc3MgfHwgXCJcIik7XHJcbiAgICAgICAgc2V0VmFsdWUoXCJudW1lcm9cIiwgY29udHJhY3REYXRhLmFkZHJlc3NOdW1iZXIgfHwgXCJcIik7XHJcbiAgICAgICAgc2V0VmFsdWUoXCJjb21wbGVtZW50b1wiLCBjb250cmFjdERhdGEuY29tcGxlbWVudCB8fCBcIlwiKTtcclxuICAgICAgICBzZXRWYWx1ZShcImVzdGFkb1wiLCBjb250cmFjdERhdGEuc3RhdGUgfHwgXCJcIik7XHJcblxyXG4gICAgICAgIC8vIERhZG9zIGJhbmPDoXJpb3NcclxuICAgICAgICBzZXRWYWx1ZShcImJhbmNvXCIsIGNvbnRyYWN0RGF0YS5iYW5rIHx8IFwiXCIpO1xyXG4gICAgICAgIHNldFZhbHVlKFwiYWdlbmNpYVwiLCBjb250cmFjdERhdGEuYnJhbmNoIHx8IFwiXCIpO1xyXG4gICAgICAgIHNldFZhbHVlKFwiY29udGFcIiwgY29udHJhY3REYXRhLmFjY291bnROdW1iZXIgfHwgXCJcIik7XHJcbiAgICAgICAgc2V0VmFsdWUoXCJjaGF2ZVBpeFwiLCBjb250cmFjdERhdGEucGhvbmUgfHwgY29udHJhY3REYXRhLmVtYWlsIHx8IFwiXCIpOyAvLyBVc2FyIHRlbGVmb25lIG91IGVtYWlsIGNvbW8gUElYIHBhZHLDo29cclxuXHJcbiAgICAgICAgLy8gRGFkb3MgZG8gaW52ZXN0aW1lbnRvIChkbyBjb250cmF0byBhdHVhbClcclxuICAgICAgICBjb25zdCB2YWxvckludmVzdGltZW50byA9IGNvbnRyYWN0LmludmVzdG1lbnRWYWx1ZSB8fCBcIlwiO1xyXG4gICAgICAgIC8vIEFwbGljYXIgbcOhc2NhcmEgbm8gdmFsb3IgZG8gaW52ZXN0aW1lbnRvIHNlIGhvdXZlciB2YWxvclxyXG4gICAgICAgIHNldFZhbHVlKFwidmFsb3JJbnZlc3RpbWVudG9cIiwgdmFsb3JJbnZlc3RpbWVudG8gPyB2YWx1ZU1hc2sodmFsb3JJbnZlc3RpbWVudG8pIDogXCJcIik7XHJcbiAgICAgICAgc2V0VmFsdWUoXCJ0YXhhUmVtdW5lcmFjYW9cIiwgY29udHJhY3QuaW52ZXN0bWVudFlpZWxkIHx8IFwiXCIpO1xyXG4gICAgICAgIHNldFZhbHVlKFwicHJhem9JbnZlc3RpbWVudG9cIiwgY29udHJhY3QuaW52ZXN0bWVudFRlcm0gfHwgXCJcIik7XHJcbiAgICAgICAgLy8gTWFwZWFyIGZvcm1hIGRlIHBhZ2FtZW50b1xyXG4gICAgICAgIGxldCBwYXltZW50TWV0aG9kID0gXCJwaXhcIjsgLy8gcGFkcsOjb1xyXG4gICAgICAgIGlmIChjb250cmFjdC5wdXJjaGFzZWRXaXRoPy5pbmNsdWRlcyhcIlRSQU5TRkVSw4pOQ0lBXCIpIHx8IGNvbnRyYWN0LnB1cmNoYXNlZFdpdGg/LmluY2x1ZGVzKFwiQkFOQ8OBUklBXCIpKSB7XHJcbiAgICAgICAgICBwYXltZW50TWV0aG9kID0gXCJiYW5rX3RyYW5zZmVyXCI7XHJcbiAgICAgICAgfSBlbHNlIGlmIChjb250cmFjdC5wdXJjaGFzZWRXaXRoPy5pbmNsdWRlcyhcIlBJWFwiKSkge1xyXG4gICAgICAgICAgcGF5bWVudE1ldGhvZCA9IFwicGl4XCI7XHJcbiAgICAgICAgfSBlbHNlIGlmIChjb250cmFjdC5wdXJjaGFzZWRXaXRoPy5pbmNsdWRlcyhcIkJPTEVUT1wiKSkge1xyXG4gICAgICAgICAgcGF5bWVudE1ldGhvZCA9IFwiYm9sZXRvXCI7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldFZhbHVlKFwiY29tcHJhckNvbVwiLCBwYXltZW50TWV0aG9kKTtcclxuICAgICAgICBzZXRWYWx1ZShcImluaWNpb0NvbnRyYXRvXCIsIGNvbnRyYWN0LmNvbnRyYWN0U3RhcnQgPyBtb21lbnQoY29udHJhY3QuY29udHJhY3RTdGFydCkuZm9ybWF0KFwiWVlZWS1NTS1ERFwiKSA6IFwiXCIpO1xyXG5cclxuICAgICAgICAvLyBNb2RhbGlkYWRlIGJhc2VhZGEgbmFzIHRhZ3NcclxuICAgICAgICBzZXRWYWx1ZShcIm1vZGFsaWRhZGVcIiwgY29udHJhY3QudGFncyA9PT0gXCJQMlBcIiA/IFwiTVVUVU9cIiA6IFwiU0NQXCIpO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZyhcIkZvcm11bMOhcmlvIHByw6ktcHJlZW5jaGlkbyBjb20gZGFkb3MgZG8gY29udHJhdG86XCIsIGNvbnRyYWN0RGF0YSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbY29udHJhY3REYXRhLCBpc0xvYWRpbmdDb250cmFjdCwgc2V0VmFsdWVdKTtcclxuXHJcbiAgLy8gQ2FsY3VsYXIgcXVhbnRpZGFkZSBkZSBjb3RhcyBhdXRvbWF0aWNhbWVudGUgcGFyYSBTQ1AgYmFzZWFkbyBubyB2YWxvciBkbyBpbnZlc3RpbWVudG9cclxuICBjb25zdCBtb2RhbGlkYWRlID0gd2F0Y2goXCJtb2RhbGlkYWRlXCIpO1xyXG4gIGNvbnN0IHZhbG9ySW52ZXN0aW1lbnRvID0gd2F0Y2goXCJ2YWxvckludmVzdGltZW50b1wiKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChtb2RhbGlkYWRlID09PSBcIlNDUFwiICYmIHZhbG9ySW52ZXN0aW1lbnRvICYmIHZhbG9ySW52ZXN0aW1lbnRvLnRyaW0oKSAhPT0gXCJcIikge1xyXG4gICAgICAvLyBVc2FyIGZvcm1hdE51bWJlclZhbHVlIHBhcmEgY29udmVydGVyIGNvcnJldGFtZW50ZSBvIHZhbG9yIGNvbSBtw6FzY2FyYSBicmFzaWxlaXJhXHJcbiAgICAgIGNvbnN0IHZhbG9yTnVtZXJpY28gPSBmb3JtYXROdW1iZXJWYWx1ZSh2YWxvckludmVzdGltZW50bykgfHwgMDtcclxuICAgICAgaWYgKHZhbG9yTnVtZXJpY28gPiAwKSB7XHJcbiAgICAgICAgY29uc3QgY290YXMgPSBNYXRoLmZsb29yKHZhbG9yTnVtZXJpY28gLyA1MDAwKTtcclxuICAgICAgICBzZXRWYWx1ZShcInF1b3RhUXVhbnRpdHlcIiwgY290YXMudG9TdHJpbmcoKSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgc2V0VmFsdWUoXCJxdW90YVF1YW50aXR5XCIsIFwiMFwiKTtcclxuICAgICAgfVxyXG4gICAgfSBlbHNlIGlmIChtb2RhbGlkYWRlID09PSBcIlNDUFwiKSB7XHJcbiAgICAgIC8vIFNlIG7Do28gaMOhIHZhbG9yIG91IHZhbG9yIGVzdMOhIHZhemlvLCBsaW1wYXIgY290YXNcclxuICAgICAgc2V0VmFsdWUoXCJxdW90YVF1YW50aXR5XCIsIFwiMFwiKTtcclxuICAgIH1cclxuICB9LCBbbW9kYWxpZGFkZSwgdmFsb3JJbnZlc3RpbWVudG8sIHNldFZhbHVlXSk7XHJcblxyXG4gIC8vIFZlcmlmaWNhciBzZSBow6EgY29udHJhdG9zIHbDoWxpZG9zIChhdGl2b3MpIHBhcmEgaGFiaWxpdGFyIG8gYm90w6NvXHJcbiAgY29uc3QgaGFzQWN0aXZlQ29udHJhY3RzID0gdXNlTWVtbygoKSA9PiB7XHJcbiAgICBpZiAoIWNvbnRyYWN0RGF0YSB8fCAhY29udHJhY3REYXRhLmNvbnRyYWN0cz8uWzBdKSByZXR1cm4gZmFsc2U7XHJcblxyXG4gICAgY29uc3QgY29udHJhY3QgPSBjb250cmFjdERhdGEuY29udHJhY3RzWzBdO1xyXG4gICAgY29uc3QgY29udHJhY3RTdGF0dXMgPSBjb250cmFjdC5jb250cmFjdFN0YXR1cz8udG9VcHBlckNhc2UoKTtcclxuXHJcbiAgICAvLyBWZXJpZmljYXIgc2UgbyBjb250cmF0byBwcmluY2lwYWwgZXN0w6EgYXRpdm9cclxuICAgIGNvbnN0IG1haW5Db250cmFjdEFjdGl2ZSA9IGNvbnRyYWN0U3RhdHVzID09PSAnQUNUSVZFJyB8fCBjb250cmFjdFN0YXR1cyA9PT0gJ0FUSVZPJztcclxuXHJcbiAgICAvLyBWZXJpZmljYXIgc2UgaMOhIHBlbG8gbWVub3MgdW0gYWRpdGl2byBhdGl2b1xyXG4gICAgbGV0IGhhc0FjdGl2ZUFkZGVuZHVtID0gZmFsc2U7XHJcbiAgICBpZiAoY29udHJhY3QuYWRkZW5kdW0gJiYgY29udHJhY3QuYWRkZW5kdW0ubGVuZ3RoID4gMCkge1xyXG4gICAgICBoYXNBY3RpdmVBZGRlbmR1bSA9IGNvbnRyYWN0LmFkZGVuZHVtLnNvbWUoKGFkZGVuZHVtOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zdCBhZGRlbmR1bVN0YXR1cyA9IGFkZGVuZHVtLmNvbnRyYWN0U3RhdHVzPy50b1VwcGVyQ2FzZSgpO1xyXG4gICAgICAgIHJldHVybiBhZGRlbmR1bVN0YXR1cyA9PT0gJ0FDVElWRScgfHwgYWRkZW5kdW1TdGF0dXMgPT09ICdBVElWTyc7XHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBtYWluQ29udHJhY3RBY3RpdmUgfHwgaGFzQWN0aXZlQWRkZW5kdW07XHJcbiAgfSwgW2NvbnRyYWN0RGF0YV0pO1xyXG5cclxuICAvLyBGdW7Dp8O1ZXMgcGFyYSBuYXZlZ2HDp8OjbyBkYXMgb3DDp8O1ZXMgZGUgdXBncmFkZVxyXG4gIGNvbnN0IGhhbmRsZVJlbnRhYmlsaXR5Q2xpY2sgPSAoKSA9PiB7XHJcbiAgICByb3V0ZXIucHVzaChgL2NvbnRyYXRvcy9hbHRlcmFyP3RpcG89cmVudGFiaWxpZGFkZSZpbnZlc3RvcklkPSR7aW52ZXN0b3JJZH1gKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVNb2RhbGl0eUNsaWNrID0gKCkgPT4ge1xyXG4gICAgcm91dGVyLnB1c2goYC9jb250cmF0b3MvYWx0ZXJhcj90aXBvPW1vZGFsaWRhZGUmaW52ZXN0b3JJZD0ke2ludmVzdG9ySWR9YCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gQ29tcG9uZW50ZSBwYXJhIGFzIG9ww6fDtWVzIGRlIHVwZ3JhZGVcclxuICBjb25zdCB1cGdyYWRlQ29udHJhY3RPcHRpb25zID0gKCkgPT4ge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi02XCI+RXNjb2xoYSBvIHRpcG8gZGUgYWx0ZXJhw6fDo288L2gzPlxyXG5cclxuICAgICAgICB7LyogTXVkYW7Dp2EgZGUgUmVudGFiaWxpZGFkZSAqL31cclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IG1kOmZsZXgtcm93IGZsZXgtY29sIGdhcC0yIGp1c3RpZnktYmV0d2VlbiBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZW50YWJpbGl0eUNsaWNrfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMxQzFDMUNdIHctZnVsbCBwLTUgcm91bmRlZC1sZyBib3JkZXItWyNGRjk5MDBdIGJvcmRlciBob3ZlcjpiZy1bIzJDMkMyQ10gdHJhbnNpdGlvbi1jb2xvcnNcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJcIj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1sLTMgZm9udC1ib2xkIHRleHQteGwgbGVhZGluZy1bMThweF0gd2VpZ2h0LTcwMCB0ZXh0LVsyMHB4XSBsaC1bMThweF0gbWItNCB0ZXh0LXdoaXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgIE11ZGFuw6dhIGRlIFJlbnRhYmlsaWRhZGVcclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1sLTMgZm9udC1yZWd1bGFyIHdlaWdodC00MDAgdGV4dC1bMTVweF0gbGgtWzE4cHhdIHRleHQtZ3JheS0zMDBcIj5cclxuICAgICAgICAgICAgICAgICAgQ2xpcXVlIGFxdWkgcGFyYSBtdWRhciBhIHJlbnRhYmlsaWRhZGUgZG8gY29udHJhdG8gZG8gaW52ZXN0aWRvclxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIlwiPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtbGcgZm9udC1ib2xkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAg4oaSXHJcbiAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogTXVkYW7Dp2EgZGUgTW9kYWxpZGFkZSAqL31cclxuICAgICAgICA8ZGl2XHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IG1kOmZsZXgtcm93IGZsZXgtY29sIGdhcC0yIGp1c3RpZnktYmV0d2VlbiBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVNb2RhbGl0eUNsaWNrfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMxQzFDMUNdIHctZnVsbCBwLTUgcm91bmRlZC1sZyBib3JkZXItWyNGRjk5MDBdIGJvcmRlciBob3ZlcjpiZy1bIzJDMkMyQ10gdHJhbnNpdGlvbi1jb2xvcnNcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJcIj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1sLTMgZm9udC1ib2xkIHRleHQteGwgbGVhZGluZy1bMThweF0gd2VpZ2h0LTcwMCB0ZXh0LVsyMHB4XSBsaC1bMThweF0gbWItNCB0ZXh0LXdoaXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgIE11ZGFuw6dhIGRlIE1vZGFsaWRhZGVcclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1sLTMgZm9udC1yZWd1bGFyIHdlaWdodC00MDAgdGV4dC1bMTVweF0gbGgtWzE4cHhdIHRleHQtZ3JheS0zMDBcIj5cclxuICAgICAgICAgICAgICAgICAgQ2xpcXVlIGFxdWkgcGFyYSBtdWRhciBhIG1vZGFsaWRhZGUgZG8gY29udHJhdG8gZG8gaW52ZXN0aWRvclxyXG4gICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIlwiPlxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtbGcgZm9udC1ib2xkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAg4oaSXHJcbiAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfTtcclxuXHJcbiAgZnVuY3Rpb24gY2FsY3VsYXJBbGlxdW90YUlSKHNob3dUb2FzdCA9IHRydWUpIHtcclxuICAgIC8vIFBlZ2EgbyBwcmF6byBkbyBpbnZlc3RpbWVudG8gZW0gbWVzZXMgZSBjb252ZXJ0ZSBwYXJhIGRpYXNcclxuICAgIGNvbnN0IHByYXpvTWVzZXMgPSBOdW1iZXIod2F0Y2goXCJwcmF6b0ludmVzdGltZW50b1wiKSk7XHJcbiAgICBpZiAoIXByYXpvTWVzZXMgfHwgaXNOYU4ocHJhem9NZXNlcykpIHtcclxuICAgICAgc2V0QWxpcXVvdGFJUihcIlwiKTtcclxuICAgICAgc2V0SXJDYWxjdWxhZG8oZmFsc2UpO1xyXG4gICAgICBpZiAoc2hvd1RvYXN0KSB7XHJcbiAgICAgICAgdG9hc3Qud2FybihcIlByZWVuY2hhIG8gcHJhem8gZG8gaW52ZXN0aW1lbnRvIHBhcmEgY2FsY3VsYXIgbyBJUi5cIik7XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG4gICAgY29uc3QgcHJhem9EaWFzID0gcHJhem9NZXNlcyAqIDMwO1xyXG4gICAgbGV0IGFsaXF1b3RhID0gXCJcIjtcclxuICAgIGlmIChwcmF6b0RpYXMgPD0gMTgwKSB7XHJcbiAgICAgIGFsaXF1b3RhID0gXCIyMiw1JVwiO1xyXG4gICAgfSBlbHNlIGlmIChwcmF6b0RpYXMgPD0gMzYwKSB7XHJcbiAgICAgIGFsaXF1b3RhID0gXCIyMCVcIjtcclxuICAgIH0gZWxzZSBpZiAocHJhem9EaWFzIDw9IDcyMCkge1xyXG4gICAgICBhbGlxdW90YSA9IFwiMTcsNSVcIjtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGFsaXF1b3RhID0gXCIxNSVcIjtcclxuICAgIH1cclxuICAgIHNldEFsaXF1b3RhSVIoYWxpcXVvdGEpO1xyXG4gICAgc2V0SXJDYWxjdWxhZG8odHJ1ZSk7XHJcbiAgICBpZiAoc2hvd1RvYXN0KSB7XHJcbiAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJJUiBjYWxjdWxhZG8gY29tIHN1Y2Vzc28hIEFnb3JhIHNlbGVjaW9uZSB1bWEgZGFzIG9ww6fDtWVzIGRlIElSIGFiYWl4by5cIik7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBvblN1Ym1pdCA9IGFzeW5jIChkYXRhOiBhbnkpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKFwiSW5pY2lhbmRvIHN1Ym1pc3PDo28gZG8gZm9ybXVsw6FyaW8uLi5cIiwgZGF0YSk7XHJcblxyXG4gICAgLy8gVmFsaWRhw6fDtWVzIGVzcGVjw61maWNhcyBwYXJhIE1VVFVPIG91IHF1YW5kbyBjb250cmF0byBhbnRlcmlvciBlcmEgTVVUVU9cclxuICAgIGlmIChkYXRhLm1vZGFsaWRhZGUgPT09IFwiTVVUVU9cIiB8fCBjb250cmFjdERldGFpbHM/LmNvbnRyYWN0VHlwZSA9PT0gXCJNVVRVT1wiKSB7XHJcbiAgICAgIGlmICghaXJDYWxjdWxhZG8pIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIuKaoO+4jyBPYnJpZ2F0w7NyaW86IENsaXF1ZSBubyBib3TDo28gJ0NhbGN1bGFyIElSJyBhbnRlcyBkZSBwcm9zc2VndWlyLlwiKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmICghZGF0YS5pckRlcG9zaXRvICYmICFkYXRhLmlyRGVzY29udG8pIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIuKaoO+4jyBPYnJpZ2F0w7NyaW86IFNlbGVjaW9uZSB1bWEgZGFzIG9ww6fDtWVzIGRlIElSIChkZXDDs3NpdG8gb3UgZGVzY29udG8pLlwiKTtcclxuICAgICAgICByZXR1cm47XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBWYWxpZGHDp8OjbyBlc3BlY8OtZmljYSBwYXJhIFNDUCB1c2FuZG8gZnVuw6fDo28gY3VzdG9taXphZGFcclxuICAgIGlmICghdmFsaWRhdGVTY3BWYWx1ZShkYXRhLnZhbG9ySW52ZXN0aW1lbnRvLCBkYXRhLm1vZGFsaWRhZGUpKSB7XHJcbiAgICAgIHRvYXN0LmVycm9yKHNjcFZhbGlkYXRpb25FcnJvcik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICAvLyBWYWxpZGFyIHNlIGVzdMOhIHRlbnRhbmRvIG11ZGFyIGRlIFNDUCBwYXJhIE1VVFVPXHJcbiAgICBpZiAoY3VycmVudENvbnRyYWN0TW9kYWxpdHkgPT09IFwiU0NQXCIgJiYgZGF0YS5tb2RhbGlkYWRlID09PSBcIk1VVFVPXCIpIHtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJOw6NvIMOpIHBvc3PDrXZlbCBhbHRlcmFyIGNvbnRyYXRvcyBTQ1AgcGFyYSBtb2RhbGlkYWRlIE3DunR1b1wiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnNvbGUubG9nKFwiSW52ZXN0b3IgSUQ6XCIsIGludmVzdG9ySWQpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGlzUEogPSBkYXRhLnRpcG9Db250cmF0byA9PT0gXCJwalwiO1xyXG4gICAgICAvLyBNw6FzY2FyYSBjb3JyZXRhIHBhcmEgQ1BGL0NOUEpcclxuICAgICAgY29uc3QgZG9jdW1lbnRvID0gaXNQSlxyXG4gICAgICAgID8gZGF0YS5jcGYucmVwbGFjZSgvXFxEL2csIFwiXCIpIC8vIENOUEpcclxuICAgICAgICA6IGRhdGEuY3BmLnJlcGxhY2UoL1xcRC9nLCBcIlwiKTsgLy8gQ1BGXHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhcIkRhZG9zIHByb2Nlc3NhZG9zOlwiLCB7IGlzUEosIGRvY3VtZW50bywgdXNlclByb2ZpbGUgfSk7XHJcblxyXG4gICAgICAvLyBDcmlhciBvYmpldG8gdXNhbmRvIGEgZXN0cnV0dXJhIGRvIENyZWF0ZU5ld0NvbnRyYWN0RHRvXHJcbiAgICAgIGNvbnN0IHJlcXVlc3REYXRhOiBhbnkgPSB7XHJcbiAgICAgICAgcm9sZTogKCgpID0+IHtcclxuICAgICAgICAgIC8vIE1hcGVhciBvcyByb2xlcyBjb3JyZXRhbWVudGVcclxuICAgICAgICAgIGlmICh1c2VyUHJvZmlsZS5uYW1lID09PSBcInN1cGVyYWRtaW5cIiB8fCB1c2VyUHJvZmlsZS5uYW1lID09PSBcImFkbWluXCIpIHtcclxuICAgICAgICAgICAgcmV0dXJuIFwiYnJva2VyXCI7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBpZiAodXNlclByb2ZpbGUubmFtZSA9PT0gXCJhZHZpc29yXCIpIHtcclxuICAgICAgICAgICAgcmV0dXJuIFwiYWR2aXNvclwiO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgaWYgKHVzZXJQcm9maWxlLm5hbWUgPT09IFwiYnJva2VyXCIpIHtcclxuICAgICAgICAgICAgcmV0dXJuIFwiYnJva2VyXCI7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICAvLyBGYWxsYmFjayBwYXJhIGJyb2tlclxyXG4gICAgICAgICAgcmV0dXJuIFwiYnJva2VyXCI7XHJcbiAgICAgICAgfSkoKSxcclxuICAgICAgICBwZXJzb25UeXBlOiBpc1BKID8gXCJQSlwiIDogXCJQRlwiLFxyXG4gICAgICAgIGNvbnRyYWN0VHlwZTogZGF0YS5tb2RhbGlkYWRlLCAvLyBTQ1Agb3UgTVVUVU9cclxuICAgICAgICBhZHZpc29yczogKCgpID0+IHtcclxuICAgICAgICAgIC8vIFNlIG8gdXN1w6FyaW8gZm9yIGFkdmlzb3IsIGFkaWNpb25hciBlbGUgbWVzbW8gY29tbyBhZHZpc29yXHJcbiAgICAgICAgICBpZiAodXNlclByb2ZpbGUubmFtZSA9PT0gXCJhZHZpc29yXCIpIHtcclxuICAgICAgICAgICAgcmV0dXJuIFt7XHJcbiAgICAgICAgICAgICAgYWR2aXNvcklkOiB1c2VyUHJvZmlsZS5yb2xlSWQsIC8vIFVzYXIgcm9sZUlkIGVtIHZleiBkZSBpZFxyXG4gICAgICAgICAgICAgIHJhdGU6IDEwMCAvLyAxMDAlIHBhcmEgbyBwcsOzcHJpbyBhZHZpc29yXHJcbiAgICAgICAgICAgIH1dO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgLy8gU2UgZm9yIGJyb2tlciBvdSBzdXBlcmFkbWluLCBkZWl4YXIgdmF6aW8gKHNlcsOhIHByZWVuY2hpZG8gcGVsbyBiYWNrZW5kKVxyXG4gICAgICAgICAgcmV0dXJuIFtdO1xyXG4gICAgICAgIH0pKCksXHJcbiAgICAgICAgaW52ZXN0bWVudDoge1xyXG4gICAgICAgICAgYW1vdW50OiBwYXJzZUludChkYXRhLnZhbG9ySW52ZXN0aW1lbnRvLnJlcGxhY2UoL1xcRC9nLCBcIlwiKSkgfHwgMCxcclxuICAgICAgICAgIG1vbnRobHlSYXRlOiBwYXJzZUZsb2F0KGRhdGEudGF4YVJlbXVuZXJhY2FvKSB8fCAwLFxyXG4gICAgICAgICAgZHVyYXRpb25Jbk1vbnRoczogMTIsIC8vIFZhbG9yIHBhZHLDo29cclxuICAgICAgICAgIHBheW1lbnRNZXRob2Q6IFwicGl4XCIsIC8vIFZhbG9yIHBhZHLDo29cclxuICAgICAgICAgIGVuZERhdGU6ICgoKSA9PiB7XHJcbiAgICAgICAgICAgIC8vIENhbGN1bGFyIGRhdGEgZGUgZmltIGJhc2VhZGEgbmEgZGF0YSBhdHVhbCArIDEyIG1lc2VzXHJcbiAgICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XHJcbiAgICAgICAgICAgIGNvbnN0IGVuZERhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSArIDEsIG5vdy5nZXRNb250aCgpLCBub3cuZ2V0RGF0ZSgpKTtcclxuICAgICAgICAgICAgcmV0dXJuIGVuZERhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdOyAvLyBGb3JtYXRvIFlZWVktTU0tRERcclxuICAgICAgICAgIH0pKCksXHJcbiAgICAgICAgICBwcm9maWxlOiBcIm1vZGVyYXRlXCIsIC8vIGNvbnNlcnZhdGl2ZSwgbW9kZXJhdGUsIGFnZ3Jlc3NpdmVcclxuICAgICAgICAgIHF1b3RhUXVhbnRpdHk6IGRhdGEubW9kYWxpZGFkZSA9PT0gXCJTQ1BcIiA/IE1hdGgubWF4KDEsIHBhcnNlSW50KGRhdGEucXVvdGFRdWFudGl0eSB8fCBcIjFcIikgfHwgMSkgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgICBpc0RlYmVudHVyZTogZmFsc2VcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJhbmtBY2NvdW50OiB7XHJcbiAgICAgICAgICBiYW5rOiBkYXRhLmJhbmNvIHx8IFwiXCIsXHJcbiAgICAgICAgICBhZ2VuY3k6IGRhdGEuYWdlbmNpYSB8fCBcIlwiLFxyXG4gICAgICAgICAgYWNjb3VudDogZGF0YS5jb250YSB8fCBcIlwiLFxyXG4gICAgICAgICAgcGl4OiBkYXRhLmNoYXZlUGl4IHx8IFwiXCJcclxuICAgICAgICB9LFxyXG4gICAgICAgIGluZGl2aWR1YWw6IGlzUEogPyB1bmRlZmluZWQgOiB7XHJcbiAgICAgICAgICBmdWxsTmFtZTogZGF0YS5ub21lQ29tcGxldG8gfHwgXCJcIixcclxuICAgICAgICAgIGNwZjogZG9jdW1lbnRvLFxyXG4gICAgICAgICAgcmc6ICgoKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJnID0gZGF0YS5pZGVudGlkYWRlO1xyXG4gICAgICAgICAgICBpZiAoIXJnKSByZXR1cm4gXCIxMjM0NTY3ODlcIjtcclxuICAgICAgICAgICAgY29uc3QgcmdMaW1wbyA9IHJnLnRvU3RyaW5nKCkucmVwbGFjZSgvXFxEL2csIFwiXCIpO1xyXG4gICAgICAgICAgICByZXR1cm4gcmdMaW1wbyB8fCBcIjEyMzQ1Njc4OVwiO1xyXG4gICAgICAgICAgfSkoKSxcclxuICAgICAgICAgIGlzc3VpbmdBZ2VuY3k6IFwiU1NQXCIsXHJcbiAgICAgICAgICBuYXRpb25hbGl0eTogXCJCcmFzaWxlaXJhXCIsXHJcbiAgICAgICAgICBvY2N1cGF0aW9uOiBcIkludmVzdGlkb3JcIixcclxuICAgICAgICAgIGJpcnRoRGF0ZTogKCgpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgYmlydGhEYXRlID0gZGF0YS5kYXRhTmFzY2ltZW50bztcclxuICAgICAgICAgICAgaWYgKCFiaXJ0aERhdGUpIHJldHVybiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXTtcclxuICAgICAgICAgICAgaWYgKGJpcnRoRGF0ZS5tYXRjaCgvXlxcZHs0fS1cXGR7Mn0tXFxkezJ9JC8pKSB7XHJcbiAgICAgICAgICAgICAgcmV0dXJuIGJpcnRoRGF0ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoYmlydGhEYXRlKTtcclxuICAgICAgICAgICAgcmV0dXJuIGRhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xyXG4gICAgICAgICAgfSkoKSxcclxuICAgICAgICAgIGVtYWlsOiBkYXRhLmVtYWlsIHx8IFwiaW52ZXN0aWRvckBleGVtcGxvLmNvbVwiLFxyXG4gICAgICAgICAgcGhvbmU6ICgoKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGNsZWFuUGhvbmUgPSBkYXRhLmNlbHVsYXIucmVwbGFjZSgvXFxEL2csIFwiXCIpO1xyXG4gICAgICAgICAgICBpZiAoY2xlYW5QaG9uZS5sZW5ndGggPT09IDExICYmICFjbGVhblBob25lLnN0YXJ0c1dpdGgoJzU1JykpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gJzU1JyArIGNsZWFuUGhvbmU7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIGNsZWFuUGhvbmU7XHJcbiAgICAgICAgICB9KSgpLFxyXG4gICAgICAgICAgbW90aGVyTmFtZTogZGF0YS5ub21lTWFlIHx8IFwiXCIsXHJcbiAgICAgICAgICBhZGRyZXNzOiB7XHJcbiAgICAgICAgICAgIHN0cmVldDogZGF0YS5ydWEgfHwgXCJSdWEgUHJpbmNpcGFsXCIsXHJcbiAgICAgICAgICAgIG5laWdoYm9yaG9vZDogZGF0YS5iYWlycm8gfHwgXCJDZW50cm9cIixcclxuICAgICAgICAgICAgY2l0eTogZGF0YS5jaWRhZGUgfHwgXCJDaWRhZGVcIixcclxuICAgICAgICAgICAgc3RhdGU6IGRhdGEuZXN0YWRvIHx8IFwiQkFcIixcclxuICAgICAgICAgICAgcG9zdGFsQ29kZTogZGF0YS5jZXAgPyBkYXRhLmNlcC5yZXBsYWNlKC9cXEQvZywgXCJcIikgOiBcIjAwMDAwMDAwXCIsXHJcbiAgICAgICAgICAgIG51bWJlcjogZGF0YS5udW1lcm8gfHwgXCIxXCIsXHJcbiAgICAgICAgICAgIGNvbXBsZW1lbnQ6IGRhdGEuY29tcGxlbWVudG8gfHwgXCJcIlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgY29tcGFueTogaXNQSiA/IHtcclxuICAgICAgICAgIGNvbXBhbnlOYW1lOiBkYXRhLm5vbWVDb21wbGV0byB8fCBcIlwiLFxyXG4gICAgICAgICAgY25wajogZG9jdW1lbnRvLFxyXG4gICAgICAgICAgbGVnYWxUeXBlOiBcIkxUREFcIixcclxuICAgICAgICAgIGVtYWlsOiBkYXRhLmVtYWlsIHx8IFwiZW1wcmVzYUBleGVtcGxvLmNvbVwiLFxyXG4gICAgICAgICAgcGhvbmU6ICgoKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGNsZWFuUGhvbmUgPSBkYXRhLmNlbHVsYXIucmVwbGFjZSgvXFxEL2csIFwiXCIpO1xyXG4gICAgICAgICAgICBpZiAoY2xlYW5QaG9uZS5sZW5ndGggPT09IDExICYmICFjbGVhblBob25lLnN0YXJ0c1dpdGgoJzU1JykpIHtcclxuICAgICAgICAgICAgICByZXR1cm4gJzU1JyArIGNsZWFuUGhvbmU7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIGNsZWFuUGhvbmU7XHJcbiAgICAgICAgICB9KSgpLFxyXG4gICAgICAgICAgYWRkcmVzczoge1xyXG4gICAgICAgICAgICBzdHJlZXQ6IGRhdGEucnVhIHx8IFwiUnVhIFByaW5jaXBhbFwiLFxyXG4gICAgICAgICAgICBuZWlnaGJvcmhvb2Q6IGRhdGEuYmFpcnJvIHx8IFwiQ2VudHJvXCIsXHJcbiAgICAgICAgICAgIGNpdHk6IGRhdGEuY2lkYWRlIHx8IFwiQ2lkYWRlXCIsXHJcbiAgICAgICAgICAgIHN0YXRlOiBkYXRhLmVzdGFkbyB8fCBcIkJBXCIsXHJcbiAgICAgICAgICAgIHBvc3RhbENvZGU6IGRhdGEuY2VwID8gZGF0YS5jZXAucmVwbGFjZSgvXFxEL2csIFwiXCIpIDogXCIwMDAwMDAwMFwiLFxyXG4gICAgICAgICAgICBudW1iZXI6IGRhdGEubnVtZXJvIHx8IFwiMVwiLFxyXG4gICAgICAgICAgICBjb21wbGVtZW50OiBkYXRhLmNvbXBsZW1lbnRvIHx8IFwiXCJcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICByZXByZXNlbnRhdGl2ZToge1xyXG4gICAgICAgICAgICBmdWxsTmFtZTogZGF0YS5ub21lQ29tcGxldG8gfHwgXCJcIixcclxuICAgICAgICAgICAgY3BmOiBcIjAwMDAwMDAwMDAwXCIsIC8vIENQRiBkbyByZXByZXNlbnRhbnRlXHJcbiAgICAgICAgICAgIHJnOiBcIjEyMzQ1Njc4OVwiLFxyXG4gICAgICAgICAgICBpc3N1aW5nQWdlbmN5OiBcIlNTUFwiLFxyXG4gICAgICAgICAgICBuYXRpb25hbGl0eTogXCJCcmFzaWxlaXJhXCIsXHJcbiAgICAgICAgICAgIG9jY3VwYXRpb246IFwiUmVwcmVzZW50YW50ZVwiLFxyXG4gICAgICAgICAgICBiaXJ0aERhdGU6IFwiMTk5MC0wMS0wMVwiLFxyXG4gICAgICAgICAgICBlbWFpbDogZGF0YS5lbWFpbCB8fCBcInJlcHJlc2VudGFudGVAZXhlbXBsby5jb21cIixcclxuICAgICAgICAgICAgcGhvbmU6ICgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgY29uc3QgY2xlYW5QaG9uZSA9IGRhdGEuY2VsdWxhci5yZXBsYWNlKC9cXEQvZywgXCJcIik7XHJcbiAgICAgICAgICAgICAgaWYgKGNsZWFuUGhvbmUubGVuZ3RoID09PSAxMSAmJiAhY2xlYW5QaG9uZS5zdGFydHNXaXRoKCc1NScpKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gJzU1JyArIGNsZWFuUGhvbmU7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgIHJldHVybiBjbGVhblBob25lO1xyXG4gICAgICAgICAgICB9KSgpLFxyXG4gICAgICAgICAgICBtb3RoZXJOYW1lOiBkYXRhLm5vbWVNYWUgfHwgXCJcIlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0gOiB1bmRlZmluZWRcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiPT09IFBBWUxPQUQgQ09NUExFVE8gPT09XCIpO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIlVzZXJQcm9maWxlOlwiLCB1c2VyUHJvZmlsZSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiUm9sZSBlbnZpYWRvOlwiLCByZXF1ZXN0RGF0YS5yb2xlKTtcclxuICAgICAgY29uc29sZS5sb2coXCJQZXJzb25UeXBlIGVudmlhZG86XCIsIHJlcXVlc3REYXRhLnBlcnNvblR5cGUpO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkNvbnRyYWN0VHlwZSBlbnZpYWRvOlwiLCByZXF1ZXN0RGF0YS5jb250cmFjdFR5cGUpO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkFkdmlzb3JzIGVudmlhZG86XCIsIHJlcXVlc3REYXRhLmFkdmlzb3JzKTtcclxuICAgICAgY29uc29sZS5sb2coXCJRdW90YVF1YW50aXR5IGVudmlhZG86XCIsIHJlcXVlc3REYXRhLmludmVzdG1lbnQucXVvdGFRdWFudGl0eSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiRW52aWFuZG8gZGFkb3MgcGFyYSBBUEkuLi5cIiwgSlNPTi5zdHJpbmdpZnkocmVxdWVzdERhdGEsIG51bGwsIDIpKTtcclxuXHJcbiAgICAgIGNyZWF0ZUNvbnRyYWN0TXV0YXRpb24ubXV0YXRlKHJlcXVlc3REYXRhKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvIGFvIHByb2Nlc3NhciBkYWRvczpcIiwgZXJyb3IpO1xyXG4gICAgICB0b2FzdC5lcnJvcihcIkVycm8gYW8gcHJvY2Vzc2FyIGRhZG9zIGRvIGNvbnRyYXRvXCIpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIFZhbGlkYcOnw6NvIGUgYXZhbsOnbyBwYXJhIHBhcnRlIDJcclxuICAvLyBMaXN0YSBkb3MgY2FtcG9zIGRvIHN0ZXAgMVxyXG4gIGNvbnN0IGNhbXBvc1N0ZXAxOiAoXHJcbiAgICB8IFwidGlwb0NvbnRyYXRvXCJcclxuICAgIHwgXCJub21lQ29tcGxldG9cIlxyXG4gICAgfCBcImlkZW50aWRhZGVcIlxyXG4gICAgfCBcImNlbHVsYXJcIlxyXG4gICAgfCBcImNwZlwiXHJcbiAgICB8IFwiZGF0YU5hc2NpbWVudG9cIlxyXG4gICAgfCBcIm5vbWVNYWVcIlxyXG4gICAgfCBcImVtYWlsXCJcclxuICAgIHwgXCJjZXBcIlxyXG4gICAgfCBcImNpZGFkZVwiXHJcbiAgICB8IFwiZW5kZXJlY29cIlxyXG4gICAgfCBcIm51bWVyb1wiXHJcbiAgICB8IFwiY29tcGxlbWVudG9cIlxyXG4gICAgfCBcImVzdGFkb1wiXHJcbiAgICB8IFwiYmFuY29cIlxyXG4gICAgfCBcImNvbnRhXCJcclxuICAgIHwgXCJhZ2VuY2lhXCJcclxuICAgIHwgXCJjaGF2ZVBpeFwiXHJcbiAgICB8IFwib2JzZXJ2YWNvZXNcIlxyXG4gICAgfCBcIm1vZGFsaWRhZGVcIlxyXG4gICAgfCBcInZhbG9ySW52ZXN0aW1lbnRvXCJcclxuICAgIHwgXCJwcmF6b0ludmVzdGltZW50b1wiXHJcbiAgICB8IFwidGF4YVJlbXVuZXJhY2FvXCJcclxuICAgIHwgXCJjb21wcmFyQ29tXCJcclxuICAgIHwgXCJpbmljaW9Db250cmF0b1wiXHJcbiAgICB8IFwiZmltQ29udHJhdG9cIlxyXG4gICAgfCBcInBlcmZpbFwiXHJcbiAgICB8IFwiZGViZW50dXJlXCJcclxuICAgIHwgXCJxdW90YVF1YW50aXR5XCJcclxuICApW10gPSBbXHJcbiAgICAgIFwidGlwb0NvbnRyYXRvXCIsXHJcbiAgICAgIFwibm9tZUNvbXBsZXRvXCIsXHJcbiAgICAgIFwiaWRlbnRpZGFkZVwiLFxyXG4gICAgICBcImNlbHVsYXJcIixcclxuICAgICAgXCJjcGZcIixcclxuICAgICAgLy8gXCJkYXRhTmFzY2ltZW50b1wiLCAvLyBPcGNpb25hbCAtIG7Do28gdmVtIGRhIEFQSVxyXG4gICAgICAvLyBcIm5vbWVNYWVcIiwgLy8gT3BjaW9uYWwgLSBuw6NvIHZlbSBkYSBBUElcclxuICAgICAgXCJlbWFpbFwiLFxyXG4gICAgICBcImNlcFwiLFxyXG4gICAgICBcImNpZGFkZVwiLFxyXG4gICAgICBcImVuZGVyZWNvXCIsXHJcbiAgICAgIFwibnVtZXJvXCIsXHJcbiAgICAgIC8vIFwiY29tcGxlbWVudG9cIiwgLy8gT3BjaW9uYWxcclxuICAgICAgXCJlc3RhZG9cIixcclxuICAgICAgXCJiYW5jb1wiLFxyXG4gICAgICBcImNvbnRhXCIsXHJcbiAgICAgIFwiYWdlbmNpYVwiLFxyXG4gICAgICBcImNoYXZlUGl4XCJcclxuICAgICAgLy8gXCJvYnNlcnZhY29lc1wiIC8vIE9wY2lvbmFsXHJcbiAgICBdO1xyXG5cclxuICBjb25zdCBoYW5kbGVOZXh0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coXCJWYWxpZGFuZG8gY2FtcG9zIGRhIHDDoWdpbmEgMTpcIiwgY2FtcG9zU3RlcDEpO1xyXG4gICAgY29uc3QgdmFsaWQgPSBhd2FpdCB0cmlnZ2VyKGNhbXBvc1N0ZXAxKTtcclxuICAgIGNvbnNvbGUubG9nKFwiUmVzdWx0YWRvIGRhIHZhbGlkYcOnw6NvOlwiLCB2YWxpZCk7XHJcbiAgICBjb25zb2xlLmxvZyhcIkVycm9zIGF0dWFpczpcIiwgZXJyb3JzKTtcclxuXHJcbiAgICBpZiAodmFsaWQpIHtcclxuICAgICAgc2V0U3RlcCgyKTtcclxuICAgICAgdG9hc3Quc3VjY2VzcyhcIkRhZG9zIGRhIHDDoWdpbmEgMSB2YWxpZGFkb3MgY29tIHN1Y2Vzc28hXCIpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8gQ29udGFyIHF1YW50b3MgY2FtcG9zIG9icmlnYXTDs3Jpb3MgZXN0w6NvIGNvbSBlcnJvXHJcbiAgICAgIGNvbnN0IGNhbXBvc0NvbUVycm8gPSBjYW1wb3NTdGVwMS5maWx0ZXIoY2FtcG8gPT4gZXJyb3JzW2NhbXBvXSk7XHJcbiAgICAgIGNvbnN0IHF1YW50aWRhZGVFcnJvcyA9IGNhbXBvc0NvbUVycm8ubGVuZ3RoO1xyXG5cclxuICAgICAgaWYgKHF1YW50aWRhZGVFcnJvcyA+IDApIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihgUG9yIGZhdm9yLCBwcmVlbmNoYSAke3F1YW50aWRhZGVFcnJvc30gY2FtcG8ocykgb2JyaWdhdMOzcmlvKHMpIGFudGVzIGRlIGNvbnRpbnVhci5gKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIlBvciBmYXZvciwgcHJlZW5jaGEgdG9kb3Mgb3MgY2FtcG9zIG9icmlnYXTDs3Jpb3MgYW50ZXMgZGUgY29udGludWFyLlwiKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH07XHJcblxyXG5cclxuICBjb25zdCBjcmVhdGVDb250cmFjdE11dGF0aW9uID0gdXNlTXV0YXRpb24oe1xyXG4gICAgbXV0YXRpb25GbjogYXN5bmMgKGRhdGE6IGFueSkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIk11dGF0aW9uIC0gQ3JpYW5kbyBub3ZvIGNvbnRyYXRvXCIpO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkRhZG9zIGVudmlhZG9zOlwiLCBkYXRhKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoYC9jb250cmFjdC9tYW51YWxgLCBkYXRhKTtcclxuICAgICAgY29uc29sZS5sb2coXCJSZXNwb3N0YSBkYSBBUEk6XCIsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0sXHJcbiAgICBvblN1Y2Nlc3M6IChyZXNwb25zZSkgPT4ge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkNvbnRyYXRvIGNyaWFkbyBjb20gc3VjZXNzbzpcIiwgcmVzcG9uc2UpO1xyXG5cclxuICAgICAgLy8gTW9zdHJhciB0b2FzdCBkZSBzdWNlc3NvIGNvbSBpbmZvcm1hw6fDtWVzIGRvIG5vdm8gY29udHJhdG9cclxuICAgICAgY29uc3QgbmV3Q29udHJhY3RJZCA9IHJlc3BvbnNlPy5pZDtcclxuICAgICAgaWYgKG5ld0NvbnRyYWN0SWQpIHtcclxuICAgICAgICB0b2FzdC5zdWNjZXNzKGBDb250cmF0byBjcmlhZG8gY29tIHN1Y2Vzc28hIElEOiAke25ld0NvbnRyYWN0SWQuc3Vic3RyaW5nKDAsIDgpfS4uLiBSZWRpcmVjaW9uYW5kbyBwYXJhIGEgaG9tZS4uLmApO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHRvYXN0LnN1Y2Nlc3MoXCJDb250cmF0byBjcmlhZG8gY29tIHN1Y2Vzc28hIFJlZGlyZWNpb25hbmRvIHBhcmEgYSBob21lLi4uXCIpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBBZ3VhcmRhciB1bSBwb3VjbyBwYXJhIG8gdXN1w6FyaW8gdmVyIGEgbWVuc2FnZW0gZSBkZXBvaXMgcmVkaXJlY2lvbmFyXHJcbiAgICAgIHNldElzUmVkaXJlY3RpbmcodHJ1ZSk7XHJcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIHJvdXRlci5wdXNoKFwiL1wiKTtcclxuICAgICAgfSwgMzAwMCk7IC8vIDMgc2VndW5kb3MgcGFyYSBkYXIgdGVtcG8gZGUgbGVyIGEgbWVuc2FnZW1cclxuICAgIH0sXHJcbiAgICBvbkVycm9yOiAoZXJyb3IpID0+IHtcclxuICAgICAgcmV0dXJuRXJyb3IoZXJyb3IsIFwiRXJybyBhbyBhdHVhbGl6YXIgbyBjb250cmF0b1wiKTtcclxuICAgIH0sXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IHRlcm0gPSB3YXRjaChcInByYXpvSW52ZXN0aW1lbnRvXCIpO1xyXG4gIGNvbnN0IGluaXREYXRlID0gd2F0Y2goXCJpbmljaW9Db250cmF0b1wiKTtcclxuXHJcbiAgY29uc3QgZW5kRGF0ZSA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgaWYgKGluaXREYXRlICYmIHRlcm0pIHtcclxuICAgICAgY29uc3QgZW5kRGF0YSA9IGdldEZpbmFsRGF0YVdpdGhNb3VudCh7XHJcbiAgICAgICAgaW52ZXN0RGF0ZTogU3RyaW5nKHRlcm0pLFxyXG4gICAgICAgIHN0YXJ0RGF0ZTogaW5pdERhdGUsXHJcbiAgICAgIH0pO1xyXG4gICAgICBjb25zdCBmb3JtYXR0ZWRFbmREYXRlID0gbW9tZW50KGVuZERhdGEsIFwiREQtTU0tWVlZWVwiKS5mb3JtYXQoXCJERC9NTS9ZWVlZXCIpO1xyXG4gICAgICAvLyBBdHVhbGl6YSBvIGNhbXBvIGZpbUNvbnRyYXRvIGF1dG9tYXRpY2FtZW50ZVxyXG4gICAgICBzZXRWYWx1ZShcImZpbUNvbnRyYXRvXCIsIG1vbWVudChlbmREYXRhLCBcIkRELU1NLVlZWVlcIikuZm9ybWF0KFwiWVlZWS1NTS1ERFwiKSwgeyBzaG91bGRWYWxpZGF0ZTogdHJ1ZSB9KTtcclxuICAgICAgcmV0dXJuIGZvcm1hdHRlZEVuZERhdGU7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gXCJcIjtcclxuICB9LCBbaW5pdERhdGUsIHRlcm0sIHNldFZhbHVlXSk7XHJcblxyXG4gIC8vIENhbGN1bGFyIGRhZG9zIGRvIGNvbnRyYXRvIGF0dWFsIGUgYWRlbmRvcyBwYXJhIG8gZGV0YWxoYW1lbnRvXHJcbiAgY29uc3QgY29udHJhY3REZXRhaWxzID0gdXNlTWVtbygoKSA9PiB7XHJcbiAgICBpZiAoIWNvbnRyYWN0RGF0YSB8fCAhY29udHJhY3REYXRhLmNvbnRyYWN0cz8uWzBdKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgICBjb25zdCBjb250cmFjdCA9IGNvbnRyYWN0RGF0YS5jb250cmFjdHNbMF07XHJcbiAgICBjb25zdCBkZXRhaWxzID0gW107XHJcblxyXG4gICAgLy8gRnVuw6fDo28gcGFyYSBjYWxjdWxhciBJUiBiYXNlYWRvIG5vIHByYXpvXHJcbiAgICBjb25zdCBjYWxjdWxhdGVJUiA9IChhbW91bnQ6IG51bWJlciwgcmF0ZTogbnVtYmVyLCBkYXlzOiBudW1iZXIpID0+IHtcclxuICAgICAgY29uc3QgdG90YWxSZXR1cm4gPSAoYW1vdW50ICogcmF0ZSAqIChkYXlzIC8gMzApKSAvIDEwMDtcclxuICAgICAgbGV0IGlyUmF0ZSA9IDA7XHJcbiAgICAgIGlmIChkYXlzIDw9IDE4MCkgaXJSYXRlID0gMjIuNTtcclxuICAgICAgZWxzZSBpZiAoZGF5cyA8PSAzNjApIGlyUmF0ZSA9IDIwO1xyXG4gICAgICBlbHNlIGlmIChkYXlzIDw9IDcyMCkgaXJSYXRlID0gMTcuNTtcclxuICAgICAgZWxzZSBpclJhdGUgPSAxNTtcclxuXHJcbiAgICAgIGNvbnN0IGlyVmFsdWUgPSAodG90YWxSZXR1cm4gKiBpclJhdGUpIC8gMTAwO1xyXG4gICAgICByZXR1cm4geyB0b3RhbFJldHVybiwgaXJSYXRlLCBpclZhbHVlIH07XHJcbiAgICB9O1xyXG5cclxuICAgIC8vIENvbnRyYXRvIHByaW5jaXBhbCAtIGFwZW5hcyBzZSBlc3RpdmVyIEFUSVZPXHJcbiAgICBjb25zdCBjb250cmFjdFN0YXR1cyA9IGNvbnRyYWN0LmNvbnRyYWN0U3RhdHVzPy50b1VwcGVyQ2FzZSgpO1xyXG4gICAgaWYgKGNvbnRyYWN0U3RhdHVzID09PSAnQUNUSVZFJyB8fCBjb250cmFjdFN0YXR1cyA9PT0gJ0FUSVZPJykge1xyXG4gICAgICBjb25zdCBtYWluQW1vdW50ID0gcGFyc2VJbnQoY29udHJhY3QuaW52ZXN0bWVudFZhbHVlKSB8fCAwO1xyXG4gICAgICBjb25zdCBtYWluUmF0ZSA9IHBhcnNlRmxvYXQoY29udHJhY3QuaW52ZXN0bWVudFlpZWxkKSB8fCAwO1xyXG4gICAgICBjb25zdCBtYWluU3RhcnREYXRlID0gY29udHJhY3QuY29udHJhY3RTdGFydCA/IG1vbWVudChjb250cmFjdC5jb250cmFjdFN0YXJ0KSA6IG1vbWVudCgpO1xyXG4gICAgICBjb25zdCBtYWluRW5kRGF0ZSA9IGNvbnRyYWN0LmNvbnRyYWN0RW5kID8gbW9tZW50KGNvbnRyYWN0LmNvbnRyYWN0RW5kKSA6IG1vbWVudCgpO1xyXG4gICAgICBjb25zdCBtYWluRGF5cyA9IG1haW5FbmREYXRlLmRpZmYobWFpblN0YXJ0RGF0ZSwgJ2RheXMnKTtcclxuICAgICAgY29uc3QgbWFpbklSID0gY2FsY3VsYXRlSVIobWFpbkFtb3VudCwgbWFpblJhdGUsIG1haW5EYXlzKTtcclxuXHJcbiAgICAgIGRldGFpbHMucHVzaCh7XHJcbiAgICAgICAgdHlwZTogJ0NvbnRyYXRvIEluaWNpYWwnLFxyXG4gICAgICAgIGFtb3VudDogbWFpbkFtb3VudCxcclxuICAgICAgICBkYXlzUmVudGFiaWxpemVkOiBtYWluRGF5cyxcclxuICAgICAgICBtb250aGx5UmF0ZTogbWFpblJhdGUsXHJcbiAgICAgICAgaXJSYXRlOiBtYWluSVIuaXJSYXRlLFxyXG4gICAgICAgIGlyVmFsdWU6IG1haW5JUi5pclZhbHVlLFxyXG4gICAgICAgIHRvdGFsUmV0dXJuOiBtYWluSVIudG90YWxSZXR1cm4sXHJcbiAgICAgICAgc3RhdHVzOiBjb250cmFjdC5jb250cmFjdFN0YXR1c1xyXG4gICAgICB9KTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBBZGVuZG9zIC0gYXBlbmFzIG9zIHF1ZSBlc3TDo28gQVRJVk9TXHJcbiAgICBpZiAoY29udHJhY3QuYWRkZW5kdW0gJiYgY29udHJhY3QuYWRkZW5kdW0ubGVuZ3RoID4gMCkge1xyXG4gICAgICBsZXQgYWN0aXZlQWRkZW5kdW1JbmRleCA9IDE7IC8vIENvbnRhZG9yIHBhcmEgYWRpdGl2b3MgYXRpdm9zXHJcbiAgICAgIGNvbnRyYWN0LmFkZGVuZHVtLmZvckVhY2goKGFkZGVuZHVtOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zdCBhZGRlbmR1bVN0YXR1cyA9IGFkZGVuZHVtLmNvbnRyYWN0U3RhdHVzPy50b1VwcGVyQ2FzZSgpO1xyXG5cclxuICAgICAgICAvLyBJbmNsdWlyIGFwZW5hcyBhZGl0aXZvcyBjb20gc3RhdHVzIEFDVElWRSBvdSBBVElWT1xyXG4gICAgICAgIGlmIChhZGRlbmR1bVN0YXR1cyA9PT0gJ0FDVElWRScgfHwgYWRkZW5kdW1TdGF0dXMgPT09ICdBVElWTycpIHtcclxuICAgICAgICAgIGNvbnN0IGFkZEFtb3VudCA9IHBhcnNlRmxvYXQoYWRkZW5kdW0uaW52ZXN0bWVudFZhbHVlKSB8fCAwO1xyXG4gICAgICAgICAgY29uc3QgYWRkUmF0ZSA9IHBhcnNlRmxvYXQoYWRkZW5kdW0uaW52ZXN0bWVudFlpZWxkKSB8fCAwO1xyXG4gICAgICAgICAgY29uc3QgYWRkU3RhcnREYXRlID0gYWRkZW5kdW0uY29udHJhY3RTdGFydCA/IG1vbWVudChhZGRlbmR1bS5jb250cmFjdFN0YXJ0KSA6IG1vbWVudCgpO1xyXG4gICAgICAgICAgY29uc3QgYWRkRW5kRGF0ZSA9IGFkZGVuZHVtLmNvbnRyYWN0RW5kID8gbW9tZW50KGFkZGVuZHVtLmNvbnRyYWN0RW5kKSA6IG1vbWVudCgpO1xyXG4gICAgICAgICAgY29uc3QgYWRkRGF5cyA9IGFkZEVuZERhdGUuZGlmZihhZGRTdGFydERhdGUsICdkYXlzJyk7XHJcbiAgICAgICAgICBjb25zdCBhZGRJUiA9IGNhbGN1bGF0ZUlSKGFkZEFtb3VudCwgYWRkUmF0ZSwgYWRkRGF5cyk7XHJcblxyXG4gICAgICAgICAgZGV0YWlscy5wdXNoKHtcclxuICAgICAgICAgICAgdHlwZTogYEFkaXRpdm8gJHthY3RpdmVBZGRlbmR1bUluZGV4fWAsXHJcbiAgICAgICAgICAgIGFtb3VudDogYWRkQW1vdW50LFxyXG4gICAgICAgICAgICBkYXlzUmVudGFiaWxpemVkOiBhZGREYXlzLFxyXG4gICAgICAgICAgICBtb250aGx5UmF0ZTogYWRkUmF0ZSxcclxuICAgICAgICAgICAgaXJSYXRlOiBhZGRJUi5pclJhdGUsXHJcbiAgICAgICAgICAgIGlyVmFsdWU6IGFkZElSLmlyVmFsdWUsXHJcbiAgICAgICAgICAgIHRvdGFsUmV0dXJuOiBhZGRJUi50b3RhbFJldHVybixcclxuICAgICAgICAgICAgc3RhdHVzOiBhZGRlbmR1bS5jb250cmFjdFN0YXR1c1xyXG4gICAgICAgICAgfSk7XHJcblxyXG4gICAgICAgICAgYWN0aXZlQWRkZW5kdW1JbmRleCsrOyAvLyBJbmNyZW1lbnRhciBhcGVuYXMgcGFyYSBhZGl0aXZvcyBhdGl2b3NcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENhbGN1bGFyIHRvdGFsIGRvIElSXHJcbiAgICBjb25zdCB0b3RhbElSID0gZGV0YWlscy5yZWR1Y2UoKHN1bSwgZGV0YWlsKSA9PiBzdW0gKyBkZXRhaWwuaXJWYWx1ZSwgMCk7XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgZGV0YWlscyxcclxuICAgICAgdG90YWxJUixcclxuICAgICAgY29udHJhY3RUeXBlOiBjb250cmFjdC50YWdzIHx8ICdNVVRVTydcclxuICAgIH07XHJcbiAgfSwgW2NvbnRyYWN0RGF0YV0pO1xyXG5cclxuICAvLyBDYWxjdWxhciB2YWxvciBhcMOzcyBkZXNjb250byBkZSBJUiBlIHZhbGlkYXIgcGFyYSBTQ1BcclxuICBjb25zdCBpckRlc2NvbnRvID0gd2F0Y2goXCJpckRlc2NvbnRvXCIpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coXCLwn5SNIFVzZUVmZmVjdCBTQ1AgLSBDb25kacOnw7VlczpcIik7XHJcbiAgICBjb25zb2xlLmxvZyhcIiAgbW9kYWxpZGFkZSA9PT0gJ1NDUCc6XCIsIG1vZGFsaWRhZGUgPT09IFwiU0NQXCIpO1xyXG4gICAgY29uc29sZS5sb2coXCIgIHZhbG9ySW52ZXN0aW1lbnRvOlwiLCB2YWxvckludmVzdGltZW50byk7XHJcbiAgICBjb25zb2xlLmxvZyhcIiAgdmFsb3JJbnZlc3RpbWVudG8udHJpbSgpICE9PSAnJzpcIiwgdmFsb3JJbnZlc3RpbWVudG8gJiYgdmFsb3JJbnZlc3RpbWVudG8udHJpbSgpICE9PSBcIlwiKTtcclxuICAgIGNvbnNvbGUubG9nKFwiICBpckRlc2NvbnRvOlwiLCBpckRlc2NvbnRvKTtcclxuICAgIGNvbnNvbGUubG9nKFwiICBjb250cmFjdERldGFpbHM/LnRvdGFsSVI6XCIsIGNvbnRyYWN0RGV0YWlscz8udG90YWxJUik7XHJcblxyXG4gICAgaWYgKG1vZGFsaWRhZGUgPT09IFwiU0NQXCIgJiYgdmFsb3JJbnZlc3RpbWVudG8gJiYgdmFsb3JJbnZlc3RpbWVudG8udHJpbSgpICE9PSBcIlwiICYmIGlyRGVzY29udG8gJiYgY29udHJhY3REZXRhaWxzPy50b3RhbElSKSB7XHJcbiAgICAgIGNvbnN0IHZhbG9yTnVtZXJpY28gPSBmb3JtYXROdW1iZXJWYWx1ZSh2YWxvckludmVzdGltZW50bykgfHwgMDtcclxuICAgICAgY29uc3QgdmFsb3JBcG9zRGVzY29udG8gPSB2YWxvck51bWVyaWNvIC0gY29udHJhY3REZXRhaWxzLnRvdGFsSVI7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZyhcIj09PSBDw4FMQ1VMTyBTQ1AgQ09NIERFU0NPTlRPIElSID09PVwiKTtcclxuICAgICAgY29uc29sZS5sb2coXCJWYWxvciBvcmlnaW5hbDpcIiwgdmFsb3JOdW1lcmljbyk7XHJcbiAgICAgIGNvbnNvbGUubG9nKFwiSVIgYSBkZXNjb250YXI6XCIsIGNvbnRyYWN0RGV0YWlscy50b3RhbElSKTtcclxuICAgICAgY29uc29sZS5sb2coXCJWYWxvciBhcMOzcyBkZXNjb250bzpcIiwgdmFsb3JBcG9zRGVzY29udG8pO1xyXG5cclxuICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gdmFsb3IgYXDDs3MgZGVzY29udG8gw6kgZGl2aXPDrXZlbCBwb3IgNTAwMFxyXG4gICAgICBjb25zdCByZXN0byA9IHZhbG9yQXBvc0Rlc2NvbnRvICUgNTAwMDtcclxuICAgICAgaWYgKHJlc3RvICE9PSAwKSB7XHJcbiAgICAgICAgLy8gQXJyZWRvbmRhciBwYXJhIEJBSVhPIChtZW5vcyBjb3RhcykgLSBwZWdhciBhcGVuYXMgY290YXMgY29tcGxldGFzXHJcbiAgICAgICAgY29uc3QgY290YXNDb21wbGV0YXMgPSBNYXRoLmZsb29yKHZhbG9yQXBvc0Rlc2NvbnRvIC8gNTAwMCk7XHJcbiAgICAgICAgY29uc3QgdmFsb3JDb3Rhc0NvbXBsZXRhcyA9IGNvdGFzQ29tcGxldGFzICogNTAwMDtcclxuICAgICAgICBjb25zdCB2YWxvckNvbXBsZW1lbnRhck5lY2Vzc2FyaW8gPSB2YWxvckFwb3NEZXNjb250byAtIHZhbG9yQ290YXNDb21wbGV0YXM7XHJcblxyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiUmVzdG86XCIsIHJlc3RvKTtcclxuICAgICAgICBjb25zb2xlLmxvZyhcIkNvdGFzIGNvbXBsZXRhczpcIiwgY290YXNDb21wbGV0YXMpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKFwiVmFsb3IgZGFzIGNvdGFzIGNvbXBsZXRhczpcIiwgdmFsb3JDb3Rhc0NvbXBsZXRhcyk7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCJWYWxvciBjb21wbGVtZW50YXIgbmVjZXNzw6FyaW86XCIsIHZhbG9yQ29tcGxlbWVudGFyTmVjZXNzYXJpbyk7XHJcblxyXG4gICAgICAgIC8vIEF0dWFsaXphciBvIHZhbG9yIGRvIGludmVzdGltZW50byBwYXJhIG8gdmFsb3IgZGFzIGNvdGFzIGNvbXBsZXRhcyArIElSXHJcbiAgICAgICAgY29uc3Qgbm92b1ZhbG9ySW52ZXN0aW1lbnRvID0gdmFsb3JDb3Rhc0NvbXBsZXRhcyArIGNvbnRyYWN0RGV0YWlscy50b3RhbElSO1xyXG4gICAgICAgIGNvbnN0IHZhbG9yRm9ybWF0YWRvID0gdmFsdWVNYXNrKChub3ZvVmFsb3JJbnZlc3RpbWVudG8gKiAxMDApLnRvU3RyaW5nKCkpO1xyXG4gICAgICAgIHNldFZhbHVlKFwidmFsb3JJbnZlc3RpbWVudG9cIiwgdmFsb3JGb3JtYXRhZG8pO1xyXG5cclxuICAgICAgICAvLyBBdHVhbGl6YXIgcXVhbnRpZGFkZSBkZSBjb3Rhc1xyXG4gICAgICAgIHNldFZhbHVlKFwicXVvdGFRdWFudGl0eVwiLCBjb3Rhc0NvbXBsZXRhcy50b1N0cmluZygpKTtcclxuXHJcbiAgICAgICAgc2V0VmFsb3JDb21wbGVtZW50YXIodmFsb3JDb21wbGVtZW50YXJOZWNlc3NhcmlvKTtcclxuICAgICAgICBzZXRTaG93Q29tcGxlbWVudE1lc3NhZ2UodHJ1ZSk7XHJcbiAgICAgICAgY29uc29sZS5sb2coXCLinIUgc2hvd0NvbXBsZW1lbnRNZXNzYWdlIGF0aXZhZG8gLSB2YWxvciBwYXJjaWFsIHBlcm1pdGlkb1wiKTtcclxuXHJcbiAgICAgICAgdG9hc3Qud2FybmluZyhg4pqg77iPIFZhbG9yIGFqdXN0YWRvIHBhcmEgJHtjb3Rhc0NvbXBsZXRhc30gY290YXMgY29tcGxldGFzLiBWYWxvciBjb21wbGVtZW50YXIgZGUgJHtuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ3B0LUJSJywgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3k6ICdCUkwnIH0pLmZvcm1hdCh2YWxvckNvbXBsZW1lbnRhck5lY2Vzc2FyaW8pfSBzZXLDoSBuZWNlc3PDoXJpby5gKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRTaG93Q29tcGxlbWVudE1lc3NhZ2UoZmFsc2UpO1xyXG4gICAgICAgIHNldFZhbG9yQ29tcGxlbWVudGFyKDApO1xyXG4gICAgICB9XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRTaG93Q29tcGxlbWVudE1lc3NhZ2UoZmFsc2UpO1xyXG4gICAgICBzZXRWYWxvckNvbXBsZW1lbnRhcigwKTtcclxuICAgIH1cclxuICB9LCBbbW9kYWxpZGFkZSwgdmFsb3JJbnZlc3RpbWVudG8sIGlyRGVzY29udG8sIGNvbnRyYWN0RGV0YWlscz8udG90YWxJUiwgc2V0VmFsdWVdKTtcclxuXHJcbiAgLy8gVmFsaWRhw6fDo28gZW0gdGVtcG8gcmVhbCBwYXJhIFNDUFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAodmFsb3JJbnZlc3RpbWVudG8gJiYgbW9kYWxpZGFkZSkge1xyXG4gICAgICB2YWxpZGF0ZVNjcFZhbHVlKHZhbG9ySW52ZXN0aW1lbnRvLCBtb2RhbGlkYWRlKTtcclxuICAgIH1cclxuICB9LCBbdmFsb3JJbnZlc3RpbWVudG8sIG1vZGFsaWRhZGUsIHZhbGlkYXRlU2NwVmFsdWUsIHNob3dDb21wbGVtZW50TWVzc2FnZV0pO1xyXG5cclxuICAvLyBSZXNldCBJUiBzdGF0ZXMgcXVhbmRvIG1vZGFsaWRhZGUgbXVkYXJcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKG1vZGFsaWRhZGUgPT09IFwiU0NQXCIpIHtcclxuICAgICAgLy8gUGFyYSBTQ1AsIG7Do28gcHJlY2lzYSBkZSBJUlxyXG4gICAgICBzZXRJckNhbGN1bGFkbyh0cnVlKTtcclxuICAgICAgc2V0SXJPcGNhb1NlbGVjaW9uYWRhKHRydWUpO1xyXG4gICAgICBzZXRWYWx1ZShcImlyRGVwb3NpdG9cIiwgZmFsc2UpO1xyXG4gICAgICBzZXRWYWx1ZShcImlyRGVzY29udG9cIiwgZmFsc2UpO1xyXG4gICAgICBzZXRWYWxvck9yaWdpbmFsSW52ZXN0aW1lbnRvKFwiXCIpOyAvLyBSZXNldCB2YWxvciBvcmlnaW5hbFxyXG4gICAgfSBlbHNlIGlmIChtb2RhbGlkYWRlID09PSBcIk1VVFVPXCIpIHtcclxuICAgICAgLy8gUGFyYSBNVVRVTywgcmVzZXRhciBlc3RhZG9zXHJcbiAgICAgIHNldElyQ2FsY3VsYWRvKGZhbHNlKTtcclxuICAgICAgc2V0SXJPcGNhb1NlbGVjaW9uYWRhKGZhbHNlKTtcclxuICAgICAgc2V0QWxpcXVvdGFJUihcIlwiKTtcclxuICAgICAgc2V0VmFsdWUoXCJpckRlcG9zaXRvXCIsIGZhbHNlKTtcclxuICAgICAgc2V0VmFsdWUoXCJpckRlc2NvbnRvXCIsIGZhbHNlKTtcclxuICAgICAgc2V0VmFsb3JPcmlnaW5hbEludmVzdGltZW50byhcIlwiKTsgLy8gUmVzZXQgdmFsb3Igb3JpZ2luYWxcclxuICAgIH1cclxuICB9LCBbbW9kYWxpZGFkZSwgc2V0VmFsdWVdKTtcclxuXHJcbiAgLy8gRnVuw6fDo28gYXV4aWxpYXIgcGFyYSBjb252ZXJ0ZXIgc3RyaW5nIGRlIHZhbG9yIHBhcmEgbsO6bWVyb1xyXG4gIGZ1bmN0aW9uIHBhcnNlVmFsb3IodmFsb3I6IHN0cmluZyk6IG51bWJlciB7XHJcbiAgICBpZiAoIXZhbG9yKSByZXR1cm4gMDtcclxuICAgIC8vIFVzYXIgYSBmdW7Dp8OjbyBmb3JtYXROdW1iZXJWYWx1ZSBxdWUgasOhIHRyYXRhIGNvcnJldGFtZW50ZSB2YWxvcmVzIGJyYXNpbGVpcm9zXHJcbiAgICByZXR1cm4gZm9ybWF0TnVtYmVyVmFsdWUodmFsb3IpIHx8IDA7XHJcbiAgfVxyXG5cclxuXHJcblxyXG4gIC8vIFZhbG9yIGludmVzdGlkbyBwcmVlbmNoaWRvXHJcbiAgY29uc3QgdmFsb3JJbnZlc3RpZG8gPSBwYXJzZVZhbG9yKHdhdGNoKFwidmFsb3JJbnZlc3RpbWVudG9cIikpO1xyXG4gIC8vIEFsw61xdW90YSBlbSBuw7ptZXJvXHJcbiAgY29uc3QgYWxpcXVvdGFOdW1iZXIgPSBhbGlxdW90YUlSID8gcGFyc2VGbG9hdChhbGlxdW90YUlSLnJlcGxhY2UoJyUnLCAnJykucmVwbGFjZSgnLCcsICcuJykpIDogMDtcclxuICAvLyBWYWxvciB0b3RhbCBkZSBJUlxyXG4gIGNvbnN0IHZhbG9yVG90YWxJUiA9IHZhbG9ySW52ZXN0aWRvICYmIGFsaXF1b3RhTnVtYmVyID8gKHZhbG9ySW52ZXN0aWRvICogKGFsaXF1b3RhTnVtYmVyIC8gMTAwKSkgOiAwO1xyXG5cclxuICAvLyBQQVJURSAxOiBEYWRvcyBwZXNzb2FpcywgYmFuY8Ohcmlvcywgb2JzZXJ2YcOnw7Vlc1xyXG4gIGNvbnN0IHJlbmRlclN0ZXAxID0gKCkgPT4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNFwiPkRhZG9zIFBlc3NvYWlzPC9oMz5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzFDMUMxQ10gcm91bmRlZC1sZyBwLTggcmVsYXRpdmUgb3ZlcmZsb3ctdmlzaWJsZSBib3JkZXIgYm9yZGVyLVsjRkY5OTAwXSBtYi04XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC00IHctZnVsbFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy0xLzIgbWItNFwiPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIG1iLTFcIj5UaXBvIGRlIENvbnRyYXRvPC9wPlxyXG4gICAgICAgICAgICA8U2VsZWN0Q3VzdG9tXHJcbiAgICAgICAgICAgICAgdmFsdWU9e3dhdGNoKFwidGlwb0NvbnRyYXRvXCIpfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0VmFsdWUoXCJ0aXBvQ29udHJhdG9cIiwgZS50YXJnZXQudmFsdWUsIHsgc2hvdWxkVmFsaWRhdGU6IHRydWUgfSl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPXtcInBmXCJ9PlBlc3NvYSBGw61zaWNhPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17XCJwalwifT5QZXNzb2EgSnVyw61kaWNhPC9vcHRpb24+XHJcbiAgICAgICAgICAgIDwvU2VsZWN0Q3VzdG9tPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBtYi00XCI+XHJcbiAgICAgICAgICAgIDxJbnB1dFRleHRcclxuICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgbmFtZT1cIm5vbWVDb21wbGV0b1wiXHJcbiAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMubm9tZUNvbXBsZXRvfVxyXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy5ub21lQ29tcGxldG8/Lm1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgbGFiZWw9XCJOb21lIENvbXBsZXRvXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGdhcC00IG1iLTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy0xLzJcIj5cclxuICAgICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgICBuYW1lPVwiaWRlbnRpZGFkZVwiXHJcbiAgICAgICAgICAgICAgICB3aWR0aD1cIjEwMCVcIlxyXG4gICAgICAgICAgICAgICAgZXJyb3I9eyEhZXJyb3JzLmlkZW50aWRhZGV9XHJcbiAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2U9e2Vycm9ycz8uaWRlbnRpZGFkZT8ubWVzc2FnZX1cclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiSWRlbnRpZGFkZVwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctMS8yXCI+XHJcbiAgICAgICAgICAgICAgPElucHV0VGV4dFxyXG4gICAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImNlbHVsYXJcIlxyXG4gICAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICAgIG1heExlbmd0aD17MTh9XHJcbiAgICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMuY2VsdWxhcn1cclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy5jZWx1bGFyPy5tZXNzYWdlfVxyXG4gICAgICAgICAgICAgICAgbGFiZWw9XCJDZWx1bGFyXCJcclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHtcclxuICAgICAgICAgICAgICAgICAgc2V0VmFsdWUoXCJjZWx1bGFyXCIsIHBob25lV2l0aENvdW50cnlNYXNrKGUudGFyZ2V0LnZhbHVlKSwgeyBzaG91bGRWYWxpZGF0ZTogdHJ1ZSB9KTtcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBnYXAtNCBtYi00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctMS8yXCI+XHJcbiAgICAgICAgICAgICAgPElucHV0VGV4dFxyXG4gICAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImNwZlwiXHJcbiAgICAgICAgICAgICAgICB3aWR0aD1cIjEwMCVcIlxyXG4gICAgICAgICAgICAgICAgZXJyb3I9eyEhZXJyb3JzLmNwZn1cclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy5jcGY/Lm1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkNQRi9DTlBKXCJcclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHtcclxuICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSB3YXRjaChcInRpcG9Db250cmF0b1wiKSA9PT0gXCJwalwiID8gY25wak1hc2soZS50YXJnZXQudmFsdWUpIDogY3BmTWFzayhlLnRhcmdldC52YWx1ZSk7XHJcbiAgICAgICAgICAgICAgICAgIHNldFZhbHVlKFwiY3BmXCIsIHZhbHVlLCB7IHNob3VsZFZhbGlkYXRlOiB0cnVlIH0pO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy0xLzJcIj5cclxuICAgICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgICBuYW1lPVwiZGF0YU5hc2NpbWVudG9cIlxyXG4gICAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICAgIGVycm9yPXshIWVycm9ycy5kYXRhTmFzY2ltZW50b31cclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy5kYXRhTmFzY2ltZW50bz8ubWVzc2FnZX1cclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiRGF0YSBkZSBOYXNjaW1lbnRvIChvcGNpb25hbClcIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBtYi00XCI+XHJcbiAgICAgICAgICAgIDxJbnB1dFRleHRcclxuICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgbmFtZT1cIm5vbWVNYWVcIlxyXG4gICAgICAgICAgICAgIHdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgICAgZXJyb3I9eyEhZXJyb3JzLm5vbWVNYWV9XHJcbiAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlPXtlcnJvcnM/Lm5vbWVNYWU/Lm1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgbGFiZWw9XCJOb21lIGRhIE3Do2UgKG9wY2lvbmFsKVwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG1iLTRcIj5cclxuICAgICAgICAgICAgPElucHV0VGV4dFxyXG4gICAgICAgICAgICAgIHJlZ2lzdGVyPXtyZWdpc3Rlcn1cclxuICAgICAgICAgICAgICBuYW1lPVwiZW1haWxcIlxyXG4gICAgICAgICAgICAgIHdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgICAgZXJyb3I9eyEhZXJyb3JzLmVtYWlsfVxyXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy5lbWFpbD8ubWVzc2FnZX1cclxuICAgICAgICAgICAgICBsYWJlbD1cIkUtbWFpbFwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBnYXAtNCBtYi00XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctMS8yXCI+XHJcbiAgICAgICAgICAgICAgPElucHV0VGV4dFxyXG4gICAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImNlcFwiXHJcbiAgICAgICAgICAgICAgICB3aWR0aD1cIjEwMCVcIlxyXG4gICAgICAgICAgICAgICAgZXJyb3I9eyEhZXJyb3JzLmNlcH1cclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy5jZXA/Lm1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkNFUFwiXHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17ZSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIHNldFZhbHVlKFwiY2VwXCIsIGNlcE1hc2soZS50YXJnZXQudmFsdWUpLCB7IHNob3VsZFZhbGlkYXRlOiB0cnVlIH0pO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy0xLzJcIj5cclxuICAgICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgICBuYW1lPVwiY2lkYWRlXCJcclxuICAgICAgICAgICAgICAgIHdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMuY2lkYWRlfVxyXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlPXtlcnJvcnM/LmNpZGFkZT8ubWVzc2FnZX1cclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiQ2lkYWRlXCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGdhcC00IG1iLTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy0xLzJcIj5cclxuICAgICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgICBuYW1lPVwiZXN0YWRvXCJcclxuICAgICAgICAgICAgICAgIHdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMuZXN0YWRvfVxyXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlPXtlcnJvcnM/LmVzdGFkbz8ubWVzc2FnZX1cclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiRXN0YWRvXCJcclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZXg6IFNDXCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWItNFwiPlxyXG4gICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgIG5hbWU9XCJlbmRlcmVjb1wiXHJcbiAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMuZW5kZXJlY299XHJcbiAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlPXtlcnJvcnM/LmVuZGVyZWNvPy5tZXNzYWdlfVxyXG4gICAgICAgICAgICAgIGxhYmVsPVwiRW5kZXJlw6dvXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGdhcC00IG1iLTRcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbWQ6dy0xLzJcIj5cclxuICAgICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgICBuYW1lPVwibnVtZXJvXCJcclxuICAgICAgICAgICAgICAgIHdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMubnVtZXJvfVxyXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlPXtlcnJvcnM/Lm51bWVybz8ubWVzc2FnZX1cclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiTsO6bWVyb1wiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctMS8yXCI+XHJcbiAgICAgICAgICAgICAgPElucHV0VGV4dFxyXG4gICAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImNvbXBsZW1lbnRvXCJcclxuICAgICAgICAgICAgICAgIHdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMuY29tcGxlbWVudG99XHJcbiAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2U9e2Vycm9ycz8uY29tcGxlbWVudG8/Lm1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkNvbXBsZW1lbnRvXCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5EYWRvcyBCYW5jw6FyaW9zPC9oMz5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzFDMUMxQ10gcm91bmRlZC1sZyBwLTggcmVsYXRpdmUgb3ZlcmZsb3ctdmlzaWJsZSBib3JkZXIgYm9yZGVyLVsjRkY5OTAwXSBtYi04XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGdhcC00IHctZnVsbCBtYi00XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTEvMlwiPlxyXG4gICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgIG5hbWU9XCJiYW5jb1wiXHJcbiAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMuYmFuY299XHJcbiAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlPXtlcnJvcnM/LmJhbmNvPy5tZXNzYWdlfVxyXG4gICAgICAgICAgICAgIGxhYmVsPVwiTm9tZSBkbyBCYW5jb1wiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIG1kOnctMS8yXCI+XHJcbiAgICAgICAgICAgIDxJbnB1dFRleHRcclxuICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgbmFtZT1cImNvbnRhXCJcclxuICAgICAgICAgICAgICB3aWR0aD1cIjEwMCVcIlxyXG4gICAgICAgICAgICAgIGVycm9yPXshIWVycm9ycy5jb250YX1cclxuICAgICAgICAgICAgICBlcnJvck1lc3NhZ2U9e2Vycm9ycz8uY29udGE/Lm1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgbGFiZWw9XCJDb250YVwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cgZ2FwLTQgdy1mdWxsXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTEvMlwiPlxyXG4gICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgIG5hbWU9XCJhZ2VuY2lhXCJcclxuICAgICAgICAgICAgICB3aWR0aD1cIjEwMCVcIlxyXG4gICAgICAgICAgICAgIGVycm9yPXshIWVycm9ycy5hZ2VuY2lhfVxyXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy5hZ2VuY2lhPy5tZXNzYWdlfVxyXG4gICAgICAgICAgICAgIGxhYmVsPVwiQWfDqm5jaWFcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LTEvMlwiPlxyXG4gICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgIG5hbWU9XCJjaGF2ZVBpeFwiXHJcbiAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMuY2hhdmVQaXh9XHJcbiAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlPXtlcnJvcnM/LmNoYXZlUGl4Py5tZXNzYWdlfVxyXG4gICAgICAgICAgICAgIGxhYmVsPVwiQ2hhdmUgUElYXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTRcIj5PYnNlcnZhw6fDtWVzPC9oMz5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1bIzFDMUMxQ10gcm91bmRlZC1sZyBwLTggcmVsYXRpdmUgb3ZlcmZsb3ctdmlzaWJsZSBib3JkZXIgYm9yZGVyLVsjRkY5OTAwXSBtYi04XCI+XHJcbiAgICAgICAgPHRleHRhcmVhXHJcbiAgICAgICAgICB7Li4ucmVnaXN0ZXIoXCJvYnNlcnZhY29lc1wiKX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLTMyIHJvdW5kZWQtbGcgYmctYmxhY2sgYm9yZGVyIGJvcmRlci1bI0ZGOTkwMF0gdGV4dC13aGl0ZSBwLTQgcmVzaXplLW5vbmUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLVsjRkY5OTAwXVwiXHJcbiAgICAgICAgICBwbGFjZWhvbGRlcj1cIk9ic2VydmHDp8O1ZXNcIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICAge2Vycm9ycy5vYnNlcnZhY29lcyAmJiAoXHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC14c1wiPntlcnJvcnMub2JzZXJ2YWNvZXMubWVzc2FnZX08L3NwYW4+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmRcIj5cclxuXHJcbiAgICAgICAgPEJ1dHRvbiBzaXplPVwibGdcIiB0eXBlPVwiYnV0dG9uXCIgY2xhc3NOYW1lPVwiYmctWyNGRjk5MDBdIHRleHQtd2hpdGUgcHgtOCBweS0yIHJvdW5kZWQtbGcgaG92ZXI6YmctWyNlNjhhMDBdXCIgb25DbGljaz17aGFuZGxlTmV4dH0+XHJcbiAgICAgICAgICBQcsOzeGltb1xyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcblxyXG4gIC8vIFBBUlRFIDI6IERhZG9zIGRlIGludmVzdGltZW50bywgcmVzdW1vL2NhbGN1bGFkb3JhLCBkZXRhbGhhbWVudG9cclxuICBjb25zdCByZW5kZXJTdGVwMiA9ICgpID0+IChcclxuICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXQob25TdWJtaXQsIChlcnJvcnMpID0+IHtcclxuICAgICAgY29uc29sZS5sb2coXCJFcnJvcyBkZSB2YWxpZGHDp8OjbzpcIiwgZXJyb3JzKTtcclxuICAgICAgdG9hc3QuZXJyb3IoXCJQb3IgZmF2b3IsIHByZWVuY2hhIHRvZG9zIG9zIGNhbXBvcyBvYnJpZ2F0w7NyaW9zXCIpO1xyXG4gICAgfSl9PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXktOCBmbGV4IGZsZXgtY29sIGdhcC00XCI+XHJcbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBtYi0xIGJsb2NrXCI+TW9kYWxpZGFkZSBkZSBJbnZlc3RpbWVudG88L2xhYmVsPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOnctMS8zXCI+XHJcbiAgICAgICAgICAgIDxTZWxlY3RDdXN0b21cclxuICAgICAgICAgICAgICB2YWx1ZT17d2F0Y2goXCJtb2RhbGlkYWRlXCIpfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG5ld01vZGFsaXR5ID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICAgICAgICAgICAgICAvLyBWYWxpZGFyIHNlIGVzdMOhIHRlbnRhbmRvIG11ZGFyIGRlIFNDUCBwYXJhIE1VVFVPXHJcbiAgICAgICAgICAgICAgICBpZiAoY3VycmVudENvbnRyYWN0TW9kYWxpdHkgPT09IFwiU0NQXCIgJiYgbmV3TW9kYWxpdHkgPT09IFwiTVVUVU9cIikge1xyXG4gICAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcihcIk7Do28gw6kgcG9zc8OtdmVsIGFsdGVyYXIgY29udHJhdG9zIFNDUCBwYXJhIG1vZGFsaWRhZGUgTcO6dHVvXCIpO1xyXG4gICAgICAgICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBzZXRWYWx1ZShcIm1vZGFsaWRhZGVcIiwgbmV3TW9kYWxpdHksIHsgc2hvdWxkVmFsaWRhdGU6IHRydWUgfSk7XHJcbiAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJNVVRVT1wiPk3DunR1bzwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJTQ1BcIj5TQ1A8L29wdGlvbj5cclxuICAgICAgICAgICAgPC9TZWxlY3RDdXN0b20+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtWyNGRjk5MDBdIHRleHQteHMgbXQtMVwiPipBbyBhbHRlcmFyIG1vZGFsaWRhZGUsIHByZXNzaW9uZSBvIGJvdMOjbyBjYWxjdWxhciBJUjwvc3Bhbj5cclxuICAgICAgICAgICAgey8qIEF2aXNvIHF1YW5kbyBjb250cmF0byBhdHVhbCDDqSBTQ1AgKi99XHJcbiAgICAgICAgICAgIHtjdXJyZW50Q29udHJhY3RNb2RhbGl0eSA9PT0gXCJTQ1BcIiAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy15ZWxsb3ctOTAwIGJvcmRlciBib3JkZXIteWVsbG93LTUwMCByb3VuZGVkLWxnIHAtMyBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy0yMDAgdGV4dC14c1wiPlxyXG4gICAgICAgICAgICAgICAgICDimqDvuI8gPHN0cm9uZz5Db250cmF0byBhdHVhbCDDqSBTQ1A6PC9zdHJvbmc+IE7Do28gw6kgcG9zc8OtdmVsIGFsdGVyYXIgcGFyYSBtb2RhbGlkYWRlIE3DunR1by5cclxuICAgICAgICAgICAgICAgICAgQXBlbmFzIHVwZ3JhZGVzIGRlbnRybyBkYSBtZXNtYSBtb2RhbGlkYWRlIG91IGRlIE3DunR1byBwYXJhIFNDUCBzw6NvIHBlcm1pdGlkb3MuXHJcbiAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIHsvKiBDYW1wbyBkZSB2YWxvciBwYXJhIFNDUCAqL31cclxuICAgICAgICAgIHt3YXRjaChcIm1vZGFsaWRhZGVcIikgPT09IFwiU0NQXCIgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6dy0xLzMgbXQtMlwiPlxyXG4gICAgICAgICAgICAgIDxJbnB1dFRleHRcclxuICAgICAgICAgICAgICAgIHJlZ2lzdGVyPXtyZWdpc3Rlcn1cclxuICAgICAgICAgICAgICAgIG5hbWU9XCJ2YWxvckludmVzdGltZW50b1wiXHJcbiAgICAgICAgICAgICAgICB3aWR0aD1cIjEwMCVcIlxyXG4gICAgICAgICAgICAgICAgZXJyb3I9eyEhZXJyb3JzLnZhbG9ySW52ZXN0aW1lbnRvIHx8ICEhc2NwVmFsaWRhdGlvbkVycm9yfVxyXG4gICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlPXtlcnJvcnM/LnZhbG9ySW52ZXN0aW1lbnRvPy5tZXNzYWdlIHx8IHNjcFZhbGlkYXRpb25FcnJvcn1cclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiVmFsb3IgZG8gSW52ZXN0aW1lbnRvXCJcclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZXg6IFIkIDUwLjAwMCwwMFwiXHJcbiAgICAgICAgICAgICAgICBzZXRWYWx1ZT17ZSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiSW5wdXQgU0NQIC0gVmFsb3IgcmVjZWJpZG86XCIsIGUpO1xyXG4gICAgICAgICAgICAgICAgICBjb25zdCBtYXNrZWRWYWx1ZSA9IGUgPyB2YWx1ZU1hc2soZSkgOiBcIlwiO1xyXG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIklucHV0IFNDUCAtIFZhbG9yIGNvbSBtw6FzY2FyYTpcIiwgbWFza2VkVmFsdWUpO1xyXG4gICAgICAgICAgICAgICAgICBzZXRWYWx1ZShcInZhbG9ySW52ZXN0aW1lbnRvXCIsIG1hc2tlZFZhbHVlLCB7IHNob3VsZFZhbGlkYXRlOiB0cnVlIH0pO1xyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIHsvKiBNZW5zYWdlbSBkZSB2YWxvciBwYXJjaWFsIHBlcm1pdGlkbyBxdWFuZG8gaMOhIGRlcMOzc2l0byBhZGljaW9uYWwgKi99XHJcbiAgICAgICAgICAgICAge3Nob3dDb21wbGVtZW50TWVzc2FnZSAmJiBzY3BWYWxpZGF0aW9uRXJyb3IgPT09IFwiXCIgJiYgKFxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTQwMCB0ZXh0LXhzIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAg4pyFIFZhbG9yIHBhcmNpYWwgcGVybWl0aWRvIChkZXDDs3NpdG8gYWRpY2lvbmFsIHNlcsOhIG5lY2Vzc8OhcmlvKVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgICAgey8qIENhbGN1bGFkb3JhIGRlIElSIC0gYXBlbmFzIHBhcmEgTVVUVU8gKi99XHJcbiAgICAgICAgICB7d2F0Y2goXCJtb2RhbGlkYWRlXCIpID09PSBcIk1VVFVPXCIgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgbWQ6dy0xLzNcIj5cclxuICAgICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJidXR0b25cIiBjbGFzc05hbWU9XCJiZy1bI0ZGOTkwMF0gdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1sZyBob3ZlcjpiZy1bI2U2OGEwMF0gdy1mdWxsXCIgb25DbGljaz17KCkgPT4gY2FsY3VsYXJBbGlxdW90YUlSKCl9PkNhbGN1bGFyIElSPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICB7LyogUmVzdW1vL2NhbGN1bGFkb3JhIC0gYXBlbmFzIHBhcmEgTVVUVU8gKi99XHJcbiAgICAgICAge3dhdGNoKFwibW9kYWxpZGFkZVwiKSA9PT0gXCJNVVRVT1wiICYmIChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmxhY2sgYm9yZGVyIGJvcmRlci1bI0ZGOTkwMF0gcm91bmRlZC1sZyBwLTQgbWItNCBtZDp3LTEvM1wiPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQteHMgbWItMVwiPlZhbG9yIGludmVzdGlkbzoge3ZhbG9ySW52ZXN0aWRvID8gdmFsb3JJbnZlc3RpZG8udG9Mb2NhbGVTdHJpbmcoJ3B0LUJSJywgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3k6ICdCUkwnIH0pIDogJ1IkIDAsMDAnfTwvcD5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGRcIj5WYWxvciB0b3RhbCBkZSBJUjoge3ZhbG9yVG90YWxJUiA/IHZhbG9yVG90YWxJUi50b0xvY2FsZVN0cmluZygncHQtQlInLCB7IHN0eWxlOiAnY3VycmVuY3knLCBjdXJyZW5jeTogJ0JSTCcgfSkgOiAnUiQgMCwwMCd9PC9wPlxyXG4gICAgICAgICAgICB7YWxpcXVvdGFJUiAmJiAoXHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1bI0ZGOTkwMF0gZm9udC1ib2xkIG10LTJcIj5BbMOtcXVvdGEgZG8gSVIgY2FsY3VsYWRhOiB7YWxpcXVvdGFJUn08L3A+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG4gICAgICAgIHsvKiBEZXRhbGhhbWVudG8gLSBwYXJhIE1VVFVPIG91IHF1YW5kbyBjb250cmF0byBhbnRlcmlvciBlcmEgTVVUVU8gKi99XHJcbiAgICAgICAgeyh3YXRjaChcIm1vZGFsaWRhZGVcIikgPT09IFwiTVVUVU9cIiB8fCBjb250cmFjdERldGFpbHM/LmNvbnRyYWN0VHlwZSA9PT0gXCJNVVRVT1wiKSAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBmbGV4IGZsZXgtY29sIGdhcC00XCI+XHJcbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+RGV0YWxoYW1lbnRvPC9oND5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XHJcbiAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC13aGl0ZSB0ZXh0LXhzIGJvcmRlciBib3JkZXItWyNGRjk5MDBdIHJvdW5kZWQteGwgYmctdHJhbnNwYXJlbnRcIj5cclxuICAgICAgICAgICAgICA8dGhlYWQ+XHJcbiAgICAgICAgICAgICAgICA8dHIgY2xhc3NOYW1lPVwiYmctWyMyMzIzMjNdXCI+XHJcbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTIgYm9yZGVyLXIgYm9yZGVyLVsjRkY5OTAwXSBmb250LXNlbWlib2xkXCI+VGlwbyBkZSBDb250cmF0bzwvdGg+XHJcbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0yIHB5LTIgYm9yZGVyLXIgYm9yZGVyLVsjRkY5OTAwXSBmb250LXNlbWlib2xkXCI+VmFsb3IgZG8gQ29udHJhdG88L3RoPlxyXG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtMiBweS0yIGJvcmRlci1yIGJvcmRlci1bI0ZGOTkwMF0gZm9udC1zZW1pYm9sZFwiPkRpYXMgUmVudGFiaWxpemFkb3M8L3RoPlxyXG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtMiBweS0yIGJvcmRlci1yIGJvcmRlci1bI0ZGOTkwMF0gZm9udC1zZW1pYm9sZFwiPlJlbnRhYmlsaWRhZGU8L3RoPlxyXG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtMiBweS0yIGJvcmRlci1yIGJvcmRlci1bI0ZGOTkwMF0gZm9udC1zZW1pYm9sZFwiPlRheGEgZGUgSVI8L3RoPlxyXG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtMiBweS0yIGJvcmRlci1yIGJvcmRlci1bI0ZGOTkwMF0gZm9udC1zZW1pYm9sZFwiPlZhbG9yIGRvIElSPC90aD5cclxuICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTIgcHktMiBmb250LXNlbWlib2xkXCI+QW5leG9zPC90aD5cclxuICAgICAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICAgICAgPC90aGVhZD5cclxuICAgICAgICAgICAgICA8dGJvZHk+XHJcbiAgICAgICAgICAgICAgICB7aXNMb2FkaW5nQ29udHJhY3QgPyAoXHJcbiAgICAgICAgICAgICAgICAgIDx0cj5cclxuICAgICAgICAgICAgICAgICAgICA8dGQgY29sU3Bhbj17N30gY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHgtMiBweS00XCI+Q2FycmVnYW5kbyBkYWRvcyBkbyBjb250cmF0by4uLjwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICAgICAgICApIDogY29udHJhY3REZXRhaWxzID8gKFxyXG4gICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBSZW5kZXJpemFyIGNvbnRyYXRvIHByaW5jaXBhbCBlIGFkZW5kb3MgLSBhcGVuYXMgY29udHJhdG9zIGF0aXZvcyAqL31cclxuICAgICAgICAgICAgICAgICAgICB7Y29udHJhY3REZXRhaWxzLmRldGFpbHMubGVuZ3RoID4gMCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbnRyYWN0RGV0YWlscy5kZXRhaWxzLm1hcCgoZGV0YWlsOiBhbnksIGluZGV4OiBudW1iZXIpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgIDx0ciBrZXk9e2luZGV4fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTIgcHktMiBib3JkZXItciBib3JkZXItWyNGRjk5MDBdXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2RldGFpbC50eXBlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIGJvcmRlci1yIGJvcmRlci1bI0ZGOTkwMF1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bmV3IEludGwuTnVtYmVyRm9ybWF0KCdwdC1CUicsIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlOiAnY3VycmVuY3knLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVuY3k6ICdCUkwnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSkuZm9ybWF0KGRldGFpbC5hbW91bnQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIGJvcmRlci1yIGJvcmRlci1bI0ZGOTkwMF1cIj57ZGV0YWlsLmRheXNSZW50YWJpbGl6ZWR9IERpYXM8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIGJvcmRlci1yIGJvcmRlci1bI0ZGOTkwMF1cIj57ZGV0YWlsLm1vbnRobHlSYXRlfSU8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIGJvcmRlci1yIGJvcmRlci1bI0ZGOTkwMF1cIj57ZGV0YWlsLmlyUmF0ZX0lPC90ZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTIgcHktMiBib3JkZXItciBib3JkZXItWyNGRjk5MDBdXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge25ldyBJbnRsLk51bWJlckZvcm1hdCgncHQtQlInLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbmN5OiAnQlJMJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0pLmZvcm1hdChkZXRhaWwuaXJWYWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC0yIHB5LTJcIj4tPC90ZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICAgICAgICAgICAgICApKVxyXG4gICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8dHI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjb2xTcGFuPXs3fSBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweC0yIHB5LTQgdGV4dC1ncmF5LTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIE5lbmh1bSBjb250cmF0byBhdGl2byBlbmNvbnRyYWRvIHBhcmEgY8OhbGN1bG8gZGUgSVJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8dHI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY29sU3Bhbj17Nn0gY2xhc3NOYW1lPVwidGV4dC1yaWdodCBweC0yIHB5LTIgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIGJvcmRlci10IGJvcmRlci1bI0ZGOTkwMF1cIj5WYWxvciB0b3RhbCBkbyBJUjwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMiBweS0yIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIGJvcmRlci10IGJvcmRlci1bI0ZGOTkwMF1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge25ldyBJbnRsLk51bWJlckZvcm1hdCgncHQtQlInLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU6ICdjdXJyZW5jeScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVuY3k6ICdCUkwnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pLmZvcm1hdChjb250cmFjdERldGFpbHMudG90YWxJUil9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgPHRyPlxyXG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjb2xTcGFuPXs3fSBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweC0yIHB5LTQgdGV4dC1ncmF5LTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgRGFkb3MgZG8gY29udHJhdG8gbsOjbyBlbmNvbnRyYWRvc1xyXG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvdGJvZHk+XHJcbiAgICAgICAgICAgIDwvdGFibGU+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBBdmlzbyBxdWFuZG8gbsOjbyBow6EgY29udHJhdG9zIGF0aXZvcyAqL31cclxuICAgICAgICB7IWlzTG9hZGluZ0NvbnRyYWN0ICYmICFoYXNBY3RpdmVDb250cmFjdHMgJiYgKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtOTAwIGJvcmRlciBib3JkZXItcmVkLTUwMCByb3VuZGVkLWxnIHAtNCBtYi00XCI+XHJcbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCBtYi0yXCI+4pqg77iPIE5lbmh1bSBDb250cmF0byBBdGl2byBFbmNvbnRyYWRvPC9oND5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgTsOjbyDDqSBwb3Nzw612ZWwgcmVhbGl6YXIgdXBncmFkZSBwb2lzIG7Do28gaMOhIGNvbnRyYXRvcyBhdGl2b3MgcGFyYSBlc3RlIGludmVzdGlkb3IuXHJcbiAgICAgICAgICAgICAgQXBlbmFzIGNvbnRyYXRvcyBjb20gc3RhdHVzIEFUSVZPIHBvZGVtIHNlciBhbHRlcmFkb3MuXHJcbiAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBBdmlzbyBwYXJhIFNDUCBzb2JyZSBJUiAqL31cclxuICAgICAgICB7d2F0Y2goXCJtb2RhbGlkYWRlXCIpID09PSBcIlNDUFwiICYmIGNvbnRyYWN0RGV0YWlscz8uY29udHJhY3RUeXBlICE9PSBcIk1VVFVPXCIgJiYgKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTkwMCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwIHJvdW5kZWQtbGcgcC00IG1iLTRcIj5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTIwMCB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAg4oS577iPIDxzdHJvbmc+TW9kYWxpZGFkZSBTQ1A6PC9zdHJvbmc+IENvbnRyYXRvcyBTQ1AgbsOjbyBwb3NzdWVtIGRlc2NvbnRvIGRlIEltcG9zdG8gZGUgUmVuZGEuXHJcbiAgICAgICAgICAgICAgQSB0YWJlbGEgZGUgSVIgbsOjbyBzZXLDoSBleGliaWRhIHBhcmEgZXN0YSBtb2RhbGlkYWRlLlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICB7LyogQXZpc28gZXNwZWNpYWwgcGFyYSBtdWRhbsOnYSBkZSBNw5pUVU8gcGFyYSBTQ1AgKi99XHJcbiAgICAgICAge3dhdGNoKFwibW9kYWxpZGFkZVwiKSA9PT0gXCJTQ1BcIiAmJiBjb250cmFjdERldGFpbHM/LmNvbnRyYWN0VHlwZSA9PT0gXCJNVVRVT1wiICYmIChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctb3JhbmdlLTkwMCBib3JkZXIgYm9yZGVyLW9yYW5nZS01MDAgcm91bmRlZC1sZyBwLTQgbWItNFwiPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW9yYW5nZS0yMDAgdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgIOKaoO+4jyA8c3Ryb25nPkFsdGVyYcOnw6NvIE3DmlRVTyDihpIgU0NQOjwvc3Ryb25nPiBDb21vIG8gY29udHJhdG8gYW50ZXJpb3IgZXJhIE3DmlRVTywgw6kgbmVjZXNzw6FyaW8gdHJhdGFyIG8gSVIgYWN1bXVsYWRvLlxyXG4gICAgICAgICAgICAgIEEgdGFiZWxhIGRlIElSIHNlcsOhIGV4aWJpZGEgcGFyYSBxdWUgdm9jw6ogcG9zc2EgZXNjb2xoZXIgY29tbyBwcm9jZWRlciBjb20gbyBkZXNjb250by5cclxuICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgey8qIEF2aXNvIHBhcmEgY2FsY3VsYXIgSVIgcHJpbWVpcm8gKi99XHJcbiAgICAgICAgeyh3YXRjaChcIm1vZGFsaWRhZGVcIikgPT09IFwiTVVUVU9cIiB8fCBjb250cmFjdERldGFpbHM/LmNvbnRyYWN0VHlwZSA9PT0gXCJNVVRVT1wiKSAmJiAhaXJDYWxjdWxhZG8gJiYgKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtOTAwIGJvcmRlciBib3JkZXItcmVkLTUwMCByb3VuZGVkLWxnIHAtNCBtYi00XCI+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTIwMCB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAg4pqg77iPIDxzdHJvbmc+QcOnw6NvIE9icmlnYXTDs3JpYTo8L3N0cm9uZz4gQ2xpcXVlIG5vIGJvdMOjbyAmcXVvdDsmcXVvdDtDYWxjdWxhciBJUiZxdW90OyZxdW90OyBhY2ltYSBhbnRlcyBkZSBwcm9zc2VndWlyLlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICB7LyogQ2hlY2tib3hlcyBkZSBJUiAtIHBhcmEgTVVUVU8gb3UgcXVhbmRvIGNvbnRyYXRvIGFudGVyaW9yIGVyYSBNVVRVTywgZSBhcMOzcyBjYWxjdWxhciBJUiAqL31cclxuICAgICAgICB7KHdhdGNoKFwibW9kYWxpZGFkZVwiKSA9PT0gXCJNVVRVT1wiIHx8IGNvbnRyYWN0RGV0YWlscz8uY29udHJhY3RUeXBlID09PSBcIk1VVFVPXCIpICYmIGlyQ2FsY3VsYWRvICYmIChcclxuICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tOTAwIGJvcmRlciBib3JkZXItZ3JlZW4tNTAwIHJvdW5kZWQtbGcgcC0zIG1iLTRcIj5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTIwMCB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICDinIUgPHN0cm9uZz5JUiBDYWxjdWxhZG86PC9zdHJvbmc+IEFnb3JhIHNlbGVjaW9uZSB1bWEgZGFzIG9ww6fDtWVzIGFiYWl4byAob2JyaWdhdMOzcmlvKTpcclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cgZ2FwLTQgbWItMlwiPlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LXdoaXRlIHRleHQteHNcIj5cclxuICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtci0yXCJcclxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17d2F0Y2goXCJpckRlcG9zaXRvXCIpfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoZS50YXJnZXQuY2hlY2tlZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0VmFsdWUoXCJpckRlcG9zaXRvXCIsIHRydWUpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0VmFsdWUoXCJpckRlc2NvbnRvXCIsIGZhbHNlKTsgLy8gRGVzbWFyY2EgYSBvdXRyYSBvcMOnw6NvXHJcbiAgICAgICAgICAgICAgICAgICAgICBzZXRJck9wY2FvU2VsZWNpb25hZGEodHJ1ZSk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gUmVzdGF1cmFyIHZhbG9yIG9yaWdpbmFsIHNlIGV4aXN0aXIgKGNhc28gZXN0YXZhIGNvbSBkZXNjb250bylcclxuICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWxvck9yaWdpbmFsSW52ZXN0aW1lbnRvKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFZhbHVlKFwidmFsb3JJbnZlc3RpbWVudG9cIiwgdmFsb3JPcmlnaW5hbEludmVzdGltZW50byk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvYXN0LmluZm8oXCJWYWxvciBvcmlnaW5hbCBkbyBpbnZlc3RpbWVudG8gcmVzdGF1cmFkby4gTyBJUiBzZXLDoSBkZXBvc2l0YWRvIHNlcGFyYWRhbWVudGUuXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzZXRWYWx1ZShcImlyRGVwb3NpdG9cIiwgZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0SXJPcGNhb1NlbGVjaW9uYWRhKCEhd2F0Y2goXCJpckRlc2NvbnRvXCIpKTsgLy8gVmVyaWZpY2Egc2UgYSBvdXRyYSBlc3TDoSBtYXJjYWRhXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIEludmVzdGlkb3IgaXLDoSBkZXBvc2l0YXIgdmFsb3IgcmVmZXJlbnRlIGFvIElSXHJcbiAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC13aGl0ZSB0ZXh0LXhzXCI+XHJcbiAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXItMlwiXHJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3dhdGNoKFwiaXJEZXNjb250b1wiKX1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGUudGFyZ2V0LmNoZWNrZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgIHNldFZhbHVlKFwiaXJEZXNjb250b1wiLCB0cnVlKTtcclxuICAgICAgICAgICAgICAgICAgICAgIHNldFZhbHVlKFwiaXJEZXBvc2l0b1wiLCBmYWxzZSk7IC8vIERlc21hcmNhIGEgb3V0cmEgb3DDp8Ojb1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0SXJPcGNhb1NlbGVjaW9uYWRhKHRydWUpO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIC8vIEFwbGljYXIgZGVzY29udG8gZG8gSVIgYXV0b21hdGljYW1lbnRlIHVzYW5kbyBvIHZhbG9yIHRvdGFsIGRhIHRhYmVsYVxyXG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsb3JBdHVhbCA9IHdhdGNoKFwidmFsb3JJbnZlc3RpbWVudG9cIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICBpZiAodmFsb3JBdHVhbCAmJiBjb250cmFjdERldGFpbHM/LnRvdGFsSVIgJiYgY29udHJhY3REZXRhaWxzLnRvdGFsSVIgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFNhbHZhciB2YWxvciBvcmlnaW5hbCBzZSBhaW5kYSBuw6NvIGZvaSBzYWx2b1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXZhbG9yT3JpZ2luYWxJbnZlc3RpbWVudG8pIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRWYWxvck9yaWdpbmFsSW52ZXN0aW1lbnRvKHZhbG9yQXR1YWwpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWxvck51bWVyaWNvID0gcGFyc2VWYWxvcih2YWxvckF0dWFsKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsb3JJUlRhYmVsYSA9IGNvbnRyYWN0RGV0YWlscy50b3RhbElSOyAvLyBVc2FyIHZhbG9yIGRhIHRhYmVsYSBkZSBkZXRhbGhhbWVudG9cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIERlYnVnIGxvZ3NcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCI9PT0gREVCVUcgREVTQ09OVE8gSVIgPT09XCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIlZhbG9yIGF0dWFsIChzdHJpbmcpOlwiLCB2YWxvckF0dWFsKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJWYWxvciBudW3DqXJpY28gY29udmVydGlkbzpcIiwgdmFsb3JOdW1lcmljbyk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiVmFsb3IgSVIgZGEgdGFiZWxhOlwiLCB2YWxvcklSVGFiZWxhKTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbG9yQ29tRGVzY29udG8gPSB2YWxvck51bWVyaWNvIC0gdmFsb3JJUlRhYmVsYTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJWYWxvciBjb20gZGVzY29udG86XCIsIHZhbG9yQ29tRGVzY29udG8pO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gdmFsb3IgY29tIGRlc2NvbnRvIMOpIHBvc2l0aXZvXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWxvckNvbURlc2NvbnRvIDw9IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcihcIuKaoO+4jyBFcnJvOiBPIHZhbG9yIGRvIElSIMOpIG1haW9yIHF1ZSBvIHZhbG9yIGRvIGludmVzdGltZW50by4gVmVyaWZpcXVlIG9zIHZhbG9yZXMuXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldFZhbHVlKFwiaXJEZXNjb250b1wiLCBmYWxzZSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBBcGxpY2FyIG8gdmFsb3IgY29tIGRlc2NvbnRvXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIENvbnZlcnRlciBwYXJhIGNlbnRhdm9zIChtdWx0aXBsaWNhciBwb3IgMTAwKSBlIGRlcG9pcyBhcGxpY2FyIG3DoXNjYXJhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbG9yRW1DZW50YXZvcyA9IE1hdGgucm91bmQodmFsb3JDb21EZXNjb250byAqIDEwMCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbG9yRm9ybWF0YWRvID0gdmFsdWVNYXNrKHZhbG9yRW1DZW50YXZvcy50b1N0cmluZygpKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJWYWxvciBlbSBjZW50YXZvczpcIiwgdmFsb3JFbUNlbnRhdm9zKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coXCJWYWxvciBmb3JtYXRhZG86XCIsIHZhbG9yRm9ybWF0YWRvKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0VmFsdWUoXCJ2YWxvckludmVzdGltZW50b1wiLCB2YWxvckZvcm1hdGFkbyk7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0b2FzdC5zdWNjZXNzKGDinIUgRGVzY29udG8gYXBsaWNhZG8hIElSIGRlICR7bmV3IEludGwuTnVtYmVyRm9ybWF0KCdwdC1CUicsIHsgc3R5bGU6ICdjdXJyZW5jeScsIGN1cnJlbmN5OiAnQlJMJyB9KS5mb3JtYXQodmFsb3JJUlRhYmVsYSl9IGZvaSBkZXNjb250YWRvIGRvIHZhbG9yIGRvIGludmVzdGltZW50by5gKTtcclxuICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvYXN0LmVycm9yKFwi4pqg77iPIE7Do28gZm9pIHBvc3PDrXZlbCBhcGxpY2FyIG8gZGVzY29udG8uIFZlcmlmaXF1ZSBzZSBow6EgY29udHJhdG9zIGF0aXZvcyBuYSB0YWJlbGEgZGUgSVIuXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRWYWx1ZShcImlyRGVzY29udG9cIiwgZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgIC8vIE1vc3RyYXIgYXZpc28gZGUgYWRlbmRvXHJcbiAgICAgICAgICAgICAgICAgICAgICB0b2FzdC5pbmZvKFwi4pqg77iPIEF0ZW7Dp8OjbzogQW8gc2VsZWNpb25hciBkZXNjb250byBkbyBJUiBzb2JyZSBvIHZhbG9yIGRvIGNvbnRyYXRvLCBwb2RlIHNlciBuZWNlc3PDoXJpbyBjcmlhciB1bSBhZGVuZG8gcGFyYSBhanVzdGUgZGUgdmFsb3IuXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzZXRWYWx1ZShcImlyRGVzY29udG9cIiwgZmFsc2UpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgc2V0SXJPcGNhb1NlbGVjaW9uYWRhKCEhd2F0Y2goXCJpckRlcG9zaXRvXCIpKTsgLy8gVmVyaWZpY2Egc2UgYSBvdXRyYSBlc3TDoSBtYXJjYWRhXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgLy8gUmVzdGF1cmFyIHZhbG9yIG9yaWdpbmFsIHNlIGV4aXN0aXJcclxuICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWxvck9yaWdpbmFsSW52ZXN0aW1lbnRvKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFZhbHVlKFwidmFsb3JJbnZlc3RpbWVudG9cIiwgdmFsb3JPcmlnaW5hbEludmVzdGltZW50byk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvYXN0LmluZm8oXCJWYWxvciBvcmlnaW5hbCBkbyBpbnZlc3RpbWVudG8gcmVzdGF1cmFkby5cIik7XHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIEludmVzdGlkb3IgZGVjaWRpdSBkZXNjb250byBkbyBJUiBzb2JyZSBvIHZhbG9yIGRvIGNvbnRyYXRvXHJcbiAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8Lz5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICB7LyogUmVzdW1vIGRvIGRlc2NvbnRvIGRlIElSIGFwbGljYWRvICovfVxyXG4gICAgICAgIHt3YXRjaChcImlyRGVzY29udG9cIikgJiYgdmFsb3JPcmlnaW5hbEludmVzdGltZW50byAmJiBjb250cmFjdERldGFpbHM/LnRvdGFsSVIgJiYgKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTkwMCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwIHJvdW5kZWQtbGcgcC00IG1iLTRcIj5cclxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtYmx1ZS0yMDAgZm9udC1ib2xkIG1iLTJcIj7wn5KwIERlc2NvbnRvIGRlIElSIEFwbGljYWRvPC9oND5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMjAwIHRleHQtc20gc3BhY2UteS0xXCI+XHJcbiAgICAgICAgICAgICAgPHA+PHN0cm9uZz5WYWxvciBvcmlnaW5hbDo8L3N0cm9uZz4ge3ZhbG9yT3JpZ2luYWxJbnZlc3RpbWVudG99PC9wPlxyXG4gICAgICAgICAgICAgIDxwPjxzdHJvbmc+VmFsb3IgdG90YWwgZG8gSVIgKGRhIHRhYmVsYSk6PC9zdHJvbmc+IHtuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ3B0LUJSJywgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3k6ICdCUkwnIH0pLmZvcm1hdChjb250cmFjdERldGFpbHMudG90YWxJUil9PC9wPlxyXG4gICAgICAgICAgICAgIDxwPjxzdHJvbmc+VmFsb3IgZmluYWwgKGNvbSBkZXNjb250byk6PC9zdHJvbmc+IHt3YXRjaChcInZhbG9ySW52ZXN0aW1lbnRvXCIpfTwvcD5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy0yMDAgdGV4dC14cyBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICDihLnvuI8gTyB2YWxvciBkZXNjb250YWRvIMOpIGJhc2VhZG8gbm8gY8OhbGN1bG8gZGV0YWxoYWRvIGRhIHRhYmVsYSBhY2ltYSwgY29uc2lkZXJhbmRvIHRvZG9zIG9zIGNvbnRyYXRvcyBhdGl2b3MuXHJcbiAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIHsvKiBNZW5zYWdlbSBkZSB2YWxvciBjb21wbGVtZW50YXIgcGFyYSBTQ1AgY29tIGRlc2NvbnRvIGRlIElSICovfVxyXG4gICAgICAgIHtzaG93Q29tcGxlbWVudE1lc3NhZ2UgJiYgd2F0Y2goXCJtb2RhbGlkYWRlXCIpID09PSBcIlNDUFwiICYmIHdhdGNoKFwiaXJEZXNjb250b1wiKSAmJiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy05MDAgYm9yZGVyIGJvcmRlci15ZWxsb3ctNTAwIHJvdW5kZWQtbGcgcC00IG10LTRcIj5cclxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQteWVsbG93LTIwMCBmb250LWJvbGQgbWItMlwiPuKaoO+4jyBBanVzdGUgZGUgVmFsb3IgTmVjZXNzw6FyaW88L2g0PlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy0yMDAgdGV4dC1zbSBtYi0zXCI+XHJcbiAgICAgICAgICAgICAgPHN0cm9uZz5Db25mb3JtZSBpbmZvcm1hZG8gbm8gZm9ybXVsw6FyaW86PC9zdHJvbmc+IEluZm9ybWFtb3MgcXVlIG8gdmFsb3IgYXR1YWwsIGFww7NzIGEgYXBsaWNhw6fDo28gZG8gZGVzY29udG8gZG8gSW1wb3N0byBkZSBSZW5kYSBzb2JyZSBvIHZhbG9yIGRvIGNvbnRyYXRvLFxyXG4gICAgICAgICAgICAgIG7Do28gZXN0w6EgZW0gY29uZm9ybWlkYWRlIGNvbSBub3NzYXMgcmVncmFzIGRlIG5lZ8OzY2lvLCBwb2lzIG7Do28gw6kgZGl2aXPDrXZlbCBwb3IgUiQgNS4wMDAsMDAuXHJcbiAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctMjAwIHRleHQtc20gZm9udC1zZW1pYm9sZCBtYi0zXCI+XHJcbiAgICAgICAgICAgICAgUGFyYSBxdWUgYXMgY290YXMgc2VqYW0gYWp1c3RhZGFzIGNvcnJldGFtZW50ZSwgc2Vyw6EgbmVjZXNzw6FyaW8gbyBwYWdhbWVudG8gY29tcGxlbWVudGFyIG5vIHZhbG9yIGRleycgJ31cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy0xMDAgZm9udC1ib2xkXCI+XHJcbiAgICAgICAgICAgICAgICB7dmFsb3JDb21wbGVtZW50YXIudG9Mb2NhbGVTdHJpbmcoJ3B0LUJSJywgeyBzdHlsZTogJ2N1cnJlbmN5JywgY3VycmVuY3k6ICdCUkwnIH0pfVxyXG4gICAgICAgICAgICAgIDwvc3Bhbj4uXHJcbiAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy15ZWxsb3ctODAwIGJvcmRlciBib3JkZXIteWVsbG93LTYwMCByb3VuZGVkIHAtMyBtdC0zXCI+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctMTAwIHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgIDxzdHJvbmc+RXhlbXBsbzo8L3N0cm9uZz4gQ29udHJhdG8gZGUgUiQgMTAuMDAwIGNvbSBJUiBkZSBSJCAyLjAwMCA9IFIkIDguMDAwIHJlc3RhbnRlcy48YnIvPlxyXG4gICAgICAgICAgICAgICAg4oCiIFNpc3RlbWEgY2FkYXN0cmE6IDxzdHJvbmc+e01hdGguZmxvb3IoKGZvcm1hdE51bWJlclZhbHVlKHdhdGNoKFwidmFsb3JJbnZlc3RpbWVudG9cIikpIC0gKGNvbnRyYWN0RGV0YWlscz8udG90YWxJUiB8fCAwKSkgLyA1MDAwKX0gY290YShzKTwvc3Ryb25nPjxici8+XHJcbiAgICAgICAgICAgICAgICDigKIgVmFsb3IgY29tcGxlbWVudGFyOiA8c3Ryb25nPnt2YWxvckNvbXBsZW1lbnRhci50b0xvY2FsZVN0cmluZygncHQtQlInLCB7IHN0eWxlOiAnY3VycmVuY3knLCBjdXJyZW5jeTogJ0JSTCcgfSl9PC9zdHJvbmc+IGRldmVyw6Egc2VyIHBhZ28gc2VwYXJhZGFtZW50ZS5cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTgwMCBib3JkZXIgYm9yZGVyLWdyZWVuLTYwMCByb3VuZGVkIHAtMyBtdC0zXCI+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0xMDAgdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAg4pyFIDxzdHJvbmc+VmFsb3IgUGFyY2lhbCBQZXJtaXRpZG86PC9zdHJvbmc+IE5lc3RlIGNhc28sIG8gY29udHJhdG8gcG9kZSBzZXIgZW52aWFkbyBjb20gbyB2YWxvciBhdHVhbCAobsOjbyBtw7psdGlwbG8gZGUgUiQgNS4wMDApLFxyXG4gICAgICAgICAgICAgICAgcG9pcyBvIHNpc3RlbWEgcmVjb25oZWNlIHF1ZSBoYXZlcsOhIHVtIGRlcMOzc2l0byBhZGljaW9uYWwgcG9zdGVyaW9yIHBhcmEgY29tcGxldGFyIGFzIGNvdGFzLlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtd2hpdGUgbXktOFwiPkRhZG9zIGRvIEludmVzdGltZW50bzwvaDM+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyMxQzFDMUNdIHJvdW5kZWQtbGcgcC04IGJvcmRlciBib3JkZXItWyNGRjk5MDBdIG1iLThcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cgZ2FwLTRcIj5cclxuICAgICAgICAgIHsvKiBDb2x1bmEgZXNxdWVyZGEgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTQgdy1mdWxsIG1kOnctMS8yXCI+XHJcbiAgICAgICAgICAgIHsvKiBDYW1wbyBkZSB2YWxvciBkbyBpbnZlc3RpbWVudG8gc8OzIGFwYXJlY2UgcGFyYSBNVVRVTyAqL31cclxuICAgICAgICAgICAge3dhdGNoKFwibW9kYWxpZGFkZVwiKSA9PT0gXCJNVVRVT1wiICYmIChcclxuICAgICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgICBuYW1lPVwidmFsb3JJbnZlc3RpbWVudG9cIlxyXG4gICAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICAgIGVycm9yPXshIWVycm9ycy52YWxvckludmVzdGltZW50byB8fCAhIXNjcFZhbGlkYXRpb25FcnJvcn1cclxuICAgICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy52YWxvckludmVzdGltZW50bz8ubWVzc2FnZSB8fCBzY3BWYWxpZGF0aW9uRXJyb3J9XHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIlZhbG9yIGRvIEludmVzdGltZW50b1wiXHJcbiAgICAgICAgICAgICAgICBzZXRWYWx1ZT17ZSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgIGNvbnN0IG1hc2tlZFZhbHVlID0gZSA/IHZhbHVlTWFzayhlKSA6IFwiXCI7XHJcbiAgICAgICAgICAgICAgICAgIHNldFZhbHVlKFwidmFsb3JJbnZlc3RpbWVudG9cIiwgbWFza2VkVmFsdWUsIHsgc2hvdWxkVmFsaWRhdGU6IHRydWUgfSk7XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHsvKiBDYW1wbyBkZSBxdWFudGlkYWRlIGRlIGNvdGFzIHPDsyBhcGFyZWNlIHBhcmEgU0NQICovfVxyXG4gICAgICAgICAgICB7d2F0Y2goXCJtb2RhbGlkYWRlXCIpID09PSBcIlNDUFwiICYmIChcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2xcIj5cclxuICAgICAgICAgICAgICAgIDxJbnB1dFRleHRcclxuICAgICAgICAgICAgICAgICAgcmVnaXN0ZXI9e3JlZ2lzdGVyfVxyXG4gICAgICAgICAgICAgICAgICBuYW1lPVwicXVvdGFRdWFudGl0eVwiXHJcbiAgICAgICAgICAgICAgICAgIHdpZHRoPVwiMTAwJVwiXHJcbiAgICAgICAgICAgICAgICAgIGVycm9yPXshIWVycm9ycy5xdW90YVF1YW50aXR5fVxyXG4gICAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2U9e2Vycm9ycz8ucXVvdGFRdWFudGl0eT8ubWVzc2FnZX1cclxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJRdWFudGlkYWRlIGRlIGNvdGFzIChjYWxjdWxhZG8gYXV0b21hdGljYW1lbnRlKVwiXHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQ2FsY3VsYWRvIGF1dG9tYXRpY2FtZW50ZVwiXHJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPElucHV0VGV4dFxyXG4gICAgICAgICAgICAgIHJlZ2lzdGVyPXtyZWdpc3Rlcn1cclxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgbmFtZT1cInRheGFSZW11bmVyYWNhb1wiXHJcbiAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnMudGF4YVJlbXVuZXJhY2FvfVxyXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy50YXhhUmVtdW5lcmFjYW8/Lm1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgbGFiZWw9XCJUYXhhIGRlIFJlbXVuZXJhw6fDo28gTWVuc2FsICVcIlxyXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiZXg6IDJcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8SW5wdXRUZXh0XHJcbiAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgIHJlZ2lzdGVyPXtyZWdpc3Rlcn1cclxuICAgICAgICAgICAgICBtYXhEYXRlPXttb21lbnQoKS5mb3JtYXQoXCJZWVlZLU1NLUREXCIpfVxyXG4gICAgICAgICAgICAgIG5hbWU9XCJpbmljaW9Db250cmF0b1wiXHJcbiAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICBzZXRWYWx1ZT17ZSA9PiBzZXRWYWx1ZShcImluaWNpb0NvbnRyYXRvXCIsIGUsIHsgc2hvdWxkVmFsaWRhdGU6IHRydWUgfSl9XHJcbiAgICAgICAgICAgICAgZXJyb3I9eyEhZXJyb3JzLmluaWNpb0NvbnRyYXRvfVxyXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZT17ZXJyb3JzPy5pbmljaW9Db250cmF0bz8ubWVzc2FnZX1cclxuICAgICAgICAgICAgICBsYWJlbD1cIkluw61jaW8gZG8gQ29udHJhdG9cIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIG1iLTEgYmxvY2tcIj5QZXJmaWw8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxTZWxlY3RDdXN0b21cclxuICAgICAgICAgICAgICAgIHZhbHVlPXt3YXRjaChcInBlcmZpbFwiKX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHNldFZhbHVlKFwicGVyZmlsXCIsIGUudGFyZ2V0LnZhbHVlLCB7IHNob3VsZFZhbGlkYXRlOiB0cnVlIH0pfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjb25zZXJ2YXRpdmVcIj5Db25zZXJ2YWRvcjwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1vZGVyYXRlXCI+TW9kZXJhZG88L29wdGlvbj5cclxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhZ2dyZXNzaXZlXCI+QWdyZXNzaXZvPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgPC9TZWxlY3RDdXN0b20+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICB7LyogQ29sdW5hIGRpcmVpdGEgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTQgdy1mdWxsIG1kOnctMS8yXCI+XHJcbiAgICAgICAgICAgIDxJbnB1dFRleHRcclxuICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgbmFtZT1cInByYXpvSW52ZXN0aW1lbnRvXCJcclxuICAgICAgICAgICAgICB3aWR0aD1cIjEwMCVcIlxyXG4gICAgICAgICAgICAgIGVycm9yPXshIWVycm9ycy5wcmF6b0ludmVzdGltZW50b31cclxuICAgICAgICAgICAgICBlcnJvck1lc3NhZ2U9e2Vycm9ycz8ucHJhem9JbnZlc3RpbWVudG8/Lm1lc3NhZ2V9XHJcbiAgICAgICAgICAgICAgbGFiZWw9XCJQcmF6byBkbyBJbnZlc3RpbWVudG8gLSBlbSBtZXNlc1wiXHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJleDogMTJcIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIG1iLTEgYmxvY2tcIj5Db21wcmFyIGNvbTwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgPFNlbGVjdEN1c3RvbVxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e3dhdGNoKFwiY29tcHJhckNvbVwiKX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHNldFZhbHVlKFwiY29tcHJhckNvbVwiLCBlLnRhcmdldC52YWx1ZSwgeyBzaG91bGRWYWxpZGF0ZTogdHJ1ZSB9KX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwicGl4XCI+UElYPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYm9sZXRvXCI+Qm9sZXRvPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYmFua190cmFuc2ZlclwiPlRyYW5zZmVyw6puY2lhIEJhbmPDoXJpYTwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgIDwvU2VsZWN0Q3VzdG9tPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPElucHV0VGV4dFxyXG4gICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcclxuICAgICAgICAgICAgICByZWdpc3Rlcj17cmVnaXN0ZXJ9XHJcbiAgICAgICAgICAgICAgbmFtZT1cImZpbUNvbnRyYXRvXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17ZW5kRGF0ZSA/IG1vbWVudChlbmREYXRlLCBcIkREL01NL1lZWVlcIikuZm9ybWF0KFwiWVlZWS1NTS1ERFwiKSA6IFwiXCJ9XHJcbiAgICAgICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgICAgICBkaXNhYmxlZD17dHJ1ZX1cclxuICAgICAgICAgICAgICBsYWJlbD1cIkZpbSBkbyBDb250cmF0b1wiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgbWItMSBibG9ja1wiPkRlYsOqbnR1cmU8L2xhYmVsPlxyXG4gICAgICAgICAgICAgIDxTZWxlY3RDdXN0b21cclxuICAgICAgICAgICAgICAgIHZhbHVlPXt3YXRjaChcImRlYmVudHVyZVwiKX1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHNldFZhbHVlKFwiZGViZW50dXJlXCIsIGUudGFyZ2V0LnZhbHVlLCB7IHNob3VsZFZhbGlkYXRlOiB0cnVlIH0pfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJzXCI+U2ltPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiblwiPk7Do288L29wdGlvbj5cclxuICAgICAgICAgICAgICA8L1NlbGVjdEN1c3RvbT5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTdGVwKDEpfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLVsjRkY5OTAwXSB0ZXh0LVsjRkY5OTAwXSBweC04IHB5LTIgcm91bmRlZC1sZyBob3ZlcjpiZy1bI0ZGOTkwMF0gaG92ZXI6dGV4dC13aGl0ZVwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAg4oaQIFZvbHRhclxyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIDxCdXR0b25cclxuICAgICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgICB0eXBlPVwic3VibWl0XCJcclxuICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTggcHktMiByb3VuZGVkLWxnICR7XHJcbiAgICAgICAgICAgIGhhc0FjdGl2ZUNvbnRyYWN0c1xyXG4gICAgICAgICAgICAgID8gXCJiZy1bI0ZGOTkwMF0gdGV4dC13aGl0ZSBob3ZlcjpiZy1bI2U2OGEwMF1cIlxyXG4gICAgICAgICAgICAgIDogXCJiZy1ncmF5LTUwMCB0ZXh0LWdyYXktMzAwIGN1cnNvci1ub3QtYWxsb3dlZFwiXHJcbiAgICAgICAgICB9YH1cclxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJCb3TDo28gQ29uY2x1aXIgY2xpY2Fkb1wiKTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXCJFc3RhZG8gZG8gZm9ybXVsw6FyaW86XCIsIHsgaXNWYWxpZCwgZXJyb3JzIH0pO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIkRhZG9zIGF0dWFpczpcIiwgd2F0Y2goKSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiQ29udHJhdG9zIGF0aXZvczpcIiwgaGFzQWN0aXZlQ29udHJhY3RzKTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBkaXNhYmxlZD17XHJcbiAgICAgICAgICAgICFoYXNBY3RpdmVDb250cmFjdHMgfHxcclxuICAgICAgICAgICAgaXNTdWJtaXR0aW5nIHx8XHJcbiAgICAgICAgICAgIGNyZWF0ZUNvbnRyYWN0TXV0YXRpb24uaXNQZW5kaW5nIHx8XHJcbiAgICAgICAgICAgIGlzUmVkaXJlY3RpbmcgfHxcclxuICAgICAgICAgICAgKCh3YXRjaChcIm1vZGFsaWRhZGVcIikgPT09IFwiTVVUVU9cIiB8fCBjb250cmFjdERldGFpbHM/LmNvbnRyYWN0VHlwZSA9PT0gXCJNVVRVT1wiKSAmJiAoIWlyQ2FsY3VsYWRvIHx8ICghd2F0Y2goXCJpckRlcG9zaXRvXCIpICYmICF3YXRjaChcImlyRGVzY29udG9cIikpKSlcclxuICAgICAgICAgIH1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7IWhhc0FjdGl2ZUNvbnRyYWN0cyA/IFwiTmVuaHVtIGNvbnRyYXRvIGF0aXZvIGVuY29udHJhZG9cIiA6XHJcbiAgICAgICAgICAgaXNSZWRpcmVjdGluZyA/IFwiUmVkaXJlY2lvbmFuZG8uLi5cIiA6XHJcbiAgICAgICAgICAgaXNTdWJtaXR0aW5nIHx8IGNyZWF0ZUNvbnRyYWN0TXV0YXRpb24uaXNQZW5kaW5nID8gXCJFbnZpYW5kby4uLlwiIDpcclxuICAgICAgICAgICAoKHdhdGNoKFwibW9kYWxpZGFkZVwiKSA9PT0gXCJNVVRVT1wiIHx8IGNvbnRyYWN0RGV0YWlscz8uY29udHJhY3RUeXBlID09PSBcIk1VVFVPXCIpICYmICFpckNhbGN1bGFkbykgPyBcIkNhbGN1bGUgbyBJUiBwcmltZWlyb1wiIDpcclxuICAgICAgICAgICAoKHdhdGNoKFwibW9kYWxpZGFkZVwiKSA9PT0gXCJNVVRVT1wiIHx8IGNvbnRyYWN0RGV0YWlscz8uY29udHJhY3RUeXBlID09PSBcIk1VVFVPXCIpICYmICghd2F0Y2goXCJpckRlcG9zaXRvXCIpICYmICF3YXRjaChcImlyRGVzY29udG9cIikpKSA/IFwiU2VsZWNpb25lIHVtYSBvcMOnw6NvIGRlIElSXCIgOlxyXG4gICAgICAgICAgIFwiQWx0ZXJhciBDb250cmF0b1wifVxyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZm9ybT5cclxuICApO1xyXG5cclxuICAvLyBNb3N0cmFyIGxvYWRpbmcgZW5xdWFudG8gY2FycmVnYSBvcyBkYWRvc1xyXG4gIGlmIChpc0xvYWRpbmdDb250cmFjdCkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPD5cclxuICAgICAgICA8SGVhZGVyIC8+XHJcbiAgICAgICAgPFNpZGViYXI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtaW4taC1bNDAwcHhdXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1bI0ZGOTkwMF0gbXgtYXV0byBtYi00XCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+Q2FycmVnYW5kbyBkYWRvcyBkbyBjb250cmF0by4uLjwvcD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L1NpZGViYXI+XHJcbiAgICAgIDwvPlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8SGVhZGVyIC8+XHJcbiAgICAgIDxTaWRlYmFyPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTYgdy1mdWxsIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIHRleHQtY2VudGVyIHctZnVsbCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxyXG4gICAgICAgICAgICAgICAgQ29udHJhdG9zXHJcbiAgICAgICAgICAgICAgICB7Y29udHJhY3REYXRhICYmIChcclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIGJsb2NrIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICB7Y29udHJhY3REYXRhLmNvbnRyYWN0cz8uWzBdPy5pbnZlc3Rvck5hbWV9XHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgPC9oMT5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIHsvKiBTZSBuw6NvIGjDoSB0aXBvIGRlZmluaWRvIGUgaMOhIGNvbnRyYXRvcyBhdGl2b3MsIG1vc3RyYXIgb3DDp8O1ZXMgZGUgdXBncmFkZSAqL31cclxuICAgICAgICAgICAgeyF0aXBvICYmIGhhc0FjdGl2ZUNvbnRyYWN0cyA/IChcclxuICAgICAgICAgICAgICB1cGdyYWRlQ29udHJhY3RPcHRpb25zKClcclxuICAgICAgICAgICAgKSA6ICF0aXBvICYmICFoYXNBY3RpdmVDb250cmFjdHMgPyAoXHJcbiAgICAgICAgICAgICAgLyogU2UgbsOjbyBow6EgY29udHJhdG9zIGF0aXZvcywgbW9zdHJhciBtZW5zYWdlbSAqL1xyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTkwMCBib3JkZXIgYm9yZGVyLXJlZC01MDAgcm91bmRlZC1sZyBwLTYgdGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCBtYi0yXCI+4pqg77iPIE5lbmh1bSBDb250cmF0byBBdGl2byBFbmNvbnRyYWRvPC9oND5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICBOw6NvIMOpIHBvc3PDrXZlbCByZWFsaXphciB1cGdyYWRlIHBvaXMgbsOjbyBow6EgY29udHJhdG9zIGF0aXZvcyBwYXJhIGVzdGUgaW52ZXN0aWRvci5cclxuICAgICAgICAgICAgICAgICAgQXBlbmFzIGNvbnRyYXRvcyBjb20gc3RhdHVzIEFUSVZPIHBvZGVtIHNlciBhbHRlcmFkb3MuXHJcbiAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgLyogU2UgaMOhIHRpcG8gZGVmaW5pZG8sIG1vc3RyYXIgZm9ybXVsw6FyaW8gbm9ybWFsICovXHJcbiAgICAgICAgICAgICAgc3RlcCA9PT0gMSA/IHJlbmRlclN0ZXAxKCkgOiByZW5kZXJTdGVwMigpXHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9TaWRlYmFyPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiQnV0dG9uIiwidG9hc3QiLCJ1c2VSb3V0ZXIiLCJ1c2VTZWFyY2hQYXJhbXMiLCJIZWFkZXIiLCJTaWRlYmFyIiwiY2VwTWFzayIsImNwZk1hc2siLCJwaG9uZU1hc2siLCJ2YWx1ZU1hc2siLCJjbnBqTWFzayIsIm1vbWVudCIsIklucHV0VGV4dCIsIlNlbGVjdEN1c3RvbSIsInVzZU1lbW8iLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlRm9ybSIsInl1cFJlc29sdmVyIiwieXVwIiwiZ2V0RmluYWxEYXRhV2l0aE1vdW50IiwidXNlTXV0YXRpb24iLCJ1c2VRdWVyeSIsInJldHVybkVycm9yIiwiYXBpIiwiZ2V0VXNlclByb2ZpbGUiLCJmb3JtYXROdW1iZXJWYWx1ZSIsInBob25lV2l0aENvdW50cnlNYXNrIiwidmFsdWUiLCJjbGVhbmVkIiwicmVwbGFjZSIsInN0YXJ0c1dpdGgiLCJsZW5ndGgiLCJ3aXRoQ291bnRyeUNvZGUiLCJ2YWxpZGF0aW9uU2NoZW1hIiwib2JqZWN0Iiwic2hhcGUiLCJ0aXBvQ29udHJhdG8iLCJzdHJpbmciLCJyZXF1aXJlZCIsIm5vbWVDb21wbGV0byIsImlkZW50aWRhZGUiLCJjZWx1bGFyIiwiY3BmIiwiZGF0YU5hc2NpbWVudG8iLCJvcHRpb25hbCIsIm5vbWVNYWUiLCJlbWFpbCIsImNlcCIsImNpZGFkZSIsImVuZGVyZWNvIiwibnVtZXJvIiwiY29tcGxlbWVudG8iLCJlc3RhZG8iLCJiYW5jbyIsImNvbnRhIiwiYWdlbmNpYSIsImNoYXZlUGl4IiwibW9kYWxpZGFkZSIsInRlc3QiLCJ2YWxvckludmVzdGltZW50byIsInBhcmVudCIsIm51bWVyaWNWYWx1ZSIsIk51bWJlciIsInJlcGxhY2VBbGwiLCJwcmF6b0ludmVzdGltZW50byIsInRheGFSZW11bmVyYWNhbyIsImNvbXByYXJDb20iLCJpbmljaW9Db250cmF0byIsImZpbUNvbnRyYXRvIiwicGVyZmlsIiwiZGViZW50dXJlIiwib2JzZXJ2YWNvZXMiLCJxdW90YVF1YW50aXR5IiwiaXJEZXBvc2l0byIsImJvb2xlYW4iLCJpckRlc2NvbnRvIiwiQWx0ZXJhckNvbnRyYXRvIiwiY29udHJhY3REYXRhIiwicm91dGVyIiwic2VhcmNoUGFyYW1zIiwidGlwbyIsImdldCIsImludmVzdG9ySWQiLCJ1c2VyUHJvZmlsZSIsImRhdGEiLCJpc0xvYWRpbmciLCJpc0xvYWRpbmdDb250cmFjdCIsInF1ZXJ5S2V5IiwicXVlcnlGbiIsInJlc3BvbnNlIiwiZW5hYmxlZCIsInJlZ2lzdGVyIiwiaGFuZGxlU3VibWl0Iiwid2F0Y2giLCJzZXRWYWx1ZSIsInRyaWdnZXIiLCJmb3JtU3RhdGUiLCJlcnJvcnMiLCJpc1ZhbGlkIiwiaXNTdWJtaXR0aW5nIiwicmVzb2x2ZXIiLCJtb2RlIiwiZGVmYXVsdFZhbHVlcyIsInN0ZXAiLCJzZXRTdGVwIiwiYWxpcXVvdGFJUiIsInNldEFsaXF1b3RhSVIiLCJpc1JlZGlyZWN0aW5nIiwic2V0SXNSZWRpcmVjdGluZyIsInZhbG9yQ29tcGxlbWVudGFyIiwic2V0VmFsb3JDb21wbGVtZW50YXIiLCJzaG93Q29tcGxlbWVudE1lc3NhZ2UiLCJzZXRTaG93Q29tcGxlbWVudE1lc3NhZ2UiLCJpckNhbGN1bGFkbyIsInNldElyQ2FsY3VsYWRvIiwiaXJPcGNhb1NlbGVjaW9uYWRhIiwic2V0SXJPcGNhb1NlbGVjaW9uYWRhIiwidmFsb3JPcmlnaW5hbEludmVzdGltZW50byIsInNldFZhbG9yT3JpZ2luYWxJbnZlc3RpbWVudG8iLCJzY3BWYWxpZGF0aW9uRXJyb3IiLCJzZXRTY3BWYWxpZGF0aW9uRXJyb3IiLCJ2YWxpZGF0ZVNjcFZhbHVlIiwidmFsb3IiLCJ0cmltIiwidmFsb3JOdW1lcmljbyIsImNvbnNvbGUiLCJsb2ciLCJjdXJyZW50Q29udHJhY3RNb2RhbGl0eSIsImNvbnRyYWN0cyIsImNvbnRyYWN0IiwidGFncyIsImludmVzdG9yTmFtZSIsImRvY3VtZW50byIsImRvY3VtZW50IiwiZG9jdW1lbnRvQ29tTWFzY2FyYSIsInJnIiwidGVsZWZvbmUiLCJwaG9uZSIsIm1vdGhlck5hbWUiLCJiaXJ0aERhdGUiLCJ6aXBDb2RlIiwiY2l0eSIsImFkZHJlc3MiLCJhZGRyZXNzTnVtYmVyIiwiY29tcGxlbWVudCIsInN0YXRlIiwiYmFuayIsImJyYW5jaCIsImFjY291bnROdW1iZXIiLCJpbnZlc3RtZW50VmFsdWUiLCJpbnZlc3RtZW50WWllbGQiLCJpbnZlc3RtZW50VGVybSIsInBheW1lbnRNZXRob2QiLCJwdXJjaGFzZWRXaXRoIiwiaW5jbHVkZXMiLCJjb250cmFjdFN0YXJ0IiwiZm9ybWF0IiwiY290YXMiLCJNYXRoIiwiZmxvb3IiLCJ0b1N0cmluZyIsImhhc0FjdGl2ZUNvbnRyYWN0cyIsImNvbnRyYWN0U3RhdHVzIiwidG9VcHBlckNhc2UiLCJtYWluQ29udHJhY3RBY3RpdmUiLCJoYXNBY3RpdmVBZGRlbmR1bSIsImFkZGVuZHVtIiwic29tZSIsImFkZGVuZHVtU3RhdHVzIiwiaGFuZGxlUmVudGFiaWxpdHlDbGljayIsInB1c2giLCJoYW5kbGVNb2RhbGl0eUNsaWNrIiwidXBncmFkZUNvbnRyYWN0T3B0aW9ucyIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwib25DbGljayIsInAiLCJjYWxjdWxhckFsaXF1b3RhSVIiLCJzaG93VG9hc3QiLCJwcmF6b01lc2VzIiwiaXNOYU4iLCJ3YXJuIiwicHJhem9EaWFzIiwiYWxpcXVvdGEiLCJzdWNjZXNzIiwib25TdWJtaXQiLCJjb250cmFjdERldGFpbHMiLCJjb250cmFjdFR5cGUiLCJlcnJvciIsImlzUEoiLCJyZXF1ZXN0RGF0YSIsInJvbGUiLCJuYW1lIiwicGVyc29uVHlwZSIsImFkdmlzb3JzIiwiYWR2aXNvcklkIiwicm9sZUlkIiwicmF0ZSIsImludmVzdG1lbnQiLCJhbW91bnQiLCJwYXJzZUludCIsIm1vbnRobHlSYXRlIiwicGFyc2VGbG9hdCIsImR1cmF0aW9uSW5Nb250aHMiLCJlbmREYXRlIiwibm93IiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwiZ2V0TW9udGgiLCJnZXREYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInByb2ZpbGUiLCJtYXgiLCJ1bmRlZmluZWQiLCJpc0RlYmVudHVyZSIsImJhbmtBY2NvdW50IiwiYWdlbmN5IiwiYWNjb3VudCIsInBpeCIsImluZGl2aWR1YWwiLCJmdWxsTmFtZSIsInJnTGltcG8iLCJpc3N1aW5nQWdlbmN5IiwibmF0aW9uYWxpdHkiLCJvY2N1cGF0aW9uIiwibWF0Y2giLCJkYXRlIiwiY2xlYW5QaG9uZSIsInN0cmVldCIsInJ1YSIsIm5laWdoYm9yaG9vZCIsImJhaXJybyIsInBvc3RhbENvZGUiLCJudW1iZXIiLCJjb21wYW55IiwiY29tcGFueU5hbWUiLCJjbnBqIiwibGVnYWxUeXBlIiwicmVwcmVzZW50YXRpdmUiLCJKU09OIiwic3RyaW5naWZ5IiwiY3JlYXRlQ29udHJhY3RNdXRhdGlvbiIsIm11dGF0ZSIsImNhbXBvc1N0ZXAxIiwiaGFuZGxlTmV4dCIsInZhbGlkIiwiY2FtcG9zQ29tRXJybyIsImZpbHRlciIsImNhbXBvIiwicXVhbnRpZGFkZUVycm9zIiwibXV0YXRpb25GbiIsInBvc3QiLCJvblN1Y2Nlc3MiLCJuZXdDb250cmFjdElkIiwiaWQiLCJzdWJzdHJpbmciLCJzZXRUaW1lb3V0Iiwib25FcnJvciIsInRlcm0iLCJpbml0RGF0ZSIsImVuZERhdGEiLCJpbnZlc3REYXRlIiwiU3RyaW5nIiwic3RhcnREYXRlIiwiZm9ybWF0dGVkRW5kRGF0ZSIsInNob3VsZFZhbGlkYXRlIiwiZGV0YWlscyIsImNhbGN1bGF0ZUlSIiwiZGF5cyIsInRvdGFsUmV0dXJuIiwiaXJSYXRlIiwiaXJWYWx1ZSIsIm1haW5BbW91bnQiLCJtYWluUmF0ZSIsIm1haW5TdGFydERhdGUiLCJtYWluRW5kRGF0ZSIsImNvbnRyYWN0RW5kIiwibWFpbkRheXMiLCJkaWZmIiwibWFpbklSIiwidHlwZSIsImRheXNSZW50YWJpbGl6ZWQiLCJzdGF0dXMiLCJhY3RpdmVBZGRlbmR1bUluZGV4IiwiZm9yRWFjaCIsImFkZEFtb3VudCIsImFkZFJhdGUiLCJhZGRTdGFydERhdGUiLCJhZGRFbmREYXRlIiwiYWRkRGF5cyIsImFkZElSIiwidG90YWxJUiIsInJlZHVjZSIsInN1bSIsImRldGFpbCIsInZhbG9yQXBvc0Rlc2NvbnRvIiwicmVzdG8iLCJjb3Rhc0NvbXBsZXRhcyIsInZhbG9yQ290YXNDb21wbGV0YXMiLCJ2YWxvckNvbXBsZW1lbnRhck5lY2Vzc2FyaW8iLCJub3ZvVmFsb3JJbnZlc3RpbWVudG8iLCJ2YWxvckZvcm1hdGFkbyIsIndhcm5pbmciLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJjdXJyZW5jeSIsInBhcnNlVmFsb3IiLCJ2YWxvckludmVzdGlkbyIsImFsaXF1b3RhTnVtYmVyIiwidmFsb3JUb3RhbElSIiwicmVuZGVyU3RlcDEiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvcHRpb24iLCJ3aWR0aCIsImVycm9yTWVzc2FnZSIsIm1lc3NhZ2UiLCJsYWJlbCIsIm1heExlbmd0aCIsInBsYWNlaG9sZGVyIiwidGV4dGFyZWEiLCJzcGFuIiwic2l6ZSIsInJlbmRlclN0ZXAyIiwiZm9ybSIsIm5ld01vZGFsaXR5Iiwic3Ryb25nIiwibWFza2VkVmFsdWUiLCJ0b0xvY2FsZVN0cmluZyIsImg0IiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsInRkIiwiY29sU3BhbiIsIm1hcCIsImluZGV4IiwiaW5wdXQiLCJjaGVja2VkIiwiaW5mbyIsInZhbG9yQXR1YWwiLCJ2YWxvcklSVGFiZWxhIiwidmFsb3JDb21EZXNjb250byIsInZhbG9yRW1DZW50YXZvcyIsInJvdW5kIiwiYnIiLCJkaXNhYmxlZCIsIm1heERhdGUiLCJ2YXJpYW50IiwiaXNQZW5kaW5nIiwiaDEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contratos/alterar/page.tsx\n"));

/***/ })

});